export const columnWidthDefaultMap = {
    INTEGER: 132,
    LONG: 132,
    BIG_INTEGER: 132,
    DOUBLE: 132,
    STRING: 124,
    ENUM: 112
};

// 地域筛选
export const regionFilterType = ['provinceName', 'provinceCityName'];
export const regionFilterBackendMap = {
    provinceName: 'provinceId',
    provinceCityName: 'cityId'
};

// 多选框筛选 目前有 小时
export const checkboxFilterType = ['hour'];

// 表格滚动
export const defaultBottomScroll = {bottom: 0};

export const defaultScroll = {x: true};

// 总共3分钟
export const maxTick = 3 * 30;
export const tickTime = 2000;

// 特殊的模版类型（组件里用）
export const templateKey = {
    default: 'default', // 默认推荐
    custom: 'custom' // 自定义（临时选择）
};
// 接口对于特殊模版类型的值
export const templateValue = {
    default: -1, // 默认推荐
    custom: 0 // 自定义（临时选择）
};
export const templateMap = new Map([
    [templateValue.default, templateKey.default],
    [templateValue.custom, templateKey.custom]
]);

// 自定义列模版个数限制
export const templateCountLimit = 50;
// 自定义列模版名称字符限制
export const templateNameLimit = 20;
