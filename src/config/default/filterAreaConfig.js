import {getRegionDatasource} from '../../utils/filterArea';
import {datePickShortCuts} from './shortCuts';
import {dateSelectShortCuts} from './dateSelectShortCuts';

export const allItemValue = 'all';
export const itemValue = 'item';

const feedIdeaStyleFilterData = {
    name: '创意样式',
    field: 'feedMaterialStyleEnum',
    defaultValue: '0',
    isInFilterCondition: true,
    option: [
        {
            label: '全部',
            value: '0'
        },
        {
            label: '单图',
            value: '1'
        },
        {
            label: '三图',
            value: '2'
        },
        {
            label: '大图',
            value: '3'
        },
        {
            label: '视频',
            value: '4'
        },
        {
            label: '橱窗',
            value: '5'
        },
        {
            label: '开屏',
            value: '6'
        },
        {
            label: '横幅',
            value: '7'
        },
        {
            label: '横版视频',
            value: '8'
        },
        {
            label: '竖版视频',
            value: '9'
        },
        {
            label: '互动图',
            value: '10'
        }
    ]
};

const regionDataSource = getRegionDatasource();

export const filterAreaConfig = {
    datePickerMinValue: '2016/07/18',
    maxSelecteMccdNum: 10,
    isShowGlobalDateSwitch: false,
    isGlobalDateChecked: false,
    isDateSelector: false,
    filterList: [],
    moreList: [],
    datePickShortCuts,
    dateSelectShortCuts,
    defaultDateSelectorValue: 1,
    filterData: {
        timeUnit: {
            isInFilterCondition: false,
            name: '时间单位',
            field: 'timeUnit',
            defaultValue: 'SUMMARY',
            width: 200,
            notSupportCompareUnits: ['HOUR', 'WEEK', 'MONTH'],
            option: [
                {
                    label: '合计',
                    value: 'SUMMARY'
                },
                {
                    label: '分时',
                    value: 'HOUR'
                },
                {
                    label: '分日',
                    value: 'DAY'
                },
                {
                    label: '分周',
                    value: 'WEEK'
                },
                {
                    label: '分月',
                    value: 'MONTH'
                }
            ]
        },
        device: {
            name: '推广设备',
            field: 'device',
            width: 200,
            defaultValue: allItemValue,
            isInFilterCondition: true,
            option: [
                {
                    label: '全部',
                    value: allItemValue,
                    realValue: ''
                },
                {
                    label: '计算机',
                    value: '0'
                },
                {
                    label: '移动',
                    value: '1'
                }
            ]
        },
        subAppId: {
            name: '内容分类',
            field: 'subAppId',
            width: 200,
            defaultValue: '5',
            isInFilterCondition: true,
            option: [
                {
                    label: '商品中心',
                    value: '0'
                },
                {
                    label: '基木鱼内容中心-产品',
                    value: '5'
                }
            ]
        },
        campaignType: {
            name: '计划类型',
            field: 'campaignType',
            width: 200,
            defaultValue: allItemValue,
            isInFilterCondition: true,
            option: [
                {
                    label: '全部',
                    value: allItemValue,
                    realValue: ''
                },
                {
                    label: '商品',
                    value: '1'
                },
                {
                    label: '直播间',
                    value: '2'
                }
            ]
        },
        storeType: {
            name: '店铺类型',
            field: 'storeType',
            width: 200,
            defaultValue: allItemValue,
            isInFilterCondition: true,
            option: [
                {
                    label: '全部',
                    value: allItemValue,
                    realValue: ''
                },
                {
                    label: '度小店',
                    value: '1'
                },
                {
                    label: '健康商城',
                    value: '4'
                },
                {
                    label: '百度小程序',
                    value: '8'
                }
            ]
        },
        feedSubjectEnum: {
            name: '营销目标',
            field: 'feedSubjectEnum',
            width: 200,
            defaultValue: allItemValue,
            isInFilterCondition: true,
            option: [
                {
                    label: '全部',
                    value: allItemValue,
                    realValue: ''
                },
                {
                    label: '网站链接',
                    value: '1'
                },
                {
                    label: '应用推广（iOS）',
                    value: '2'
                },
                {
                    label: '应用推广（Android）',
                    value: '3'
                },
                {
                    label: '小程序',
                    value: '4'
                },
                {
                    label: '商品目录',
                    value: '5'
                },
                {
                    label: '门店推广',
                    value: '6'
                },
                {
                    label: '电商店铺',
                    value: '7'
                }
            ]
        },
        feedMaterialStyleEnum: {
            name: '生成样式',
            field: 'feedMaterialStyleEnum',
            width: 200,
            defaultValue: allItemValue,
            isInFilterCondition: true,
            option: [
                {
                    label: '全部',
                    value: allItemValue,
                    realValue: ''
                },
                {
                    label: '程序化',
                    value: '100'
                },
                {
                    label: '自定义',
                    value: '101',
                    isIgnoreValue: true,
                    children: feedIdeaStyleFilterData
                }
            ]
        },
        ocpcPayMode: {
            name: '付费模式',
            field: 'ocpcPayMode',
            width: 200,
            defaultValue: allItemValue,
            isInFilterCondition: true,
            option: [
                {
                    label: '全部',
                    value: allItemValue,
                    realValue: ['0', '1', '2']
                },
                {
                    label: 'oCPM',
                    value: '2'
                },
                {
                    label: 'oCPC',
                    value: itemValue,
                    realValue: ['0', '1']
                }
            ]
        },
        bidType: {
            name: '出价模式',
            field: 'bidType',
            width: 200,
            defaultValue: allItemValue,
            isInFilterCondition: true,
            option: [
                {
                    label: '全部',
                    value: allItemValue,
                    realValue: ['3', '5']
                },
                {
                    label: '转化出价',
                    value: '3'
                },
                {
                    label: '点击出价',
                    value: '5'
                }
            ]
        },
        targetingType: {
            name: '购买方式',
            field: 'targetingType',
            option: [
                {
                    label: '关键词',
                    value: '0'
                },
                {
                    label: '行业定投',
                    value: '6'
                },
                {
                    label: '页面投放',
                    value: '7'
                }
            ]
        },
        queryStatus: {
            name: '账户添加状态',
            field: 'queryStatus',
            option: [
                {
                    label: '已添加',
                    value: '0'
                },
                {
                    label: '未添加',
                    value: '1'
                }
            ]
        },
        provinceId: {
            name: '地域',
            field: 'provinceId',
            option: regionDataSource
        },
        feedIdeaStyleFilterData,
        secondComponentType: {
            name: '组件类型',
            field: 'secondComponentType',
            width: 200,
            isInFilterCondition: true,
            option: [
                {
                    label: '图片类',
                    value: 101,
                    children: [
                        {
                            label: '图集',
                            value: 10101
                        },
                        {
                            label: '大图',
                            value: 10102
                        },
                        // {
                        //     label: '大图',
                        //     value: 10103
                        // },
                        {
                            label: '合成大图',
                            value: 10104
                        },
                        {
                            label: '图文',
                            value: 10105
                        }
                    ]
                },
                {
                    label: '线索类',
                    value: 102,
                    children: [
                        {
                            label: '咨询',
                            value: 10201
                        },
                        {
                            label: '电话',
                            value: 10202
                        }
                    ]
                },
                {
                    label: '子链类',
                    value: 103,
                    children: [
                        {
                            label: '短子链',
                            value: 10301
                        },
                        {
                            label: '长子链',
                            value: 10302
                        }
                    ]
                },
                {
                    label: '文本类',
                    value: 104,
                    children: [
                        {
                            label: '文本描述',
                            value: 10401
                        },
                        {
                            label: '知识问答',
                            value: 10402
                        }
                    ]
                },
                {
                    label: '视频类',
                    value: 105,
                    children: [
                        {
                            label: '多视频',
                            value: 10501
                        },
                        {
                            label: '单视频',
                            value: 10502
                        },
                        {
                            label: '视频大图',
                            value: 10503
                        },
                        {
                            label: '视频三图',
                            value: 10504
                        },
                        {
                            label: '视频单图',
                            value: 10505
                        },
                        {
                            label: '多视频横滑',
                            value: 10506
                        },
                        {
                            label: '视频子链列表',
                            value: 10507
                        },
                        {
                            label: '视频预览',
                            value: 10508
                        }
                    ]
                },
                {
                    label: '下载类',
                    value: 106,
                    children: [
                        {
                            label: '下载',
                            value: 10601
                        }
                    ]
                },
                {
                    label: '列表类',
                    value: 107,
                    children: [
                        {
                            label: '图文列表',
                            value: 10701
                        },
                        {
                            label: '文本列表',
                            value: 10702
                        }
                    ]
                }
            ]
        },
        product: {
            name: '投放渠道',
            field: 'product',
            width: 200,
            defaultValue: allItemValue,
            isInFilterCondition: true,
            option: [
                {
                    label: '全部',
                    value: allItemValue,
                    realValue: ['0', '1']
                },
                {
                    label: '搜索推广',
                    value: '0'
                },
                {
                    label: '信息流推广',
                    value: '1'
                }
            ]
        }
    }
};
