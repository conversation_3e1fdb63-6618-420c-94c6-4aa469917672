import {getQLeftDay, getYearLeftDay} from '../../utils/filterArea';

const today = new Date();
// 今年过去了几月
const month = today.getMonth();
// 当前季度过去了几月
const qLeftMonth = month % 3;
const year = today.getFullYear();
const qLeftDay = getQLeftDay(year, month, today);
const yearLeftDay = getYearLeftDay(year, today);

export const datePickShortCuts = [
    {
        label: '今天',
        to: 0,
        from: 0
    },
    {
        label: '昨天',
        to: -1,
        from: -1
    },
    {
        label: '最近7天',
        to: -1,
        from: -7
    },
    {
        label: '最近14天',
        to: -1,
        from: -14
    },
    {
        label: '最近30天',
        to: -1,
        from: -30
    },
    {
        label: '上个月',
        from: {
            startOf: 'month',
            months: -1
        },
        to: {
            startOf: 'month',
            days: -1
        }
    },
    {
        label: '本月',
        // 本月第一天
        from: {
            startOf: 'month'
        },
        // 今天
        to: 0
    },
    {
        label: '本周',
        // 本周第一天，days 为 0 是可以省略的
        from: {
            startOf: 'week',
            days: 0
        },
        // 今天
        to: 0
    },
    {
        label: '上周',
        // 本周第一天，days 为 0 是可以省略的
        from: {
            startOf: 'week',
            weeks: -1
        },
        // 今天
        to: {
            startOf: 'week',
            days: -1
        }
    },
    {
        label: '本季度',
        // 本季度第一个月
        from: {
            startOf: 'month',
            months: -qLeftMonth
        },
        // 今天
        to: 0
    },
    {
        label: '上季度',
        // 上季度第一个月
        from: {
            startOf: 'month',
            months: -(qLeftMonth + 3)
        },
        // 本季度前一天
        to: -qLeftDay
    },
    {
        label: '本年',
        // 本年第一个月
        from: {
            startOf: 'month',
            months: -month
        },
        // 今天
        to: 0
    },
    {
        label: '上一年',
        // 前一年第一个月
        from: {
            startOf: 'month',
            months: -(month + 12)
        },
        // 今年前一天
        to: -yearLeftDay
    }
];
