export const numberFilterMap = {
    GT: '>',
    GTE: '≥',
    LT: '<',
    LTE: '≤',
    EQ: '=',
    NOT_EQ: '≠'
};

export const stringFilterMap = {
    LIKE: '(模糊)包含',
    EQ: '等于',
    NOT_EQ: '不等于',
    NOT_LIKE: '(模糊)排除',
    IN: '(精确)包含',
    NOT_IN: '(精确)排除'
};

export const stringSingleFilters = ['LIKE', 'NOT_LIKE', 'EQ', 'NOT_EQ'];

export const enumFilterMap = {
    IN: '包含'
};

export const customFilterMap = {
    IN: '',
    MATCH_ANY: '包括'
};

export const numberTypes = ['INTEGER', 'LONG', 'BIG_INTEGER', 'DOUBLE'];

export const stringFilterMaxLine = 10;

export const stringFilterMaxLength = 50;

export const defaultFilterOperatorMap = {
    INTEGER: 'GT',
    LONG: 'GT',
    BIG_INTEGER: 'GT',
    DOUBLE: 'GT',
    STRING: 'LIKE',
    ENUM: 'IN',
    BOOLEAN: 'IN',
    CUSTOM: 'IN'
};

export default {
    numberFilterMap,
    stringFilterMap,
    enumFilterMap,
    customFilterMap,
    numberTypes,
    defaultFilterOperatorMap,
    stringSingleFilters,
    stringFilterMaxLine,
    stringFilterMaxLength
};

const hourFilterMap = [
    '0:00 ~ 1:00',
    '1:00 ~ 2:00',
    '2:00 ~ 3:00',
    '3:00 ~ 4:00',
    '4:00 ~ 5:00',
    '5:00 ~ 6:00',
    '6:00 ~ 7:00',
    '7:00 ~ 8:00',
    '8:00 ~ 9:00',
    '9:00 ~ 10:00',
    '10:00 ~ 11:00',
    '11:00 ~ 12:00',
    '12:00 ~ 13:00',
    '13:00 ~ 14:00',
    '14:00 ~ 15:00',
    '15:00 ~ 16:00',
    '16:00 ~ 17:00',
    '17:00 ~ 18:00',
    '18:00 ~ 19:00',
    '19:00 ~ 20:00',
    '20:00 ~ 21:00',
    '21:00 ~ 22:00',
    '22:00 ~ 23:00',
    '23:00 ~ 24:00'
];

// 多选框筛选配置
export const checkboxFilterMapConfig = {
    hour: hourFilterMap.map((item, index) => ({label: item, value: index}))
};