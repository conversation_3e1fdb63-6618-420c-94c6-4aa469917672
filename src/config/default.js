import filterConfig from './default/filter';
import {columnWidthDefaultMap} from './default/table';
import {commonClassPrefix} from './common';
import {filterAreaConfig} from './default/filterAreaConfig';

export default {
    pageTitle: '数据报告',
    datePlacement: 'withTitle',
    api: {},
    classPrefix: commonClassPrefix,
    maxEmails: 5,
    columnWidthDefaultMap,
    filter: filterConfig,
    isNeedCompare: true,
    isNeedMcc: true,
    pagination: {
        pageSize: 20,
        pageSizes: ['20', '50', '100']
    },
    cellRenderConfig: {
        renders: {},
        contentFormats: {}
    },
    filterAreaConfig
};
