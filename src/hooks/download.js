import {useRequestCallback} from '@huse/request';
import {useEffect, useCallback, useRef} from 'react';
import {TIME_OUT, INTERVAL} from '../config/download';
import {downloadWithToast} from '../utils/download';

/**
 *
 * @param {*} task_
 * @param {*} params
 * @param {*} param2
 */

// 具体下载过程是一个“可取消”的异步任务，把触发、状态更新和清除副作用 封装在一起
export function useCancelableAsyncTaskCallback(task_, params, {timeout, interval}) {
    const controllerRef = useRef();
    const task = useCallback(function task(params) {
        if (controllerRef.current) {
            controllerRef.current.abort();
        }
        const controller = new AbortController();
        controllerRef.current = controller;
        return task_(params, {signal: controller.signal, timeout, interval});
    }, [task_]);
    const cancel = useCallback(function () {
        const controller = controllerRef.current;
        controller && controller.abort();
    }, []);
    const [callback, result] = useRequestCallback(task, params);
    useEffect(() => cancel, []);
    return [callback, {...result, cancel}];
}

// eslint-disable-next-line max-len
export const useCancelableAsyncDownloadTask = (params, {timeout = TIME_OUT, interval = INTERVAL} = {}) => useCancelableAsyncTaskCallback(downloadWithToast, params, {timeout, interval});