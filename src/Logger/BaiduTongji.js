/**
 * @file 百度统计日志器
 * <AUTHOR>
 */
import {commonClassPrefix as hmjsScriptID} from '../config/common';

/* eslint-disable */
export default class {
    constructor(key) {
        if (!key) {
            throw new Error('Baidu<PERSON>ongji key is required');
        }
        this.key = key;

        // 安装百度统计脚本，要求每个 key 都要装对应的 js。
        // _hmt.id 最终用 先执行的 hm.js 的 key ，所以要保证业务端的 hm.js 先加载完成
        // 或者更保险的方法，业务端事先往 _hmt 里 push 一个 setAccount，保证业务端的 key 一定在前面
        // 免得默认 key 是创意SDk 的，导致 SPA统计有问题
        window._hmt = window._hmt || [];

        if (document.getElementById(hmjsScriptID)) {
            // 避免多次实例化时重复创建
            return;
        }
        let hm = document.createElement('script');
        hm.id = hmjsScriptID;
        hm.src = `https://hm.baidu.com/hm.js?${encodeURIComponent(key)}`;
        let s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(hm, s);
    }

    send(hitType, fields, extra = {}) {
        const keysToOmitOfExtra = ['isIframe', 'hostOrigin', 'page'];
        let eventExtra = Object.keys(extra).reduce(function (ret, key) {
            if (keysToOmitOfExtra.indexOf(key) < 0) {
                ret[key] = extra[key];
            }
            return ret;
        }, {
            env: extra.isIframe ? 'iframe' : 'normal',
        });

        if (extra.page) {
            let i = extra.page.indexOf('?');
            if (i > 0) {
                eventExtra.page = extra.page.substring(0, i);
            }
        }
        if (extra.isIframe) {
            eventExtra.hostOrigin = extra.hostOrigin;
        }

        if (hitType === 'pageview') {
            this.sendCustomEvent('pv', {value: fields, ...eventExtra});
        }
        else if (hitType === 'event') {
            const {category, label, action, value} = fields;
            this.sendCustomEvent(action, {category, label, value, ...eventExtra});
        }
    }

    sendCustomEvent(...args) {
        this.sendBaiduTongji('_trackCustomEvent', ...args);
    }

    sendBaiduTongji(...args) {
        if (!window._hmt) {
            return;
        }
        let previousHmtID = _hmt.id;
        let userid = args[2].userid;
        _hmt.push(['_setAccount', this.key]);
        // PM要求 SDK 以 userid 区分 uid（若不传百度统计会以设备区分）
        // setAccount 后，_hmt 的所有操作都仅针对当前的 account，不会影响到其他 account
        if (userid) {
            _hmt.push(['_setUserId', userid]);
        }
        _hmt.push(args);

        // 恢复原来的 key
        _hmt.push(['_setAccount', previousHmtID]);
    }
}


