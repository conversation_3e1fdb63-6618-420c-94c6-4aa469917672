import post from '../common/reportSdk/api';
import {getApiToast} from '../../utils/common';

const GET_TABLE_META_DATA_URL = 'marsPro/GET/ReportConfigService/getReportConfig';
const GET_TABLE_DATA_URL = 'marsPro/GET/ReportDataService/getReportData';
const GET_COMPARE_TABLE_DATA_URL = 'marsPro/GET/ReportDataService/getCompareReportData';

const defaultConf = {
    paramsBuilder(params) {
        return params;
    },
    responseTransfer(res) {
        return res;
    },
    errorHandler(path, error) {
        const {eventHub: {fire}} = this.context;
        fire('sdk.event.enter.log', {
            hitType: 'request-error',
            fields: {
                path,
                ...error
            }
        });
        throw error;
    }
};
const uploadConf = {
    ...defaultConf,
    paramsBuilder(params) {
        const {token} = this.context;
        return {token, ...params};
    }
};

export default class TableService {
    constructor(context) {
        const {hairuo: {userId, optId, token}} = context;
        const postParams = {
            token,
            userId,
            optId,
            extraConfig: {
                onAPIFailure: (requestOptions, error) => {
                    error.reqId = requestOptions.reqId;
                }
            }
        };
        this.post = post(postParams);
        this.context = context;
    }

    /**
     * 公共请求发送方法
     *
     * @param {Object} conf 请求conf
     * @param {Object} rawParams 原始请求参数
     * @param {string} url 请求路径
     * @return {Promise} 请求 promise
     */
    postService(conf, rawParams, url) {
        const {paramsBuilder, responseTransfer, errorHandler} = conf;
        const params = paramsBuilder.call(this, rawParams);
        return this
            .post(url, params)
            .then(
                res => responseTransfer(res)
            )
            .catch(
                err => {
                    if (err?.errors?.length && err?.errors?.[0]?.code === '12345678') {
                        getApiToast({content: '当前账户数据查询权限已受限，请联系百度侧接口人解除限制'});
                        return;
                    }
                    return errorHandler.call(this, url, err);
                }
            );
    }

    // 获取表格元数据
    getTableMetaData({params, url = GET_TABLE_META_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 获取表格数据
    getTableData({params, url = GET_TABLE_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 获取对比表格数据
    getCompareTableData({params, url = GET_COMPARE_TABLE_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }

}
