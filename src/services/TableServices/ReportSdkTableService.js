/**
 * @file 报表SDK apiService
 * <AUTHOR>
 */

import post from '../common/reportSdk/api';

const GET_TABLE_META_DATA_URL = 'marsPro/GET/ReportConfigService/getReportConfig';
export const GET_TABLE_DATA_URL = 'marsPro/GET/ReportDataService/getReportData';
export const GET_COMPARE_TABLE_DATA_URL = 'marsPro/GET/ReportDataService/getCompareReportData';
const ADD_CUSTOM_COLUMNS_URL = 'marsPro/GET/ReportConfigService/saveCustomColumns';
const DOWNLOAD_TABLE_DATA_URL = 'marsPro/GET/ReportDataService/getReportFile';
const GET_PERSONAL_SET_UP_URL = 'marsPro/GET/PersonalSetup/getPersonalSetup';
const SAVE_PERSONAL_SET_UP_URL = 'marsPro/GET/PersonalSetup/savePersonalSetup';
const EMAIL_TABLE_DATA_URL = 'marsPro/GET/AsyncReportDataService/createReportTask';
const GET_MCC_INFO_URL = 'marsPro/GET/MccUserService/getMccUserList';
export const GET_SPLIT_TABLE_DATA_URL = 'marsPro/GET/ReportDataService/getSplitReportData';
const CREATE_REPORT_TASK = 'marsPro/GET/AsyncReportDataService/createReportTask';
const GET_TASK_STATUS = 'marsPro/GET/AsyncReportDataService/getTaskStatus';
const GET_COMOMNKEY_CONTENT = 'hongyan/GET/HelpInfoService/getHelpInformation';
const GET_TEMPLATE_URL = 'marsPro/GET/CustomColumnTemplateService/getTemplates';
const SAVE_TEMPLATE_URL = 'marsPro/GET/CustomColumnTemplateService/saveTemplate';
const MARK_AS_CURRENT_TEMPLATE_URL = 'marsPro/GET/CustomColumnTemplateService/markAsCurrentTemplate';
const DELETE_TEMPLATE_URL = 'marsPro/GET/CustomColumnTemplateService/deleteTemplates';
const GET_HISTORY_URL = 'aigc-boost/GET/ConversationService/getHistory';
const GET_RANDOM_QUESTION_URL = 'aigc-boost/GET/InstructService/randomQuestion';

const defaultConf = {
    paramsBuilder(params) {
        return params;
    },
    responseTransfer(res) {
        return res;
    },
    errorHandler(path, error) {
        const {eventHub: {fire}} = this.context;
        fire('sdk.event.enter.log', {
            hitType: 'request-error',
            fields: {
                path,
                ...error
            }
        });
        throw error;
    }
};
const uploadConf = {
    ...defaultConf,
    paramsBuilder(params) {
        const {token} = this.context;
        return {token, ...params};
    }
};

export default class TableService {
    constructor(context) {
        const {hairuo: {userId, optId, token}} = context;
        const postParams = {
            token,
            userId,
            optId,
            extraConfig: {
                onAPIFailure: (requestOptions, error) => {
                    error.reqId = requestOptions.reqId;
                }
            }
        };
        this.post = post(postParams);
        this.context = context;
    }

    /**
     * 公共请求发送方法
     *
     * @param {Object} conf 请求conf
     * @param {Object} rawParams 原始请求参数
     * @param {string} url 请求路径
     * @return {Promise} 请求 promise
     */
    postService(conf, rawParams, url) {
        const {paramsBuilder, responseTransfer, errorHandler} = conf;
        const params = paramsBuilder.call(this, rawParams);
        return this
            .post(url, params)
            .then(
                res => responseTransfer(res)
            )
            .catch(
                err => errorHandler.call(this, url, err)
            );
    }

    // 以下为接口方法
    // 获取表格元数据
    getTableMetaData({params, url = GET_TABLE_META_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 获取自定义列模版列表
    getTemplates({params, url = GET_TEMPLATE_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 保存自定义列模版
    saveTemplate({params, url = SAVE_TEMPLATE_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 保存为当前模版
    markAsCurrentTemplate({params, url = MARK_AS_CURRENT_TEMPLATE_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 删除模版
    deleteTemplate({params, url = DELETE_TEMPLATE_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 获取表格数据
    getTableData({params, url = GET_TABLE_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 获取对比表格数据
    getCompareTableData({params, url = GET_COMPARE_TABLE_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 保存自定义列
    saveCustomColumns({params, url = ADD_CUSTOM_COLUMNS_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 下载表格数据
    downloadTableData({params, url = DOWNLOAD_TABLE_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 获取下载设置的信息
    getPersonalDownloadSetup({params, url = GET_PERSONAL_SET_UP_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 修改下载设置的信息
    modPersonalDownloadSetup({params, url = SAVE_PERSONAL_SET_UP_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 发送邮箱
    emailTableData({params, url = EMAIL_TABLE_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 获取多账户
    getMccInfo({params, url = GET_MCC_INFO_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 获取细分表格数据
    getSplitTableData({params, url = GET_SPLIT_TABLE_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 创建异步任务
    createTask({params, url = CREATE_REPORT_TASK}) {
        return this.postService(uploadConf, params, url);
    }

    // 获取异步任务状态
    getTaskStatus({params, url = GET_TASK_STATUS}) {
        return this.postService(uploadConf, params, url);
    }

    // 获取报告释义提示文案
    getCommonKeyContent({params, url = GET_COMOMNKEY_CONTENT}) {
        return this.postService(uploadConf, params, url);
    }

    // 查看历史
    getHistory({params, url = GET_HISTORY_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 猜你喜欢
    getRandomQuestion({params, url = GET_RANDOM_QUESTION_URL}) {
        return this.postService(uploadConf, params, url);
    }
}
