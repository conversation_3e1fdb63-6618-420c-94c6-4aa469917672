/**
 * @file 报表SDK apiService
 * <AUTHOR>
 */

import post from '../common/reportSdk/api';

const GET_CUSTOM_LIST_URL = 'marsPro/GET/CustomReportService/getCustomReportList';
const RENEWAL_CUSTOM = 'marsPro/GET/CustomReportService/renewal';
const RENEWAL_CUSTOM_LIST = 'marsPro/GET/CustomReportService/deleteCustomReportList';
const DEL_CUSTOM = 'marsPro/GET/CustomReportService/deleteCustomReport';
const GET_CUSTOM_RESULT = 'marsPro/GET/CustomReportService/getCustomReportResultByExecutionId';
const GET_CUSTOM_REPORT = 'marsPro/GET/CustomReportService/getCustomReport';
const GET_TABLE_META_DATA_URL = 'marsPro/GET/ReportConfigService/getReportConfig';
const GET_MCC_INFO_URL = 'marsPro/GET/MccUserService/getMccUserList';
const SAVE_CUSTOM_REPORT = 'marsPro/GET/CustomReportService/saveCustomReport';

const defaultConf = {
    paramsBuilder(params) {
        return params;
    },
    responseTransfer(res) {
        return res;
    },
    errorHandler(path, error) {
        const {eventHub: {fire}} = this.context;
        fire('sdk.event.enter.log', {
            hitType: 'request-error',
            fields: {
                path,
                ...error
            }
        });
        throw error;
    }
};
const uploadConf = {
    ...defaultConf,
    paramsBuilder(params) {
        return params;
    }
};

export default class TableService {
    constructor(context) {
        const {hairuo: {userId, optId, token}} = context;
        const postParams = {
            token,
            userId,
            optId,
            extraConfig: {
                onAPIFailure: (requestOptions, error) => {
                    error.reqId = requestOptions.reqId;
                }
            }
        };
        this.post = post(postParams);
        this.context = context;
    }

    /**
     * 公共请求发送方法
     *
     * @param {Object} conf 请求conf
     * @param {Object} rawParams 原始请求参数
     * @param {string} url 请求路径
     * @return {Promise} 请求 promise
     */
    postService(conf, rawParams, url) {
        const {paramsBuilder, responseTransfer, errorHandler} = conf;
        const params = paramsBuilder.call(this, rawParams);
        return this
            .post(url, params)
            .then(
                res => responseTransfer(res)
            )
            .catch(
                err => errorHandler.call(this, url, err)
            );
    }

    // 以下为接口方法
    // 获取表格数据
    getCustomList({params, url = GET_CUSTOM_LIST_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 续期
    renewalCustomReport({params, url = RENEWAL_CUSTOM}) {
        return this.postService(uploadConf, params, url);
    }

    // 删除
    delCustomReport({params, url = DEL_CUSTOM}) {
        return this.postService(uploadConf, params, url);
    }

    // 批量删除
    delCustomReportList({params, url = RENEWAL_CUSTOM_LIST}) {
        return this.postService(uploadConf, params, url);
    }

    // 获取报告结果
    getCustomReportResult({params, url = GET_CUSTOM_RESULT}) {
        return this.postService(uploadConf, params, url);
    }

    // 获得报告信息
    getCustomReport({params, url = GET_CUSTOM_REPORT}) {
        return this.postService(uploadConf, params, url);
    }
    // 获取多账户
    getMccInfo({params, url = GET_MCC_INFO_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 获取表格元数据
    getTableMetaData({params, url = GET_TABLE_META_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 保存报告
    saveCustomReport({params, url = SAVE_CUSTOM_REPORT}) {
        return this.postService(uploadConf, params, url);
    }
}
