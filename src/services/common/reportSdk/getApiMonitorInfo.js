/**
 * @file 收集请求相关的埋点信息 ,参考apiMonitorHooks 无store 版本
 * <AUTHOR>
 */

import guid from '@baidu/guid';

const requestPeriod = {
    success: 'success',
    fail: 'fail'
};
const SEND_LOG = 'send log';
const FETCH = 0;

function sendLog(type, requestOptions, responseOrError) {
    const endpoint = requestOptions.endpoint;
    const request = requestOptions;
    const fetchResponse = responseOrError;
    const reqId = requestOptions.reqId;
    const monitorInfo = requestOptions.monitorInfo || {};
    monitorInfo.reqid = reqId;
    // eslint-disable-next-line
    const {___time___: time, ...response} = fetchResponse;
    const {beginTime, endTime} = time || {};
    const {error, status} = response;

    return {
        [SEND_LOG]: {
            log: {
                source: monitorInfo.source,
                target: `fetch_${type}`,
                path: endpoint,
                beginTime,
                request,
                endTime,
                response: {error, status},
                responseStatus: response.error && response.error.httpStatus,
                spendTime: endTime - beginTime,
                eventId: monitorInfo.eventId || guid(),
                monitorInfo
            },
            typeValue: FETCH
        }
    };
}

export const getApiSuccessInfo = sendLog.bind(null, requestPeriod.success);

export const getApiFailureInfo = sendLog.bind(null, requestPeriod.fail);
