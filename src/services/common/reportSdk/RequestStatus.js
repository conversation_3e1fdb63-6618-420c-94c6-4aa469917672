/**
 * @file 请求的通用错误码定义
 * <AUTHOR> (<EMAIL>)
 * @date 2017/11/15.
 *
现在海若给前端返回的status值有0，1，2，3。分别代表以下含义：
0：成功  （对应puppet, thunder的 200）
1：部分成功 （对应puppet, thunder的 300）
2：失败（对应puppet, thunder的400/600/700）
3：系统异常（对应puppet, thunder的500；以及海若自身的异常）
 */
import Enum from 'enum';

export default new Enum({
    INITIALIZE: -1,
    /**
     * Ajax成功的标识 (对应thunder的200)
     */
    SUCCESS: 0,
    /**
     * 业务部分失败 (对应thunder的300)
     */
    PARTFAIL: 1,
    /**
     * 业务失败 (对应thunder的400, 500, 600)
     */
    FAIL: 2,
    /**
     * 没有权限
     */
    NOAUTH: 700,
    /**
     * 超时
     */
    TIMEOUT: 900,
    /**
     * Ajax成功了，但是后置处理数据抛出异常
     */
    CLIENT_SIDE_EXCEPTION: 910,
    /**
     * Ajax通讯发生了错误，这时需要去看httpStatus
     */
    REQUEST_ERROR: 920,
    /**
     * 返回的status没有被识别
     */
    UNRECOGNIZED_STATUS: 930,
    /**
     * Ajax成功了，需要前端重定向，后置处理数据按业务失败处理
     */
    REDIRECT_STATUS: 940
});
