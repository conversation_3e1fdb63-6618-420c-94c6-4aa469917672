/**
 * @file 按照凤巢接口方式封装的请求工具
 * <AUTHOR> (<EMAIL>)
 * @date 2017/11/15.
 */
import partial from 'lodash/partial';
import superagent from 'superagent';
import Status from './RequestStatus';

export function getRedirectUrl() {
    let redirectUrl = '/hairuo/main.do';
    const search = window.location.search.substring(1);

    if (search) {
        redirectUrl += `?${search}`;
    }

    return redirectUrl;
}

function isNoAuth({errors}) {
    return !!errors.find(({code}) => String(code) === `${Status.NOAUTH.value}`);
}

/* eslint-disable no-throw-literal */
// 处理http请求成功的情况。此时Ajax仍有可能是业务失败的。
const defaultOnSuccess = (response, {url, postData}) => {
    const body = response.body;
    function captureAPIException(status) {
        if (window.__Weirwood && window.__Weirwood.error && window.__Weirwood.error.captureAPIException) {
            window.__Weirwood.error.captureAPIException({
                url,
                method: 'post',
                params: postData,
                status,
                response,
                user: postData.userid
            });
        }
    }

    // 若拿到的response不是JSON，则是客户端解析出错
    if (typeof body !== 'object' || body.status == null) {
        captureAPIException(Status.CLIENT_SIDE_EXCEPTION.value);
        throw {
            status: Status.CLIENT_SIDE_EXCEPTION.value,
            error: {detail: {response: body}}
        };
    }
    // 若response指明要redirect，则前端redirect
    if (body.redirect === true) {
        window.location.href = body.redirecturl || getRedirectUrl();
        captureAPIException(Status.REDIRECT_STATUS.value);
        throw {
            status: body.status,
            error: {detail: {response: body}}
        };
    }
    // 若状态不是成功，此时是业务失败，原样返回
    if (body.status !== Status.SUCCESS.value) {
        if (isNoAuth(body)) {
            captureAPIException(Status.NOAUTH.value);
        } else {
            captureAPIException(body.status);
        }
        throw {...body};
    }
    // 成功
    return {...body};
};

const defaultOnFailure = response => {
    if (response.timeout) {
        throw {status: Status.TIMEOUT.value};
    }
    throw {
        status: Status.REQUEST_ERROR.value,
        error: {
            httpStatus: response.status,
            detail: {response: response.body}
        }
    };
};

/**
 * 符合凤巢Ajax规则的request方法。
 *
 * @param {string} url 请求路径
 * @param {Object} postData 发送数据
 * @param {?number} options.timeout 超时设置，默认为-1，即不设超时
 * @param {?string} options.accept 响应接收格式，默认为json
 * @param {?string} options.type 请求content-type，默认为form
 * @param {?Function} options.onSuccess 请求成功处理方法
 * @param {?Function} options.onFail 请求失败的处理方法
 * @return {Promise<Response>} 返回的promise
 */
export default function request(url, postData, options = {}) {
    const {
        timeout = null,
        accept = 'json',
        type = 'form',
        onSuccess = defaultOnSuccess,
        onFailure = defaultOnFailure,
        headers = {}
    } = options;
    const beginTime = +(new Date());
    const theRequest = superagent
        .post(url)
        .set(headers)
        .withCredentials()
        .accept(accept)
        .type(type)
        .send(postData);
    if (timeout !== null) {
        theRequest.timeout(timeout);
    }
    return theRequest
        .then(
            partial(onSuccess, partial.placeholder, {url, postData}),
            onFailure
        )
        .then(resp => {
            const endTime = +(new Date());
            return {
                ...resp,
                // eslint-disable-next-line
                ___time___: {beginTime, endTime}
            };
        }, error => {
            const endTime = +(new Date());
            throw {
                ...error,
                // eslint-disable-next-line
                ___time___: {beginTime, endTime}
            };
        });
}
