/**
 * @file api.js
 * <AUTHOR>
 */
import URL from 'url-parse';
import guid from '@baidu/guid';
import request from './request';

const NOT_HAIRUO_API = [
    'sirius/GET/material',
    'pluto/GET/updatetime'
];
const getFullUrl = (endpoint, reqId) => {
    let apiRoot = '/hairuo/request.ajax';
    // feed的计划，单元，创意请求接口，用于联调，正式发版需要注释
    if (NOT_HAIRUO_API.indexOf(endpoint) > -1) {
        apiRoot = '/nirvana/request.ajax';
    }
    const url = new URL(apiRoot);
    url.set('query', {
        path: endpoint,
        reqid: reqId
    });
    return url.toString();
};

const getRequestOptions = ({token, userId, optId, path, params}) => {
    const reqId = guid();
    const formData = {
        reqid: reqId,
        userid: userId,
        optid: optId,
        token,
        path,
        params: JSON.stringify(params || {})
    };
    const fullUrl = getFullUrl(path, reqId);
    const timeout = 30000;
    return {
        fullUrl,
        formData,
        timeout,
        params,
        reqId
    };
};

export default ({userId, optId, token: outerToken, extraConfig = {}}) => (path, params) => {
    const {
        headers,
        token,
        onAPISuccess,
        onAPIFailure
    } = extraConfig;
    const requestOptions = getRequestOptions({token: outerToken || token, userId, optId, path, params});
    const {
        fullUrl,
        formData,
        timeout
    } = requestOptions;
    return request(fullUrl, formData, {timeout, headers})
        .then(response => {
            onAPISuccess && onAPISuccess(requestOptions, response);
            return response;
        }, (error = {}) => {
            onAPIFailure && onAPIFailure(requestOptions, error);
            throw error;
        });
};
