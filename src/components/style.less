@import "./table/style.less";
@import "./chartArea/style.less";
@import "./filterArea/style.less";
@import "./indicatorSelector/style.less";
@import './datePicker/style.less';
@import './smartQueryArea/style.less';

.@{report-prefix-cls} {
    // padding: @dls-padding-unit * 6;
    // background-color: @dls-color-gray-1;
    .@{report-sdk-container} {
        position: relative;
        background: @dls-color-gray-0;
        border-radius: @dls-padding-unit * 1.5;
        padding: @dls-padding-unit * 6;
        padding-top: @dls-padding-unit * 3;
        &-title {
            &-area {
                height: @dls-padding-unit * 7;
                display: flex;
                align-items: center;
                margin-bottom: @dls-padding-unit * 4;
                &-title {
                    font-size: @dls-font-size-2;
                    margin-right: @dls-padding-unit * 3;
                }
                &-container-hidden {
                    display: none;
                }
            }
            &-tip {
                font-size: @dls-font-size-0;
                color: @dls-color-gray-6;
                z-index: 9;
            }
            &-box {
                position: absolute;
                right: @dls-padding-unit * 6;
                top: @dls-padding-unit * 5;
                height: @dls-padding-unit * 7;
                display: flex;
                align-items: center;
                &-common-indicator {
                    right: @dls-padding-unit * 40;
                }
            }
        }
        &-no-tab {
            padding-top: @dls-padding-unit * 6;
        }
        &-no-common-filter {
            padding: 0;
        }
        &-top-tab {
            .one-tabs-bar .one-tabs-nav-container {
                padding: 0;
            }
            &-hidden {
                display: none;
            }
        }
        &-hidden-chart-area {
            padding-top: 24px;
        }
        &-custom-dash {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
        }
        &-main-area-table-area {
            .one-tabs-bar .one-tabs-nav-container {
                padding: 0;
            }
        }
    }
}