import React from 'react';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import {dlsPaddingUnit, dlsFontSize0} from 'less-plugin-dls/variables';
import {Select, Tooltip} from '@baidu/one-ui';
import {getScrollWidth} from '../../utils/common';

const Option = Select.Option;

const selectWidth = parseInt(dlsPaddingUnit, 10) * 30;

const optionWithScrollCount = 8;

export default class IndicatorSelector extends React.Component {

    static contextTypes = {
        eventHub: PropTypes.object.isRequired
    }

    constructor(props, context) {
        super(props, context);
        const {metrics = [], defaultIndicator} = this.props;
        let indicator = defaultIndicator;
        let compareIndicator = 'notSelect';
        if (metrics.length === 1) {
            indicator = metrics[0];
        } else if (metrics.length === 2) {
            [indicator, compareIndicator] = metrics;
        }
        this.state = {
            indicator,
            compareIndicator
        };
    }

    onChange = value => {
        this.setState({indicator: value});
        this.handleMetricsChange([value, this.state.compareIndicator]);
    }

    onCompareChange = value => {
        this.setState({compareIndicator: value});
        this.handleMetricsChange([this.state.indicator, value]);
    }

    handleMetricsChange = metrics => {
        const {topTabKey, chartKey, isCommon = false} = this.props;
        const {eventHub: {trigger}} = this.context;
        trigger('sdk.event.chart.metrics.change', {data: {
            eventKey: `${isCommon ? 'common' : topTabKey}-${chartKey || 'common'}`,
            metrics: metrics.filter(item => item !== 'notSelect')
        }});
    }

    render() {
        const {
            className,
            indicatorList,
            isIndicatorCompare,
            isMulitTab,
            isFilterCheckButton,
            commonFilterInfo
        } = this.props;
        const {indicator, compareIndicator} = this.state;
        const selectBaseProps = {
            width: selectWidth,
            trigger: 'click',
            size: 'small'
        };
        const TooltipBaseProps = {
            placement: 'left',
            type: 'dark'
        };
        const displayIndicatorList = [...((typeof indicatorList === 'function')
            ? indicatorList(commonFilterInfo)
            : indicatorList.filter(v => v.display !== false))];
        if (isIndicatorCompare) {
            displayIndicatorList.unshift({
                label: '无',
                value: 'notSelect'
            });
        }
        const displayIndicatorListMap = displayIndicatorList.reduce((displayIndicatorListMap, item) => {
            return {...displayIndicatorListMap, [item.value]: item.label};
        }, {});
        // fix innerWidth = width - padding - scrollWidth
        const optionInnerWidth = selectBaseProps.width - (parseInt(dlsPaddingUnit, 10) * 8)
            - (displayIndicatorList.length > optionWithScrollCount ? getScrollWidth() : 0);
        return (<div
            className={classNames(`${className}-indicator-selector`, {
                [`${className}-indicator-selector-title`]: !isMulitTab,
                [`${className}-indicator-selector-check-btn`]: isFilterCheckButton
            })}
        >
            <Select
                {...selectBaseProps}
                onChange={this.onChange}
                value={indicator}
                customRenderTarget={() => displayIndicatorListMap[indicator]}
            >
                {
                    displayIndicatorList.map(
                        ({label, value}) => {
                            const indicatorLableWidth = label.length * parseInt(dlsFontSize0, 10);
                            const isShowIndicatorTooltip = indicatorLableWidth > optionInnerWidth;
                            const toolTipWidth = indicatorLableWidth + parseInt(dlsPaddingUnit, 10) * 2;
                            const selectWidth = selectBaseProps.width + parseInt(dlsPaddingUnit, 10) * 4;
                            const toolTipPlacement = isIndicatorCompare && (toolTipWidth < selectWidth)
                                ? 'right'
                                : 'left';
                            return (
                                <Option
                                    key={value}
                                    value={value}
                                    disabled={isIndicatorCompare ? (value === compareIndicator) : false}
                                >
                                    {
                                        isShowIndicatorTooltip
                                            ? <Tooltip
                                                {...TooltipBaseProps}
                                                placement={toolTipPlacement}
                                                key={value}
                                                title={label}
                                            >
                                                {label}
                                            </Tooltip>
                                            : <>{label}</>
                                    }
                                </Option>
                            );
                        }
                    )
                }
            </Select>
            {isIndicatorCompare && <div className={`${className}-indicator-selector-compare`}>
                <Select
                    {...selectBaseProps}
                    value={compareIndicator}
                    onChange={this.onCompareChange}
                    customRenderTarget={() => displayIndicatorListMap[compareIndicator]}
                >
                    {displayIndicatorList.map(
                        ({label, value}) => {
                            const compareLableWidth = label.length * parseInt(dlsFontSize0, 10);
                            const isShowCompareTooltip = compareLableWidth > optionInnerWidth;
                            return (
                                <Option
                                    key={value}
                                    value={value}
                                    disabled={value === indicator}
                                >
                                    {
                                        isShowCompareTooltip
                                            ? (<Tooltip {...TooltipBaseProps} key={value} title={label}>
                                                {label}
                                            </Tooltip>)
                                            : <>{label}</>
                                    }
                                </Option>
                            );
                        }
                    )}
                </Select>
            </div>}
        </div>);
    }
}