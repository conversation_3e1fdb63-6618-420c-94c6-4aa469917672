@import "../../default.less";

.@{report-prefix-cls}-smart-query-area-container {
    margin-bottom: 24px;

    .icon-sync {
        margin: 0 4px;
        position: relative;
        top: 1px;
    }

    .icon-more {
        display: inline-block;
        margin: 10px 0 0 90px;
    }

    .light-ai-slot.light-ai-slot-vertical {
        box-sizing: border-box;
    }
}
.radio-tag-groups-select-container {
    --light-ai-tag-bubble-border-radius: 0 10px 10px 10px;

    margin-top: 16px;
    padding: 0 14px;
    display: inline-flex;
    flex-direction: column;
    gap: 16px;

    .radio-tag-groups {
        display: list-item;

        &-name {
            word-break: keep-all;
            margin-right: 4px;
        }
    }

    .radio-tag-groups-container {
        display: flex;
        align-items: baseline;
    }

    .radio-tag-groups::marker {
        color: #a8b0bf;
    }

    .radio-tag-groups-tag-group-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }
}
