import React from 'react';
import {Tag} from '@baidu/light-ai-react';

export function RadioTagGroups(props) {
    const {options, context, mode} = props;

    return (
        <PureRadioTagGroupsCard
            mode={mode}
            context={context}
            options={options}
        />
    );
};
export function PureRadioTagGroupsCard({options, context, mode}) {

    const sendReport = ({value}) => {
        const {eventHub: {trigger}} = context;
        trigger('sdk.event.smartQuery.search', {
            question: value,
            mode
        });
    };

    return (
        <div className="radio-tag-groups-select-container">
            {
                (options || []).map(groupItem => {
                    return (
                        <div className="radio-tag-groups" key={groupItem.name}>
                            <div className="radio-tag-groups-container">
                                <div className="radio-tag-groups-name">{groupItem.name}：</div>
                                <Tag.Group className="radio-tag-groups-tag-group-container">
                                    {
                                        (groupItem?.optionItems || []).map(item => {
                                            const {text, value} = item;
                                            const tagProps = {
                                                onClick: () => sendReport(item),
                                                className: 'radio-select-item'
                                            };
                                            return (
                                                <Tag {...tagProps} key={value}>{text}</Tag>
                                            );
                                        })
                                    }
                                </Tag.Group>
                            </div>
                        </div>
                    );
                })
            }
        </div>
    );
}
