/**
 * @file sdk - 智能数据查询区域
 * <AUTHOR>
 * @date 2024/01/25
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import {Card, OmniInput} from '@baidu/light-ai-react';
import {Button} from '@baidu/one-ui';
import {IconSync, IconChevronRight, IconChevronUp} from 'dls-icons-react';
import ReportSdkTableService from '../../services/TableServices/ReportSdkTableService';
import {RadioTagGroups} from './components/RadioTagGroups';

/* eslint-disable react/prefer-stateless-function */
export default class SmartQueryArea extends PureComponent {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }
    static propTypes = {
        useInputQuery: PropTypes.bool.isRequired,
        source: PropTypes.number.isRequired,
        smartReportType: PropTypes.number.isRequired
    }
    static defaultProps = {
        useInputQuery: false,
        source: 0,
        smartReportType: 0
    }

    constructor(props) {
        super(props);
        this.state = {
            historyData: [],
            questionData: [],
            more: false
        };
    }

    componentDidMount() {
        const {config, token, hairuo} = this.context;
        this.service = new ReportSdkTableService({config, token, hairuo});
        this.handleGetSmartQueryAreaData();
    }

    handleGetSmartQueryAreaData = () => {
        const {smartReportType, source} = this.props;
        Promise.all([
            this.service.getRandomQuestion({
                params: {
                    reportType: smartReportType,
                    source
                }
            }),
            this.service.getHistory({
                params: {
                    reportType: smartReportType,
                    source
                }
            })
        ]).then(([questionRes, historyRes]) => {
            this.setState({
                questionData: questionRes.data.map(item => ({
                    name: '猜你想搜',
                    optionItems: item.infos.map(info => ({
                        text: info.text,
                        value: info.question || info.message
                    }))
                })),
                historyData: historyRes.data.map(item => ({
                    name: '查看历史',
                    optionItems: item.answer.map(ans => ({
                        text: ans.detail.content,
                        value: ans.detail.content
                    }))
                }))
            });
        });
    }

    handleGetRandomQuestion = () => {
        const {smartReportType, source} = this.props;
        this.service.getRandomQuestion({
            params: {
                reportType: smartReportType,
                source
            }
        }).then(res => {
            this.setState({
                questionData: res.data.map(item => ({
                    name: '猜你想搜',
                    optionItems: item.infos.map(info => ({
                        text: info.text,
                        value: info.question
                    }))
                }))
            });
        });
    }

    render() {
        const {useInputQuery} = this.props;
        const {config: {classPrefix}, eventHub: {trigger}} = this.context;
        const omniInputProps = {
            maxLength: 100,
            onEnterPress: e => {
                trigger('sdk.event.smartQuery.search', {
                    question: e.target.value,
                    mode: 'omniInput'
                });
            }
        };
        const buttonProps = {
            type: 'text-strong',
            onClick: () => this.handleGetRandomQuestion()
        };
        const moreButtonProps = {
            type: 'text-strong',
            onClick: () => this.setState({more: !this.state.more})
        };
        return (
            <Card className={`${classPrefix}-smart-query-area-container`} header="智能数据查询" variant="strong">
                {!!useInputQuery && <OmniInput {...omniInputProps} />}
                <p>
                    {
                        !!this.state.questionData?.[0]?.optionItems.length
                        && (
                            <>
                                <RadioTagGroups
                                    mode="question"
                                    options={this.state.questionData}
                                    context={this.context}
                                />
                                <Button {...buttonProps}>
                                    <span><IconSync className="icon-sync" />换一换</span>
                                </Button>
                            </>
                        )
                    }
                </p>
                <p>
                    {
                        !!this.state.historyData?.[0]?.optionItems.length
                        && (
                            <>
                                <RadioTagGroups
                                    mode="history"
                                    options={
                                        this.state.more
                                            ? this.state.historyData.map(({name, optionItems}) => ({name, optionItems}))
                                            : this.state.historyData.map(
                                                ({name, optionItems}) => ({name, optionItems: optionItems.slice(0, 5)})
                                            )
                                    }
                                    context={this.context}
                                />
                                <p>
                                    {
                                        !!(this.state.historyData[0].optionItems.length > 5)
                                            ? (
                                                <Button {...moreButtonProps}>
                                                    {
                                                        this.state.more
                                                            ? (
                                                                <span className="icon-more">
                                                                    收起<IconChevronUp className="icon-sync" />
                                                                </span>
                                                            )
                                                            : (
                                                                <span className="icon-more">
                                                                    +{this.state.historyData[0].optionItems.length - 5}
                                                                    更多<IconChevronRight className="icon-sync" />
                                                                </span>
                                                            )
                                                    }
                                                </Button>
                                            )
                                            : null
                                    }
                                </p>
                            </>
                        )
                    }
                </p>
            </Card>
        );
    }
}
