/**
 * @file sdk入口 - 组件
 * <AUTHOR>
 * @date 2020/06/23
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import ChartArea from './chartArea/';
import ReportTable from './table';
import Indicator from './table/indicator';

export default class ReportMainArea extends PureComponent {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }

    constructor(props, context) {
        super(props, context);
        this.eventHub = context.eventHub;
        this.state = {
            chartAreaVisible: true,
            tableLoading: false
        };
    }
    static propTypes = {
        className: PropTypes.string,
        topTabKey: PropTypes.string.isRequired,
        mccStatus: PropTypes.bool,
        handleToggleCharts: PropTypes.func,
        getMainTableComparable: PropTypes.func
    };

    static defaultProps = {
        className: 'report-sdk',
        mccStatus: false,
        handleToggleCharts() {},
        getMainTableComparable() {}
    };

    onToggleChartArea = visible => {
        this.setState({
            chartAreaVisible: visible
        });
        this.props.handleToggleCharts(visible);
    }

    onChangeTableLoading = status => {
        this.setState({
            tableLoading: status
        });
    }

    render() {
        const {
            className,
            mainAreaConfig,
            topTabKey,
            mccStatus,
            getMainTableComparable,
            isMcc,
            initDateAndCompare
        } = this.props;
        const {chartAreaVisible, tableLoading} = this.state;
        const tableAreaConfig = mainAreaConfig.tableAreaConfig;
        const {
            tableIndicatorList,
            defaultIndicator,
            indicatorUiType = 'dropDown',
            tableReportType,
            extraStaticSorts,
            isShowTableArea = true
        } = tableAreaConfig || {};
        const reportTableProps = {
            isMcc,
            key: !extraStaticSorts && tableReportType || topTabKey,
            topTabKey,
            tableAreaConfig: tableAreaConfig || {},
            mainAreaConfig,
            onToggleChartArea: this.onToggleChartArea,
            initDateAndCompare,
            chartAreaVisible,
            mccStatus,
            getMainTableComparable,
            onChangeTableLoading: this.onChangeTableLoading
        };
        const containerClassName = `${className}-main-area`;
        const chartAreaProps = {
            className: containerClassName,
            topTabKey,
            mainAreaConfig,
            chartAreaVisible
        };
        const isRenderIndicatorList = tableIndicatorList
            && tableIndicatorList.length > 0
            && indicatorUiType === 'tab';
        const indicatorProps = {
            defaultIndicator,
            tableIndicatorList,
            indicatorUiType,
            className: `${containerClassName}-table-area-tab`,
            tableReportType,
            tableLoading
        };
        return (
            <div className={containerClassName}>
                {!mccStatus && <ChartArea {...chartAreaProps} />}
                <div className={`${containerClassName}-table-area`}>
                    {isRenderIndicatorList && <Indicator {...indicatorProps} />}
                    {isShowTableArea && <ReportTable {...reportTableProps} />}
                </div>
            </div>
        );
    }
}
