/**
 * @file sdk入口 - 组件
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Table, SearchBox, Pagination, Button, Toast} from '@baidu/one-ui';
import {IconTimes} from 'dls-icons-react';
import ReportSdkCustomReportService from '../../services/CustomServices/ReportSdkCustomReportService';
import {getApiError} from '../../utils/common';
import AddCustom from './components/AddCustom';
import IconTip from './components/IconTip';
import {getColumns, customReportType, columnSortType} from './config';
import {sortActions} from './handler';

const childContextTypesScheme = {
    eventHub: PropTypes.shape({
        on: PropTypes.func.isRequired,
        once: PropTypes.func.isRequired,
        un: PropTypes.func.isRequired,
        fire: PropTypes.func.isRequired,
        trigger: PropTypes.func.isRequired,
        destroyEvents: PropTypes.func.isRequired
    }).isRequired,
    config: PropTypes.object.isRequired,
    hairuo: PropTypes.object.isRequired,
    token: PropTypes.string.isRequired
};

export default class ReportSdk extends PureComponent {
    constructor(props) {
        super(props);
        const {config, eventHub} = props;
        this.eventHub = eventHub;
        const {
        } = config;
        this.state = {
            sortOrder: '',
            sortType: '',
            dataSource: [],
            pageNo: 1,
            pageSize: 20,
            searchValue: '',
            tableLoading: true,
            updateByFilterValue: '',
            updateByFilterValueInput: '',
            updateByFilterDropdownVisible: false,
            selectedRowKeys: [],
            deling: false,
            countCreateByOpId: 0,
            countLimit: 50
        };
    }
    static propTypes = {
        /** 用户可自定义class */
        className: PropTypes.string,
        ...childContextTypesScheme
    };

    static defaultProps = {
        className: 'custom-report-sdk'
    };

    static childContextTypes = {
        ...childContextTypesScheme
    }

    componentDidMount() {
        // 请求多账户数据
        const {config, hairuo} = this.props;
        const eventHub = this.eventHub;
        this.service = new ReportSdkCustomReportService({
            eventHub,
            config,
            hairuo
        });
        eventHub.on('sdk.event.table.loadData', this.handleGetCustomList);
        this.handleGetCustomList();
    }

    handleGetCustomList = () => {
        const {fire} = this.eventHub;
        const {config = {}} = this.props;
        const {appId} = config;
        this.setState({tableLoading: true});
        const params = {
            appId,
            customReportType
        };
        this.service.getCustomList({params}).then(({data = []}) => {
            fire('sdk.event.enter.log', {
                hitType: 'custom-data-loaded-success'
            });
            let countCreateByOpId = 0;
            let countLimit = 50;
            if (data.length > 0) {
                const firstData = data[0];
                countCreateByOpId = firstData.countCreateByOpId || 0;
                countLimit = firstData.countLimit || 50;
            }
            this.setState({
                dataSource: data,
                listData: data,
                pageNo: 1,
                countCreateByOpId,
                countLimit
            });
        }, err => {
            getApiError(err, {useToast: true});
            fire('sdk.event.enter.log', {
                hitType: 'custom-data-loaded-fail'
            });
        }).finally(() => {
            this.setState({tableLoading: false});
        });
    }

    getChildContext() {
        const {config, hairuo, token} = this.props;
        return {
            eventHub: this.eventHub,
            config,
            hairuo,
            token
        };
    }
    handleTableSort = ({sortColumn: {dataIndex}, sortOrder}) => {
        const {
            sortOrder: lastSortOrder,
            sortType: lastSortType,
            listData
        } = this.state;
        let sortType = dataIndex;

        if (lastSortOrder === 'ascend' && lastSortType === sortType) {
            sortType = '';
        }
        let dataSource = [...listData];
        if (sortType) {
            const sortAction = sortActions[columnSortType[sortType]];
            dataSource.sort(sortAction(sortType, sortOrder));
        }
        this.setState({
            sortOrder,
            sortType,
            dataSource
        });
    }
    onShowSizeChange = e => {
        this.setState({
            pageSize: +e.target.value,
            pageNo: 1
        });
    }
    onPageNoChange = e => {
        this.setState({
            pageNo: +e.target.value
        });
    }
    handleSearchChange = e => {
        this.setState({searchValue: e.target.value, pageNo: 1});
    }
    handleSearchClear = () => {
        this.setState({searchValue: '', pageNo: 1});
    }
    updateByFilterDropdownVisibleChange = visible => {
        this.setState({updateByFilterDropdownVisible: visible});
    }
    updateByFilterValueChange = e => {
        this.setState({updateByFilterValueInput: e.target.value});
    }
    updateByFilterConfirm = () => {
        const {updateByFilterValueInput} = this.state;
        this.setState({
            updateByFilterValue: updateByFilterValueInput,
            updateByFilterDropdownVisible: false,
            pageNo: 1
        });
    }
    handleTableSelectChange = selectedRowKeys => {
        this.setState({selectedRowKeys});
    }
    handleClearSelectRows = () => {
        this.setState({selectedRowKeys: []});
    }
    handleBatchDel = () => {
        const {fire} = this.eventHub;
        const params = {
            reportIds: this.state.selectedRowKeys
        };
        this.setState({deling: true});
        this.service.delCustomReportList({params}).then(res => {
            fire('sdk.event.enter.log', {
                hitType: 'custom-batch-del-success'
            });
            Toast.success({content: '删除成功！', showCloseIcon: false});
            this.setState({deling: false, selectedRowKeys: []});
            this.handleGetCustomList();
        }, err => {
            fire('sdk.event.enter.log', {
                hitType: 'custom-batch-del-fail'
            });
            getApiError(err, {useToast: true});
            this.setState({deling: false});
        });
    }
    render() {
        const {fire} = this.eventHub;
        const {
            className,
            config: {
                reportTypes = []
            },
            hairuo: {optId}
        } = this.props;
        const {
            dataSource,
            listData = [],
            sortType,
            sortOrder,
            pageNo,
            pageSize,
            searchValue,
            tableLoading,
            updateByFilterValue,
            updateByFilterValueInput,
            updateByFilterDropdownVisible,
            selectedRowKeys,
            deling,
            countCreateByOpId,
            countLimit
        } = this.state;
        const searchBoxProps = {
            width: '204px',
            placeholder: '请搜索报告名称',
            showSearchIcon: true,
            onChange: this.handleSearchChange,
            onClearClick: this.handleSearchClear
        };
        const tableData = (searchValue || updateByFilterValue)
            ? dataSource.filter(dataItem => {
                if (searchValue && updateByFilterValue) {
                    return dataItem.reportName.includes(searchValue)
                        && dataItem.updateBy.includes(updateByFilterValue);
                } else if (searchValue) {
                    return dataItem.reportName.includes(searchValue);
                } else {
                    return dataItem.updateBy.includes(updateByFilterValue);
                }
            })
            : dataSource;
        const reportTypesMap = reportTypes.reduce((memo, reportItem) => {
            memo[reportItem.value] = reportItem.label;
            return memo;
        }, {});
        const dataSourceProps = [...tableData].splice((pageNo - 1) * pageSize, pageSize);
        const changableRowKeys = tableData.reduce((memo, item) => {
            if (+item.updateByUserId === +optId) {
                memo.push(item.reportId);
            }
            return memo;
        }, []);
        const changableRowKeysCount = changableRowKeys.length;
        const tableProps = {
            className: `${className}-main-container-table`,
            key: sortType,
            loading: tableLoading,
            columns: getColumns({
                reportTypesMap,
                updateByFilterValue,
                updateByFilterValueInput,
                updateByFilterDropdownVisible,
                updateByFilterDropdownVisibleChange: this.updateByFilterDropdownVisibleChange,
                updateByFilterValueChange: this.updateByFilterValueChange,
                updateByFilterConfirm: this.updateByFilterConfirm
            }).map(column => {
                if (column.dataIndex === sortType) {
                    column.sortOrder = sortOrder;
                }
                return column;
            }),
            rowKey: record => record.reportId,
            rowSelection: dataSourceProps.length > 0
                ? {
                    selectedRowKeys,
                    onChange: this.handleTableSelectChange,
                    getCheckboxProps: record => {
                        return {
                            disabled: +record.updateByUserId !== +optId
                        };
                    },
                    hideDefaultSelections: true,
                    selections: [{
                        key: 'current',
                        text: '选择当前页',
                        onSelect: () => {
                            fire('sdk.event.enter.log', {
                                hitType: 'custom-table-selector-current'
                            });
                            this.setState({
                                selectedRowKeys: changableRowKeys.filter(item => {
                                    return dataSourceProps.filter(data => data.reportId === item).length;
                                })
                            });
                        }
                    }, {
                        key: 'all',
                        text: '选择全部',
                        onSelect: () => {
                            fire('sdk.event.enter.log', {
                                hitType: 'custom-table-selector-all'
                            });
                            this.setState({
                                selectedRowKeys: changableRowKeys
                            });
                        }
                    }]
                }
                : {hideDefaultSelections: true},
            dataSource: dataSourceProps,
            onSortClick: this.handleTableSort,
            size: 'small',
            showHeader: true,
            updateWidthChange: true,
            headerFixTop: 0,
            bottomScroll: {bottom: 0},
            useStickyFixTop: true,
            pagination: false
        };
        const paginationProps = {
            total: tableData.length,
            pageNo,
            pageSize,
            onPageSizeChange: this.onShowSizeChange,
            onPageNoChange: this.onPageNoChange
        };
        let tipContent = '定制报告任务上限为50个，如超出限制，可以通过删除不用的定制报告后重新提交定制任务；'
            + '定制报告有效期为一年，您可以在有效期临近到期前30天内进行续期，如未续期且超出有效期截止日6个月，系统自动清理这部分报告。';
        if (countCreateByOpId > 50) {
            tipContent = `当前还可以新增${countLimit - countCreateByOpId}个定制报告任务，超出后无法再次新建，可以通过删除不用的定制报告后重新提交定制任务；`
                + '定制报告有效期为一年，您可以在有效期临近到期前30天内进行续期，如未续期且超出有效期截止日6个月，系统自动清理这部分报告。';
        }
        const isLimited = countCreateByOpId >= countLimit;
        if (isLimited) {
            tipContent = '定制报告生成数量已达上限，请先清理不需要的报告后再进行新建';
        }
        const selectedRowsCount = selectedRowKeys.length;
        return (
            <div className={className}>
                <div className={`${className}-title-area`}>定制报告</div>
                <div className={`${className}-main-container`}>
                    <div className={`${className}-main-container-action-bar`}>
                        {selectedRowsCount ? <div className={`${className}-main-container-action-bar-batch-alert`}>
                            <div
                                className={`${className}-main-container-action-bar-batch-alert-container`}
                            >
                                <span>
                                    已选
                                    {changableRowKeysCount === selectedRowsCount ? '全部' : ''}
                                    {selectedRowsCount}行
                                </span>
                                <Button onClick={this.handleBatchDel} loading={deling}>删除</Button>
                            </div>
                            <IconTimes onClick={this.handleClearSelectRows} />
                        </div> : null}
                        <AddCustom isLimited={isLimited} className={`${className}-main-container-action-bar-add`} />
                        <div className={`${className}-main-container-action-bar-tip`}>
                            <IconTip
                                className={`${className}-main-container-action-bar-tip-icon`}
                                title="定制报告"
                                content={tipContent}
                            />
                        </div>
                        <div className={`${className}-main-container-action-bar-search-box`}>
                            <SearchBox {...searchBoxProps} />
                        </div>
                    </div>
                    <Table {...tableProps} />
                    <div className={`${className}-main-container-pagination`}>
                        <Pagination {...paginationProps} />
                    </div>
                </div>
            </div>
        );
    }
}
