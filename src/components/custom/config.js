import React from 'react';
import {range} from 'lodash';
import {SearchBox} from '@baidu/one-ui';
import {IconInfoCircle} from 'dls-icons-react';
import cellRender from './components/CellRender';
import Operation from './components/CellRender/Operation';
import IconTip from './components/IconTip';
import {getDiffDay} from '../../utils/filterArea';

export const cls = 'custom-report-sdk';

export const statusMap = {
    WAITING: '未开始',
    RUNNING: '运行中',
    SUCCESS: '成功',
    FAIL: '失败'
};

const renewalTip = '该报告将于30天内到期，为不影响您的正常使用，请及时在操作栏中进行续期。';
const renewalTipDay = 30;

export const getColumns = ({
    reportTypesMap,
    updateByFilterValue,
    updateByFilterValueInput,
    updateByFilterDropdownVisible,
    updateByFilterDropdownVisibleChange,
    updateByFilterValueChange,
    updateByFilterConfirm
}) => [{
    title: '报告名称',
    dataIndex: 'reportName',
    key: 'reportName',
    fixed: 'left',
    sorter: true,
    width: 144,
    render: cellRender
}, {
    title: '操作',
    key: 'action',
    fixed: 'left',
    width: 124,
    render: (text, record) => {
        return <Operation record={record} />;
    }
}, {
    title: '已选账户',
    dataIndex: 'selectedUsers',
    key: 'selectedUsers',
    width: 100,
    render: (value, record, row) => {
        const names = (record.selectedUsers || []).map(name => name.userName);
        const accountNum = names.length;
        const nameDesc = names.slice(0, 3).join(',');
        const content = accountNum > 3
            ? `${nameDesc}等${accountNum}个账户`
            : nameDesc;
        return cellRender(content);
    }
}, {
    title: '操作人',
    key: 'updateBy',
    dataIndex: 'updateBy',
    sorter: true,
    width: 100,
    render: cellRender,
    filteredValue: updateByFilterValue,
    filterDropdownVisible: updateByFilterDropdownVisible,
    onFilterDropdownVisibleChange: updateByFilterDropdownVisibleChange,
    filterDropdown: (<div className="custom-report-sdk-main-container-table-header-filter">
        <SearchBox
            searchIconType="custom"
            customRender="确定"
            value={updateByFilterValueInput}
            onChange={updateByFilterValueChange}
            onClearClick={updateByFilterValueChange}
            onSearch={updateByFilterConfirm}
        />
    </div>)
}, {
    title: '时间范围',
    dataIndex: 'startDate',
    key: 'startDate',
    width: 132,
    render: (value, record, row) => {
        if (!value || !record.endDate) {
            return '-';
        }
        return <span>{value}至<br />{record.endDate}</span>;
    }
}, {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    sorter: true,
    width: 132,
    render: cellRender
}, {
    title: '有效期至',
    dataIndex: 'expireTime',
    key: 'expireTime',
    width: 132,
    render: (value, record, row) => {
        if (!value) {
            return '-';
        }
        const {renewalable} = record;
        return (<>
            <span title={value}>{value}</span>
            {!!renewalable && <IconTip content={renewalTip}>
                <IconInfoCircle className={`${cls}-main-container-table-expire-tip`} />
            </IconTip>}
        </>);
    }
}, {
    title: '报告类型',
    dataIndex: 'reportType',
    key: 'reportType',
    sorter: true,
    width: 100,
    render: value => {
        if (!value) {
            return '-';
        }
        return cellRender(reportTypesMap[value] || '定向分析报告');
    }
}, {
    title: '更新频率',
    dataIndex: 'scheduleTimeName',
    key: 'scheduleTimeName',
    width: 100,
    render: cellRender
}, {
    title: '状态',
    dataIndex: 'executionStatus',
    key: 'executionStatus',
    sorter: true,
    width: 80,
    render: value => {
        return cellRender(statusMap[value]);
    }
}, {
    title: '邮箱',
    dataIndex: 'mails',
    key: 'mails',
    width: 100,
    render: (value = []) => {
        const accountNum = value.length;
        const mailsDesc = value.slice(0, 3).join(';');
        const content = accountNum > 3
            ? `${mailsDesc}等${accountNum}个邮箱`
            : mailsDesc;
        return cellRender(content);
    }
}, {
    title: '报告ID',
    dataIndex: 'reportId',
    key: 'reportId',
    width: 170,
    render: cellRender
}];

export const customReportType = 'CUSTOM';

export const columnSortType = {
    reportName: 'sortString',
    updateBy: 'sortString',
    updateTime: 'sortTime',
    reportType: 'sortNumber',
    executionStatus: 'sortString',
    fileSize: 'sortString'
};

export const sortValueMap = {
    executionStatus: statusMap
};

export const allItemValue = 'all';
export const itemValue = 'item';
export const filtersDefault = {
    feedSubjectEnum: {
        name: '营销目标',
        field: 'feedSubjectEnum',
        defaultValue: allItemValue,
        option: [
            {
                label: '全部',
                value: allItemValue,
                realValue: ''
            },
            {
                label: '网站链接',
                value: '1'
            },
            {
                label: '应用推广（iOS）',
                value: '2'
            },
            {
                label: '应用推广（Android）',
                value: '3'
            },
            {
                label: '商品目录',
                value: '5'
            },
            {
                label: '门店推广',
                value: '6'
            },
            {
                label: '电商店铺',
                value: '7'
            }
        ]
    },
    ocpcPayMode: {
        name: '付费模式',
        field: 'ocpcPayMode',
        defaultValue: allItemValue,
        option: [
            {
                label: '全部',
                value: allItemValue,
                realValue: ['0', '1', '2']
            },
            {
                label: 'oCPM',
                value: '2'
            },
            {
                label: 'oCPC',
                value: itemValue,
                realValue: ['0', '1']
            }
        ]
    },
    bidType: {
        name: '出价模式',
        field: 'bidType',
        defaultValue: allItemValue,
        option: [
            {
                label: '全部',
                value: allItemValue,
                realValue: ['3', '5']
            },
            {
                label: '转化出价',
                value: '3'
            },
            {
                label: '点击出价',
                value: '5'
            }
        ]
    },
    secondComponentType: {
        name: '组件类型',
        field: 'secondComponentType',
        style: {width: 200},
        isMultipleValue: true
    }
};
export const timeUnits = [
    {
        label: '合计',
        value: 'SUMMARY'
    },
    {
        label: '分时',
        value: 'HOUR'
    },
    {
        label: '分日',
        value: 'DAY'
    },
    {
        label: '分周',
        value: 'WEEK'
    },
    {
        label: '分月',
        value: 'MONTH'
    }
];


export const scheduleOptionChildren = range(31).map(date => {
    if (date < 28) {
        return {
            value: date + 1,
            label: `第${date + 1}天`
        };
    }
    return {
        value: 27 - date,
        label: `倒数第${date - 27}天`
    };
});

export const hourInDay = 'HOUR_IN_DAY';
export const dayInWeek = 'DAY_IN_WEEK';
export const dayInMonth = 'DAY_IN_MONTH';
export const dayInQuarter = 'DAY_IN_QUARTER';
export const scheduleOptions = [{
    value: 'NONE',
    label: '仅此一次'
}, {
    value: hourInDay,
    label: '每天'
}, {
    value: dayInWeek,
    label: '每周',
    children: [{
        value: 1,
        label: '周一'
    }, {
        value: 2,
        label: '周二'
    }, {
        value: 3,
        label: '周三'
    }, {
        value: 4,
        label: '周四'
    }, {
        value: 5,
        label: '周五'
    }, {
        value: 6,
        label: '周六'
    }, {
        value: 7,
        label: '周日'
    }]
}, {
    value: dayInMonth,
    label: '每月',
    children: scheduleOptionChildren
}, {
    value: dayInQuarter,
    label: '每季度',
    children: scheduleOptionChildren
}];

export const maxEmails = 5;

export const regionNameMap = {provinceName: '分省', provinceCityName: '分地市'};
export const regionColumns = Object.keys(regionNameMap);

export const disableDatePickTip = '报告“更新频率”已设置为周期性，数据时间范围以所选定的周期为准；若希望查询自定义时间段数据，请将“更新频率”设置为“仅一次”';
