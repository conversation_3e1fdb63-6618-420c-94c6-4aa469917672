@import "../../default.less";
@import "./components//filterItem/secondComponentType/style.less";

.@{custom-report-prefix-cls} {
    &-title-area {
        font-size: @dls-padding-unit * 5;
        line-height: @dls-padding-unit * 5;
        background-color: @dls-color-gray-0;
        border-radius: @dls-border-radius-2;
        padding: @dls-padding-unit * 5 @dls-padding-unit * 6 @dls-padding-unit * 4.5;
        margin-bottom: @dls-padding-unit * 6;
    }
    &-main-container {
        display: flex;
        flex-direction: column;
        padding: @dls-padding-unit * 6;
        background-color: @dls-color-gray-0;
        border-radius: @dls-border-radius-2;
        &-action-bar {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: @dls-padding-unit * 4;
            position: relative;
            &-tip {
                margin-left: @dls-padding-unit * 3;
                font-size: @dls-font-size-2;
            }
            &-search-box {
                margin-left: @dls-padding-unit * 3;
            }
            &-batch-alert {
                display: flex;
                justify-content: space-between;
                align-items: center;
                position: absolute;
                top: 0;
                width: 100%;
                height: 48px;
                border-radius: 3px;
                background-color: #0052cc;
                padding: 0 16px;
                box-sizing: border-box;
                z-index: 9;
                &-container {
                    display: flex;
                    align-items: center;
                    >span:first-of-type {
                        font-size: @dls-font-size-1;
                        color: @dls-color-gray-0;
                        margin-right: @dls-padding-unit * 5;
                    }
                }
                svg {
                    color: @dls-color-gray-0;
                    cursor: pointer;
                }
            }
        }
        &-table {
            &-cell {
                display: -webkit-box;
                overflow: hidden;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
            &-operaion {
                display: flex;
                align-items: center;
                &-btn {
                    margin-left: @dls-padding-unit * 2;
                    &:first-of-type {
                        margin-left: 0;
                    }
                }
                &-icon {
                    font-size: @dls-font-size-0;
                    margin-left: @dls-padding-unit * 2;
                    cursor: pointer;
                    padding-right: @dls-padding-unit * 2;
                }
            }
            &-header-filter {
                padding: 16px;
            }
            &-expire-tip {
                margin-left: @dls-padding-unit * 2;
                color: @dls-color-error;
            }
        }
        &-pagination {
            margin-top: @dls-padding-unit * 4;
        }
    }
    &-form {
        &-group {
            padding: @dls-padding-unit * 5 @dls-padding-unit * 6;
            background-color: @dls-color-gray-0;
            border-radius: @dls-border-radius-2;
            margin-bottom: @dls-padding-unit * 6;
            &-title {
                font-size: @dls-font-size-3;
            }
            &-content {
                margin-top: @dls-padding-unit * 6;
                &-row {
                    display: flex;
                    margin-bottom: @dls-padding-unit * 8;
                    &.text-line {
                        margin-top: -@dls-padding-unit * 3 / 2;
                    }
                    &:last-of-type {
                        margin-bottom: 0;
                    }
                }
                &-label {
                    width: @dls-padding-unit * 24;
                    margin-right: @dls-padding-unit * 8;
                    text-align: right;
                    &-require {
                        color: @dls-color-error-7;
                        padding-right: @dls-padding-unit;
                    }
                }
                &-label:not(.text-line) {
                    height: @dls-padding-unit * 8;
                    line-height: @dls-padding-unit * 8;
                }
                &-control {
                    display: flex;
                    &.flex-dir-column {
                        flex-direction: column;
                    }
                    &-more-filter.one-select {
                        margin-left: @dls-padding-unit * 5;
                        &:first-of-type {
                            margin-left: 0;
                        }
                    }
                    &-user-picker {
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        width: @dls-padding-unit * 73 / 2;
                        text-align: left;
                        transform: translateX(-@dls-padding-unit * 5 / 2);
                        &-content {
                            background: @dls-color-gray-0;
                            background-color: @dls-color-gray-0;
                            padding: @dls-padding-unit * 5;
                            border-radius: @dls-padding-unit;
                            box-shadow: @dls-shadow-1;
                            &-title {
                                font-size: @dls-font-size-1;
                                margin-bottom: @dls-padding-unit * 3.5;
                            }
                            &-button-container {
                                margin-top: @dls-padding-unit * 4;
                            }
                        }
                    }
                    &-mail-content {
                        width: @dls-padding-unit * 75;
                        box-sizing: border-box;
                        background: @dls-background-color-base-1;
                        border: 1px solid @dls-border-color-fillable;
                        border-radius: @dls-border-radius-1;
                        padding: @dls-spacing-unit * 2.5;
                        .one-tag {
                            margin: 0 0 (@dls-spacing-unit * 2) 0;
                            float: left;
                            clear: both;
                        }
                        &-input {
                            display: flex;
                            justify-content: space-around;
                            align-items: center;
                            input {
                                width: @dls-padding-unit * 58;
                                border: 0;
                                outline: 0;
                                line-height: @dls-line-height-2;
                                font-size: @dls-font-size-1;
                            }
                            span {
                                float: right;
                                font-size: @dls-font-size-0;
                                color: @dls-color-gray-9;
                                letter-spacing: 0;
                                line-height: @dls-line-height-2;
                            }
                        }
                        &-required {
                            color: @dls-color-error;
                        }
                        &-tags {
                            overflow: hidden;
                            &-text {
                                overflow: hidden;
                                text-overflow: ellipsis;
                                max-width: @dls-padding-unit * 60;
                            }
                        }
                    }
                    .one-row.one-form-item {
                        margin-bottom: 0;
                    }
                    &-date-pick {
                        display: inline-block;
                    }
                    &-icon {
                        margin-left: @dls-padding-unit * 3;
                    }
                }
                &-columns-tip {
                    color: @dls-color-gray-7;
                    margin-bottom: @dls-padding-unit * 3 / 2;
                }
                &-type-tip {
                    color: @dls-color-gray-7;
                    margin-top: @dls-padding-unit * 3 / 2;
                    font-size: @dls-font-size-0;
                }
                &-tip {
                    color: @dls-color-gray-7;
                    margin-left: @dls-padding-unit * 5;
                }
            }
        }
        &-action {
            padding: @dls-padding-unit * 6;
            background-color: @dls-color-gray-0;
            border-radius: @dls-border-radius-2;
            &-cancel {
                margin-left: @dls-padding-unit * 3;
            }
        }
    }
}
.@{custom-report-prefix-cls}-main-container-table-operaion-pop-content {
    .one-tooltip-inner {
        display: flex;
        padding: @dls-padding-unit * 2 0;
        .one-menu-item {
            width: @dls-padding-unit * 13;
            margin: 0;
            color: @dls-color-brand-7;
            box-sizing: initial;
            &:hover {
                background-color: @dls-background-color-base-1-hover;
            }
            &:active {
                background-color: @dls-background-color-base-1-active;
            }
            &-disabled {
                color: @dls-foreground-color-neutral-disabled;
                cursor: not-allowed;
                &:hover {
                    background-color: #fff;
                }
            }
        }
    }
    &-renewal {
        margin-left: @dls-padding-unit;
    }
}
.@{custom-report-prefix-cls}-tip-icon {
    color: @dls-color-gray-7;
    &:hover {
        color: @dls-color-gray-8;
        cursor: pointer;
    }
    &:active {
        color: @dls-color-gray-9;
    }
    &-content {
        width: @dls-padding-unit * 50;
    }
    &-title {
        color: @dls-color-gray-9;
    }
    &-popover {
        .one-popover-arrow {
            pointer-events: none;
        }
    }
}
.@{custom-report-prefix-cls}-main-container-action-bar-add-dialog {
    .one-dialog {
        .one-dialog-content {
            height: initial;
            min-height: 100%;
            max-height: initial;
            background-color: @dls-background-color-base-2;
            .one-dialog-body {
                max-height: initial;
                overflow: visible;
            }
        }
    }
}
