import {isEmpty} from 'lodash-es';

import {
    sortValueMap,
    filtersDefault,
    allItemValue,
    hourInDay,
    dayInWeek
} from './config';

const getValues = (a, b, sortType) => {
    let aValue = a[sortType];
    let bValue = b[sortType];
    const valueMap = sortValueMap[sortType];
    if (valueMap) {
        aValue = valueMap[aValue];
        bValue = valueMap[bValue];
    }
    return {aValue, bValue};
};
export const sortActions = {
    sortString: (sortType, sortOrder) => (a, b) => {
        const {aValue, bValue} = getValues(a, b, sortType);
        if (sortOrder === 'ascend') {
            return aValue.localeCompare(bValue, 'zh-Hans-CN', {sensitivity: 'accent'});
        } else {
            return bValue.localeCompare(aValue, 'zh-Hans-CN', {sensitivity: 'accent'});
        }
    },
    sortTime: (sortType, sortOrder) => (a, b) => {
        const {aValue, bValue} = getValues(a, b, sortType);
        const aTime = +new Date(aValue);
        const bTime = +new Date(bValue);
        if (sortOrder === 'ascend') {
            return aTime - bTime;
        } else {
            return bTime - aTime;
        }
    },
    sortNumber: (sortType, sortOrder) => (a, b) => {
        const {aValue, bValue} = getValues(a, b, sortType);
        if (sortOrder === 'ascend') {
            return aValue - bValue;
        } else {
            return bValue - aValue;
        }
    }
};

export const getUserPickData = ({userList = []}) => {
    const allDataMap = {};
    const candidateList = [];
    for (let user of userList) {
        allDataMap[user.userId] = {
            key: user.userId,
            title: user.userName
        };
        candidateList.push(user.userId);
    }
    return {
        allDataMap,
        candidateList
    };
};

export function isEmail(email) {
    /* eslint-disable-next-line */
    const EMAIL_REG = /^([a-zA-Z0-9_\.-]+)@([\da-zA-Z\.-]+)\.([a-zA-Z\.]{2,6})([,，]([a-zA-Z0-9_\.-]+)@([\da-zA-Z\.-]+)\.([a-zA-Z\.]{2,6}))*$/;
    return EMAIL_REG.test(email);
}

const getMatchedUsers = (userList, selectedUsers) => {
    // TODO 待one-ui修复穿梭框搜索后选择返回数值类型不一致后去掉下面兼容逻辑 bug卡片：https://console.cloud.baidu-int.com/devops/icafe/issue/baidu-fc-fe-one-ui-192/show
    selectedUsers = selectedUsers.map(userId => +userId);

    return userList.filter(
        user => selectedUsers.includes(user.userId)
    );
};
const formatFilters2Api = (
    filters,
    filtersFromConfig = []
) => Object.entries(filters).reduce((memo, [filter, filterValue]) => {

    const filterInfoFromConfig = filtersFromConfig.filter(i => {
        return i.field === filter;
    })[0] || {};

    const filterConfig = {
        ...(filtersDefault[filter] || {}),
        ...filterInfoFromConfig
    };

    let values = [];
    if (filterConfig.isMultipleValue) {
        values = filterValue;
        if (!values?.length) {
            return memo;
        }
    }
    else {
        const selectedFilter = filterConfig.option.filter(
            item => item.value === filterValue
        )[0];
        if (selectedFilter.value === allItemValue && !selectedFilter.realValue) {
            return memo;
        }
        values = selectedFilter.realValue || [selectedFilter.value];
    }

    memo.push({
        column: filter,
        values,
        operator: 'IN'
    });
    return memo;
}, []);
export const getSaveParams = ({
    appId,
    reportId,
    reportType,
    schedule,
    formData: {
        columns,
        reportName,
        selectedUsers,
        startDate,
        endDate,
        mails,
        filters,
        timeUnit,
        splitColumn
    } = {},
    mccInfo: {
        isMcc,
        userList = []
    } = {},
    userName,
    userId,
    filtersFromConfig = []
}) => {
    return {
        reportName,
        customReportType: 'CUSTOM',
        reportType,
        appId,
        scheduleTimeUnit: hourInDay === schedule[0] ? dayInWeek : schedule[0],
        scheduleTimes: schedule.length > 1 ? [schedule[1]] : [],
        startDate: startDate.replace(/\//g, '-'),
        endDate: endDate.replace(/\//g, '-'),
        mails,
        ...(mails.length ? {sendChannels: [
            'MAIL'
        ]} : {}),
        selectedUsers: isMcc ? getMatchedUsers(userList, selectedUsers) : [{userName, userId}],
        ...(!reportId ? {} : {reportId}),
        dataRequest: {
            timeUnit,
            columns,
            filters: formatFilters2Api(filters, filtersFromConfig),
            splitColumn: splitColumn[splitColumn.length - 1] || ''
        }
    };
};

export const formatFilters = (filters = [], filtersFromConfig = []) => {
    return filters.reduce((memo, {
        column,
        values
    }) => {

        const filterInfoFromConfig = filtersFromConfig.filter(i => {
            return i.field === column;
        })[0] || {};

        const filterConfig = {
            ...(filtersDefault[column] || {}),
            ...filterInfoFromConfig
        };
        if (!isEmpty(filterConfig)) {
            const {isMultipleValue, option: filterOptions} = filterConfig;

            if (isMultipleValue) {
                memo[column] = values;
            }
            else {
                let filterValue = '';
                if (values.length > 1) {
                    for (let v of filterOptions) {
                        const configRealValue = v.realValue || [];
                        const isEquslItem = configRealValue.length
                            && values.length === configRealValue.length
                            && Array.from(new Set([...configRealValue, ...values])).length === values.length;
                        if (isEquslItem) {
                            filterValue = v.value;
                            break;
                        }
                    }
                }
                else {
                    filterValue = values[0];
                }
                memo[column] = filterValue;
            }
        }
        return memo;
    }, {});
};
