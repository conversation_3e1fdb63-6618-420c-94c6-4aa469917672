import React from 'react';
import {Select} from '@baidu/one-ui';

const Option = Select.Option;

export default ({value, options = [], disabled, className, customRenderTarget, onChange}) => (<Select
    className={className}
    width={200}
    disabled={disabled}
    value={value}
    customRenderTarget={customRenderTarget}
    onChange={onChange}
    getPopupContainer={triggerNode => triggerNode.parentNode}
>
    {options.map(({value, label, disabled}) => {
        return <Option key={value} value={value} disabled={disabled}>{label}</Option>;
    })}
</Select>);

