
.custom-filter-second-component-type-container {
    height: @dls-padding-unit * 7.5;
    line-height: @dls-padding-unit * 7.5;
    font-size: @dls-font-size-1;
    color: @dls-color-gray-9;
    border: 1px solid @dls-color-gray-4;
    border-radius: @dls-padding-unit;
    // padding: 0 @dls-padding-unit * 3 0 0;
    display: inline-flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    cursor: pointer;
    position: relative;
    &.disabled {
        background-color: @dls-color-gray-1;
        color: @dls-color-gray-6;
    }
    .second-component-type-content {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-items: center;
        padding-left: @dls-padding-unit * 3;

        .texts {
            width: @dls-padding-unit * 25;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
        }
    }
    .indicator-arrow {
        position: absolute;
        top: @dls-padding-unit * 0.5;
        right: @dls-padding-unit * 3;
        display: inline-block;
        margin-left: @dls-padding-unit * 2;
        .indicator-icon {
            color: @dls-color-gray-7;
            width: @dls-padding-unit * 2.5;
        }
    }
}
.custom-filter-second-component-type-overlay-container {
    background: @dls-color-gray-0;
    box-shadow: @dls-shadow-1;
    .btn-container {
        padding: @dls-padding-unit * 4;
        border-top: 1px solid @dls-color-gray-4;
    }
}
.custom-filter-second-component-type-cascader {
    .one-cascader-pane-menus {
        box-shadow: none;
        .one-cascader-pane-menu {
            min-width: @dls-padding-unit * 43;
            height: @dls-padding-unit * 58;
        }
    }
}
