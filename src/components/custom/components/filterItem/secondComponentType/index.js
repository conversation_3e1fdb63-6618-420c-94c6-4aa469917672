/**
 * @file 组件类型
 * <AUTHOR>
 * @date 2020/08/31
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Dropdown, CascaderPane, Button} from '@baidu/one-ui';
import {IconChevronDown, IconChevronUp} from '@baidu/one-ui-icon';

export default class SecondComponentType extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    constructor(props) {
        super(props);
        this.state = {
            checkedKeys: props.value || [],
            visible: false
        };
    }

    onVisibleChange = visible => {
        this.setState({
            visible,
            checkedKeys: this.props.value || []
        });
    }

    onCheckboxChange = checkedKeys => {
        this.setState({
            checkedKeys
        });
    }

    onComfirm = () => {
        const {checkedKeys} = this.state;
        const {options} = this.props;
        // 剔除掉有子级的一级标签
        const targetKeys = checkedKeys.filter(key => {
            const targetOptionList = options.filter(o => o.value === key);
            if (targetOptionList?.[0]?.children?.length) {
                return false;
            }
            return true;
        });
        this.props.onChange(targetKeys);

        this.setState({
            visible: false
        });
    }

    onCancel = () => {
        this.setState({
            visible: false
        });
    }

    getSelectedItemText = () => {

        const {options} = this.props;

        const selectedTexts = (this.props.value || []).map(key => {
            let text = '';
            options.filter(o => {
                const {children} = o;
                if (children && children.length) {
                    children.map(child => {
                        if (child.value === key) {
                            text = child.label;
                        }
                    });

                }
                else {
                    if (o.value === key) {
                        text = o.label;
                    }
                }
            });
            return text;
        }).join(',');
        return selectedTexts;
    }

    render() {
        const classPrefix = 'custom-filter';
        const {options, style = {width: 200}, disabled} = this.props;
        const {visible, checkedKeys} = this.state;
        const selectedKeys = this.props.value || [];
        const cascaderPaneProps = {
            showCheckbox: true,
            style: {'min-height': '180px'},
            options,
            checkedKeys,
            onCheckboxChange: this.onCheckboxChange,
            className: `${classPrefix}-second-component-type-cascader`
        };
        const content = (
            <div className={`${classPrefix}-second-component-type-overlay-container`}>
                <CascaderPane {...cascaderPaneProps} />
                <div className="btn-container">
                    <Button type="primary" style={{marginRight: '12px'}} onClick={this.onComfirm}>确定</Button>
                    <Button onClick={this.onCancel}>取消</Button>
                </div>
            </div>
        );
        const dropdownProps = {
            overlay: content,
            visible,
            disabled,
            onVisibleChange: this.onVisibleChange,
            // fix 在drawer下body一直为屏幕宽度会导致滚动时dropdown不跟随
            // 加这个fix
            getPopupContainer: triggerNode => triggerNode.parentNode,
            trigger: ['click']
        };
        const arrowRender = visible
            ? <span className="indicator-arrow"><IconChevronUp className="indicator-icon" /></span>
            : <span className="indicator-arrow"><IconChevronDown className="indicator-icon" /></span>;
        const selectedTexts = this.getSelectedItemText();
        const dropdownText = selectedKeys?.length ? selectedTexts : '全部';
        const dropdownContent = (
            <div className="second-component-type-content" title={dropdownText} >
                <span>组件类型</span>
                <span className="texts">：{selectedKeys?.length ? selectedTexts : '全部'}</span>
                {arrowRender}
            </div>
        );
        return (
            <div
                className={`${classPrefix}-second-component-type-container ${disabled ? 'disabled' : ''}`}
                style={style}
            >
                <Dropdown {...dropdownProps}>
                    {dropdownContent}
                </Dropdown>
            </div>
        );
    }
}
