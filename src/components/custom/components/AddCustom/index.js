import React, {Component} from 'react';
import PropTypes from 'prop-types';
import {<PERSON><PERSON>, Dialog} from '@baidu/one-ui';
import {partial} from 'lodash';
import {getApiError} from '../../../../utils/common';
import ReportSdkCustomReportService from '../../../../services/CustomServices/ReportSdkCustomReportService';
import ReportForm from '../ReportForm';

export default class AddCustom extends Component {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }
    state = {
        visible: false,
        mccInfo: null
    }
    componentDidMount() {
        const {eventHub: {on}} = this.context;
        on('sdk.event.addCustom.handleTableAction', this.handleTableAction);
        this.service = new ReportSdkCustomReportService(this.context);
    }
    handleTableAction = async ({data: {visible, initData, isEdit, isCopy}}) => {
        const mccInfo = await this.getMccInfo();
        mccInfo && this.setState({visible, initFormData: initData, isEdit, isCopy, mccInfo});
    }
    handleAddDialogVisibleChange = async visible => {
        const {eventHub: {fire}} = this.context;
        fire('sdk.event.enter.log', {
            hitType: 'custom-table-actions-add'
        });
        const mccInfo = await this.getMccInfo();
        mccInfo && this.setState({visible, mccInfo});
    }
    getMccInfo = async () => {
        const {mccInfo} = this.state;
        if (mccInfo) {
            return mccInfo;
        }
        try {
            const params = {token: this.context.token};
            const {data = {}} = await this.service.getMccInfo({params});
            return data;
        } catch (err) {
            getApiError(err, {useToast: true});
        }
    }
    render() {
        const {className, isLimited} = this.props;
        const {visible, initFormData, isEdit, isCopy, mccInfo} = this.state;
        const dialogProps = {
            title: `${isEdit ? '编辑' : '新建'}定制报告`,
            fullScreen: true,
            destroyOnClose: true,
            needCloseIcon: false,
            className: `${className}-dialog`,
            visible,
            onCancel: partial(this.handleAddDialogVisibleChange, false),
            footer: []
        };
        const reportFromProps = {
            initFormData,
            isEdit,
            isCopy,
            mccInfo
        };
        return (<div className={className}>
            <Dialog {...dialogProps} >
                <ReportForm {...reportFromProps} />
            </Dialog>
            <Button
                type="primary"
                disabled={isLimited}
                onClick={partial(this.handleAddDialogVisibleChange, true)}
            >新建定制报告</Button>
        </div>);
    }
}