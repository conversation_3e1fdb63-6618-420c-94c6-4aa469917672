import React, {Component} from 'react';
import PropTypes from 'prop-types';
import {IconEllipsisVertical} from 'dls-icons-react';
import {<PERSON><PERSON>, Tooltip, <PERSON>u, Dialog, Toast} from '@baidu/one-ui';
import {debounce, partial} from 'lodash';
import ReportSdkCustomReportService from '../../../../services/CustomServices/ReportSdkCustomReportService';
import IconTip from '../IconTip';
import {cls, statusMap} from '../../config';
import {getApiError} from '../../../../utils/common';
import {download} from '../../../../utils/download';

export default class Operation extends Component {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    constructor(props, context) {
        super(props, context);
        this.handleDownload = debounce(this.startDownload, 3000, {leading: true});
    }
    componentDidMount() {
        this.service = new ReportSdkCustomReportService(this.context);
    }

    handleActions = async actionType => {
        const {eventHub: {fire, trigger}} = this.context;
        fire('sdk.event.enter.log', {
            hitType: `custom-table-actions-${actionType}`
        });
        try {
            const params = {
                reportId: this.props.record.reportId
            };
            const {data: initData = {}} = await this.service.getCustomReport({params});
            trigger('sdk.event.addCustom.handleTableAction', {data: {
                actionType,
                isEdit: actionType === 'edit',
                isCopy: actionType === 'copy',
                visible: true,
                initData
            }});
        } catch (err) {
            getApiError(err, {useToast: true});
        }
    }

    startDownload = async () => {
        Toast.info({content: '数据加载中，请耐心等待'});
        const {eventHub: {fire}} = this.context;
        fire('sdk.event.enter.log', {
            hitType: 'custom-table-actions-download'
        });
        try {
            const params = {
                executionId: this.props.record.executionId
            };
            const {data: {fileUrl} = {}} = await this.service.getCustomReportResult({params});
            download(fileUrl);
        } catch (err) {
            getApiError(err, {useToast: true});
        }
    }

    handleMenuClick = ({key}) => {
        const {eventHub: {fire, trigger}} = this.context;
        const isRenewal = key === 'renewal';
        const logName = `custom-table-actions-${key}`;
        fire('sdk.event.enter.log', {
            hitType: logName
        });
        Dialog.confirm({
            title: '温馨提示',
            content: isRenewal ? '是否确定将此报告的有效期延长一年？' : '您确定要删除该定制报告吗？删除操作不可恢复。',
            needCloseIcon: true,
            zIndex: 1051,
            onOk: () => {
                const actionService = isRenewal ? 'renewalCustomReport' : 'delCustomReport';
                const params = {
                    reportId: this.props.record.reportId
                };
                fire('sdk.event.enter.log', {
                    hitType: `${logName}-confirm`
                });
                return this.service[actionService]({params}).then(res => {
                    trigger('sdk.event.table.loadData');
                    Toast.success({
                        content: isRenewal ? '续期成功！' : '删除成功！',
                        showCloseIcon: false
                    });
                    return Promise.resolve();
                }, err => {
                    getApiError(err, {useToast: true});
                    return Promise.reject();
                });
            }
        });
    }

    render() {
        const {
            handleActions,
            handleDownload,
            handleMenuClick
        } = this;
        const preCls = `${cls}-main-container-table-operaion`;
        const btnProps = {
            size: 'small',
            type: 'text-strong',
            className: `${preCls}-btn`
        };
        const menuProps = {
            size: 'small',
            onClick: handleMenuClick
        };
        const {
            record: {
                renewalable,
                copyable,
                editable,
                executionStatus
            } = {}
        } = this.props;
        const isSuccess = statusMap[executionStatus] === '成功';
        return (<div className={preCls}>
            <Button onClick={partial(handleActions, 'edit')} {...btnProps} disabled={!editable} >编辑</Button>
            <Button onClick={partial(handleActions, 'copy')} {...btnProps} disabled={!copyable} >复制</Button>
            <Button onClick={handleDownload} {...btnProps} disabled={!isSuccess} >下载</Button>
            <Tooltip
                overlayClassName={`${preCls}-pop-content`}
                placement="rightTop"
                title={
                    <Menu {...menuProps} >
                        <Menu.Item key="renewal" disabled={!renewalable}>
                            续期
                            {!renewalable
                                ? <span className={`${preCls}-pop-content-renewal`}>
                                    <IconTip
                                        content="当前定制任务在有效期内，此按钮不可点；当定制任务有效期临近到期前30天内，此按钮可点，点击此按钮续期，每次默认续期一年。"
                                    />
                                </span>
                                : null
                            }
                        </Menu.Item>
                        <Menu.Item key="del" >删除</Menu.Item>
                    </Menu>
                }
            >
                <IconEllipsisVertical className={`${preCls}-icon`} />
            </Tooltip>
        </div>);
    }
}
