import React, {PureComponent} from 'react';
import {Tag, Tooltip} from '@baidu/one-ui';
import {maxEmails} from '../../config';
import {isEmail} from '../../handler';
import {partial} from 'lodash';

export default class MulitEmail extends PureComponent {
    state = {
        inputEmails: ''
    }
    onInputChange = e => {
        this.setState({
            inputEmails: e.target.value
        });
    }
    keyDown = e => {
        if (e.keyCode === 13) {
            const {onInput, emails: mails} = this.props;
            const emails = [...mails];
            const {inputEmails} = this.state;
            const emailArr = inputEmails.trim().split(';');
            emailArr.forEach(v => {
                if (v && !emails.includes(v) && emails.length < maxEmails) {
                    emails.push(v);
                }
            });

            this.setState({
                inputEmails: ''
            });
            onInput({mails: emails, mailsError: this.validateInput(emails)});
        }
    }
    onClear = v => {
        const {onInput, emails} = this.props;
        const mails = emails.filter(email => email !== v);
        onInput({mails, mailsError: this.validateInput(mails)});
    }
    validateInput = emails => {
        for (let email of emails) {
            if (!isEmail(email)) {
                return '存在非法邮箱';
            }
        }
        return '';
    }
    render() {
        const {emails, className} = this.props;
        const {inputEmails} = this.state;

        const contentCls = `${className}-mail-content`;
        const tagsCls = `${contentCls}-tags`;
        const tagTextCls = `${contentCls}-tags-text`;
        const mailErr = `${contentCls}-required`;
        const inputCls = `${contentCls}-input`;
        return (<div className={contentCls}>
            <div className={tagsCls}>
                {emails.map(v => {
                    return (
                        <Tag
                            key={v}
                            closable
                            bordered={false}
                            size="small"
                            onClose={partial(this.onClear, v)}
                        >
                            {
                                isEmail(v)
                                    ? <span
                                        className={tagTextCls}
                                        title={v}
                                    >{v}</span>
                                    : <Tooltip
                                        getPopupContainer={() => document.querySelector('.custom-report-sdk-form')}
                                        title={(
                                            <div className={mailErr}>
                                                您的邮箱格式不符合规范，请检查您的设置。
                                            </div>
                                        )}
                                    >
                                        <span className={`${tagTextCls} ${mailErr}`} >
                                            {v}
                                        </span>
                                    </Tooltip>
                            }
                        </Tag>
                    );
                })}
            </div>
            <div className={inputCls}>
                <input
                    value={inputEmails}
                    onChange={this.onInputChange}
                    onKeyDown={this.keyDown}
                    disabled={emails.length === maxEmails}
                    placeholder={emails.length === maxEmails
                        ? `您最多可添加${maxEmails}个邮箱` : '同时输入多个邮箱请以;分隔'}
                />
                <span>{emails.length} / {maxEmails}</span>
            </div>
        </div>);
    }
}