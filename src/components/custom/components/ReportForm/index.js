import React, {Component, useMemo} from 'react';
import PropTypes from 'prop-types';
import {cloneDeep, partial} from 'lodash';
import dayjs from 'dayjs';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';
import {Button, DatePicker, Input, Cascader, Form, Toast} from '@baidu/one-ui';
import ReportSdkCustomReportService from '../../../../services/CustomServices/ReportSdkCustomReportService';
import {getApiError} from '../../../../utils/common';
import SelectControl from '../SelectControl';
import ColumnsField from './ColumnsField';
import UserPicker from './UserPicker';
import MulitEmail from './MulitEmail';
import IconTip from '../IconTip';
import {
    cls,
    timeUnits,
    filtersDefault,
    scheduleOptions,
    hourInDay,
    dayInWeek,
    dayInMonth,
    dayInQuarter,
    disableDatePickTip
} from '../../config';
import {getSaveParams, formatFilters} from '../../handler';
import {datePickShortCuts as datePickShortCutsConfig} from '../../../../config/default/shortCuts';
import {getTargetDate, formatDate, getDiffDay} from '../../../../utils/filterArea';
import {filterComponentMap} from '../filterItem/config';

dayjs.extend(quarterOfYear);

const getContainer = triggerNode => triggerNode.parentNode;

class ReportForm extends Component {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    constructor(props, context) {
        super(props, context);
        const {
            isEdit,
            initFormData: {
                reportName,
                scheduleTimeUnit = 'NONE',
                scheduleTimes = [],
                startDate = '',
                endDate = '',
                mails = [],
                dataRequest,
                selectedUsers = []
            } = {}
        } = props;
        const {
            columns = [],
            filters,
            reportType,
            timeUnit,
            splitColumn = ''
        } = dataRequest || {};
        const {
            reportModel,
            reportIndicator
        } = this.getReportTypeInfo(reportType);
        const firstSchedule = scheduleTimeUnit === dayInWeek && !scheduleTimes.length
            ? hourInDay
            : scheduleTimeUnit;

        const {
            config: {
                reportTypes,
                reportTypeConfig
            }
        } = this.context;
        const {
            defaultStartDate = '',
            defaultEndDate = '',
            filters: filtersFromConfig = []
        } = reportTypeConfig[reportModel] || {};

        this.state = {
            isFirstInitCustom: true,
            submiting: false,
            reportModel,
            reportIndicator,
            schedule: [firstSchedule, ...scheduleTimes],
            splitColumnsInfo: {
                splitable: false,
                splitColumns: []
            },
            formData: {
                columns,
                reportName,
                selectedUsers: selectedUsers.map(user => user.userId),
                // 编辑时初始化从接口拿，配置的无效
                startDate: formatDate(isEdit ? '' : defaultStartDate) || formatDate(startDate) || getTargetDate(7),
                endDate: formatDate(isEdit ? '' : defaultEndDate) || formatDate(endDate) || getTargetDate(),
                mails,
                filters: formatFilters(filters, filtersFromConfig),
                timeUnit: timeUnit || 'SUMMARY',
                splitColumn: splitColumn ? [splitColumn] : []
            }
        };
    }
    componentDidMount() {
        this.service = new ReportSdkCustomReportService(this.context);
    }
    getFiltersFromConfig = () => {
        const {
            config: {
                reportTypeConfig
            }
        } = this.context;
        const {
            reportModel
        } = this.state;
        const config = reportTypeConfig[reportModel] || {};
        const {
            filters = []
        } = config;
        return filters;
    }
    handleSubmit = e => {
        e.preventDefault();
        const {eventHub: {fire, trigger}} = this.context;
        fire('sdk.event.enter.log', {
            hitType: 'custom-form-save'
        });
        this.props.form.validateFieldsAndScroll((err, values) => {
            const {mailsError, formData, schedule} = this.state;
            if (!err && !mailsError) {
                const {hairuo: {userName, userId}, config: {appId}} = this.context;
                const {
                    initFormData: {
                        reportId
                    } = {},
                    mccInfo,
                    isEdit
                } = this.props;
                const params = getSaveParams({
                    appId,
                    ...(isEdit ? {reportId} : {}),
                    reportType: this.getReportType(),
                    schedule,
                    formData: {...formData, reportName: values.reportName},
                    mccInfo,
                    userName,
                    userId,
                    filtersFromConfig: this.getFiltersFromConfig()
                });

                this.setState({submiting: true});
                this.service.saveCustomReport({params}).then(res => {
                    this.setState({submiting: false});
                    trigger('sdk.event.table.loadData');
                    trigger('sdk.event.addCustom.handleTableAction', {data: {visible: false}});
                    Toast.success({
                        content: isEdit ? '保存成功！' : '新建成功！',
                        showCloseIcon: false
                    });
                }, err => {
                    this.setState({submiting: false});
                    getApiError(err, {useToast: true});
                });
            }
        });
    }
    handleCloseForm = () => {
        const {eventHub: {fire, trigger}} = this.context;
        fire('sdk.event.enter.log', {
            hitType: 'custom-form-close'
        });
        trigger('sdk.event.addCustom.handleTableAction', {data: {visible: false}});
    }
    getReportTypeInfo = reportType => {
        const {
            config: {
                reportTypes = [],
                reportTypeConfig = {}
            }
        } = this.context;
        let res;
        if (reportTypeConfig[reportType]) {
            res = {
                reportModel: reportType
            };
        } else if (reportTypeConfig['target-children']) {
            for (let v of reportTypeConfig['target-children'].reportIndicators) {
                if (v.value === reportType) {
                    res = {
                        reportModel: 'target-children',
                        reportIndicator: [reportType]
                    };
                    break;
                } else if (v.children) {
                    for (let vChild of v.children) {
                        if (vChild.value === reportType) {
                            res = {
                                reportModel: 'target-children',
                                reportIndicator: [v.value, reportType]
                            };
                            break;
                        }
                    }
                }
            }
        }
        if (!res) {
            res = {
                reportModel: reportTypes[0].value,
                reportIndicator: reportTypeConfig['target-children']
                    && reportTypeConfig['target-children'].defaultIndicator
            };
        }
        return res;
    }
    getReportType = () => {
        const {reportModel, reportIndicator} = this.state;
        const {
            config: {
                reportTypeConfig
            }
        } = this.context;
        const reportIndicators = (reportTypeConfig[reportModel] || {}).reportIndicators;
        if (reportIndicators) {
            return reportIndicator[reportIndicator.length - 1];
        }
        return reportModel;
    }
    getSendTipBySchedule = schedule => {
        const [classify, selected] = schedule;
        const DAYJS_FORMAT_CONFIG = 'YYYY-MM-DD';
        switch (classify) {
            case hourInDay:
                return `报告将在每天为您发送一次，内容为前一天0-24点的数据，下次发送日期是${dayjs().add(1, 'day').format(DAYJS_FORMAT_CONFIG)}`;
            case dayInWeek: {
                const addTime = dayjs().day() < selected ? selected - dayjs().day() : 7 -  dayjs().day() + selected;
                return `报告将在每周您设置的日期发送，内容为截至前7天数据，下次发送日期是${
                    dayjs().add(addTime, 'day').format(DAYJS_FORMAT_CONFIG)
                }`;
            }
            case dayInMonth: {
                let sendDate = '';
                let selectedDate = selected > 0 ? selected : dayjs().daysInMonth() + selected + 1;
                const curDay = dayjs().date();
                if (curDay >= selectedDate) {
                    // 取下个月的value
                    const nextMonthSelectedDate =  selected > 0
                        ? selected : dayjs().add(1, 'month').daysInMonth() + selected + 1;
                    sendDate = dayjs().startOf('month').add(1, 'month').set('date', nextMonthSelectedDate);
                } else if (curDay < selectedDate) {
                    // 取当月的
                    sendDate = dayjs().startOf('month').set('date', selectedDate);
                }
                return `报告将在每月您设置的日期发送，内容为截至前一月结束之时数据，下次发送日期是${
                    sendDate.format(DAYJS_FORMAT_CONFIG)
                }`;
            }
            case dayInQuarter: {
                let sendDate = '';
                let selectedDate = selected > 0 ? selected : dayjs().endOf('quarter').daysInMonth() + selected + 1;
                const selectedMonth = selected > 0
                    ? dayjs().startOf('quarter').month()
                    : dayjs().endOf('quarter').month();
                const curMonth = dayjs().month();
                const curDay = dayjs().date();
                if ((curDay >= selectedDate && curMonth === selectedMonth) || curMonth > selectedMonth) {
                    // 取下个季度第一个月已选的date
                    const nextQuarterSelectedDate = selected > 0
                        ? selected : dayjs().endOf('quarter').add(1, 'quarter').daysInMonth() + selected + 1;
                    sendDate = selected > 0
                        ? dayjs().startOf('quarter').add(1, 'quarter').set('date', nextQuarterSelectedDate)
                        : dayjs().endOf('quarter').add(1, 'quarter').set('date', nextQuarterSelectedDate);
                } else {
                    sendDate = selected > 0
                        ? dayjs().startOf('quarter').set('date', selectedDate)
                        : dayjs().endOf('quarter').set('date', selectedDate);
                }
                return `报告将在每季您设置的日期发送，内容为截至前一月结束之时数据，下次发送日期是${
                    sendDate.format(DAYJS_FORMAT_CONFIG)
                }`;
            }
            default:
                return '周期定制报告会按照您选择的频率发送至您的账户内';
        }
    }
    handleModelChange = reportModel => {
        const {
            config: {
                reportTypeConfig
            }
        } = this.context;

        const {
            defaultStartDate = '',
            defaultEndDate = ''
        } = reportTypeConfig[reportModel] || {};

        const columns = [];
        const startDate = formatDate(defaultStartDate) || getTargetDate(7);
        const endDate = formatDate(defaultEndDate) || getTargetDate();
        const formData = this.state.formData;
        this.props.form.setFieldsValue({
            columns,
            dateRange: `${startDate}-${endDate}-${formData.selectedUsers.join(',')}`
        });

        this.setState({
            reportModel,
            formData: {
                ...formData,
                columns: [],
                timeUnit: 'SUMMARY',
                filters: {},
                splitColumn: [],
                splitColumnsInfo: {
                    splitable: false,
                    splitColumns: []
                },
                startDate,
                endDate
            }
        });
    }
    handleReportIndicatorChange = value => {
        this.setState({reportIndicator: value});
    }
    onColumnsChange = columns => {
        const {isEdit, isCopy, form} = this.props;
        if ((isEdit || isCopy) && this.state.isFirstInitCustom) {
            this.setState({isFirstInitCustom: false});
            return;
        }
        form.setFieldsValue({columns: columns.join(',')});
        this.setState({
            formData: {
                ...this.state.formData,
                columns
            }
        });
    }
    handleTimeUnitChange = timeUnit => {
        this.setState({
            formData: {
                ...this.state.formData,
                timeUnit
            }
        });
    }
    handleFiltersChange = (field, value) => {
        const formData = this.state.formData;
        this.setState({
            formData: {
                ...formData,
                filters: {
                    ...formData.filters,
                    [field]: value
                }
            }
        });
    }
    handleDateChange = ([startDate, endDate]) => {
        const formData = this.state.formData;
        this.props.form.setFieldsValue({dateRange: `${startDate}-${endDate}-${formData.selectedUsers.join(',')}`});
        this.setState({
            formData: {
                ...formData,
                startDate,
                endDate
            }
        });
    }
    handleScheduleChange = schedule => {
        this.setState({schedule});
    }
    handlePickUsers = selectedUsers => {
        const formData = this.state.formData;
        this.props.form.setFieldsValue({
            selectedUsers: selectedUsers.join(','),
            dateRange: `${formData.startDate}-${formData.endDate}-${selectedUsers.join(',')}`
        });

        this.setState({
            formData: {
                ...formData,
                selectedUsers
            }
        });
    }
    handleMails = ({mails, mailsError}) => {
        const formData = this.state.formData;
        this.setState({
            mailsError,
            formData: {
                ...formData,
                mails
            }
        });
    }
    onSetSplitColumnsInfo = splitColumnsInfo => {
        this.setState({splitColumnsInfo});
    }
    handleSplitColumnChange = splitColumn => {
        const formData = this.state.formData;
        this.setState({
            formData: {
                ...formData,
                splitColumn
            }
        });
    }
    render() {
        const {
            isEdit,
            initFormData = {},
            mccInfo
        } = this.props;
        const {expireTime} = initFormData;
        const {isMcc} = mccInfo || {};
        const {
            submiting,
            reportModel,
            reportIndicator,
            schedule,
            mailsError,
            splitColumnsInfo: {
                splitable,
                splitColumns
            },
            formData: {
                columns,
                reportName,
                startDate,
                endDate,
                mails = [],
                selectedUsers,
                timeUnit,
                filters: formFilters = {},
                splitColumn
            }
        } = this.state;
        const {
            config: {
                reportTypes,
                reportTypeConfig,
                columnsSplitable
            }
        } = this.context;
        const customColumnsProps = {
            columns,
            reportType: this.getReportType(),
            onColumnsChange: this.onColumnsChange,
            onSetSplitColumnsInfo: this.onSetSplitColumnsInfo
        };
        const saveBtnProps = {
            type: 'primary',
            onClick: this.handleSubmit,
            loading: submiting
        };
        const formCls = `${cls}-form`;
        const cancelBtnProps = {
            className: `${formCls}-action-cancel`,
            onClick: this.handleCloseForm
        };
        const formGroupCls = `${formCls}-group`;
        const formGroupTitleCls = `${formGroupCls}-title`;
        const formGroupContentCls = `${formGroupCls}-content`;
        const formRowCls = `${formGroupContentCls}-row`;
        const formTipCls = `${formGroupContentCls}-tip`;
        const formLabelCls = `${formGroupContentCls}-label`;
        const formLabelReqCls = `${formGroupContentCls}-label-require`;
        const formControlCls = `${formGroupContentCls}-control`;
        const {getFieldDecorator} = this.props.form;
        const columnsValue = columns.join(',');
        const config = reportTypeConfig[reportModel] || {};
        const {
            reportIndicators,
            timeUnitNotSupport = {},
            filters = [],
            desc,
            validateMinDate,
            validateMaxDate,
            datePickShortCuts,
            // 用于要下线的报告，新建时只能选一次性发送
            scheduleDisabled = false,
            mccMaxAccount = 10000,
            mccMaxAccountDaysLimit = {}
        } = config;
        const userPickerProps = {
            selectedUsers,
            mccInfo,
            mccMaxAccount,
            onPickUsers: this.handlePickUsers
        };
        const isNotsupportAll = timeUnitNotSupport.ALL === true;
        const timeUnitOptions = !isNotsupportAll
            && timeUnits.filter(timeUnit => timeUnitNotSupport[timeUnit.value] !== true);
        const isSplitable = columnsSplitable !== false && splitable && splitColumns.length;
        const splitValue = splitColumn[0] && !splitColumns.filter(item => item.value === splitColumn[0]).length
            ? ['region', splitColumn[0]]
            : splitColumn;
        const cascaderProps = {
            placeholder: '细分',
            value: splitValue,
            options: splitColumns,
            onChange: this.handleSplitColumnChange,
            width: 200,
            getPopupContainer: getContainer,
            allowClear: true,
            displayRender: label => {
                const content = label && label.length ? `细分：${label.join('-')}` : '';
                return <span title={content}>{content}</span>;
            }
        };
        const reportTypeOptions = cloneDeep(reportTypes)
            .reduce((memo, item) => {
                if (item.selectable !== false || isEdit) {
                    const configInfo = reportTypeConfig[item.value];
                    if (configInfo && configInfo.mccMaxAccount && selectedUsers.length > configInfo.mccMaxAccount) {
                        item.disabled = true;
                    }
                    memo.push(item);
                }
                return memo;
            }, []);
        const diableDatePick = schedule[0] !== 'NONE';
        return (<div className={formCls}>
            <Form>
                <div className={formGroupCls}>
                    <div className={formGroupTitleCls}>模板设置</div>
                    <div className={formGroupContentCls}>
                        <div className={formRowCls}>
                            <div className={formLabelCls}>
                                <span className={formLabelReqCls}>*</span>
                                选择报告模板
                            </div>
                            <div className={`${formControlCls} flex-dir-column`}>
                                <SelectControl
                                    disabled={isEdit}
                                    value={reportModel}
                                    options={reportTypeOptions}
                                    onChange={this.handleModelChange}
                                />
                                {!!desc && (
                                    <div className={`${formGroupContentCls}-type-tip`}>{desc}</div>
                                )}
                            </div>
                        </div>
                        {reportIndicators ? <div className={formRowCls}>
                            <div className={formLabelCls}>
                                <span className={formLabelReqCls}>*</span>
                                定向维度
                            </div>
                            <div className={formControlCls}>
                                <Cascader
                                    getPopupContainer={getContainer}
                                    allowClear={false}
                                    disabled={isEdit}
                                    width={200}
                                    value={reportIndicator}
                                    options={reportIndicators}
                                    onChange={this.handleReportIndicatorChange}
                                />
                            </div>
                        </div> : null}
                        <div className={`${formRowCls} text-line`}>
                            <div className={`${formLabelCls} text-line`}>
                                <span className={formLabelReqCls}>*</span>
                                自定义指标
                            </div>
                            <div className={formControlCls}>
                                <div>
                                    <div className={`${formGroupContentCls}-columns-tip`}>您可以定制报告中所需要的指标</div>
                                    <div>
                                        <ColumnsField {...customColumnsProps} />
                                        <Form.Item>
                                            {getFieldDecorator('columns', {
                                                initialValue: columnsValue,
                                                rules: [{required: true, message: '请选择自定义指标'}]
                                            })(<div value={columnsValue}></div>)}
                                        </Form.Item>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className={formRowCls}>
                            <div className={formLabelCls}>
                                <span className={formLabelReqCls}>*</span>
                                报告名称
                            </div>
                            <div className={formControlCls}>
                                <Form.Item>
                                    {getFieldDecorator('reportName', {
                                        initialValue: reportName,
                                        rules: [
                                            {required: true, message: '请输入报告名称'},
                                            {min: 1, max: 36, message: '支持1-36字符'}
                                        ]
                                    })(<Input value={reportName} maxLen={36} placeholder="1-36个字符" />)}
                                </Form.Item>
                            </div>
                        </div>
                        {isMcc ? <div className={formRowCls}>
                            <div className={formLabelCls}>
                                <span className={formLabelReqCls}>*</span>
                                选择账户
                            </div>
                            <div className={formControlCls}>
                                <div>
                                    <UserPicker {...userPickerProps} />
                                    <Form.Item>
                                        {getFieldDecorator('selectedUsers', {
                                            initialValue: selectedUsers.join(','),
                                            rules: [{required: true, message: '请选择账户'}]
                                        })(<div value={selectedUsers.join(',')} />)}
                                    </Form.Item>
                                </div>
                            </div>
                        </div> : null}
                    </div>
                </div>
                <div className={formGroupCls}>
                    <div className={formGroupTitleCls}>频率设置</div>
                    <div className={formGroupContentCls}>
                        <div className={formRowCls}>
                            <div className={formLabelCls}>
                                更新频率
                            </div>
                            <div className={formControlCls}>
                                <div>
                                    <Cascader
                                        allowClear={false}
                                        value={schedule}
                                        getPopupContainer={triggerNode => triggerNode.parentNode}
                                        width={200}
                                        disabled={scheduleDisabled}
                                        options={scheduleOptions}
                                        onChange={this.handleScheduleChange}
                                    />
                                    <span className={formTipCls}>{this.getSendTipBySchedule(schedule)}</span>
                                </div>
                            </div>
                        </div>
                        <div className={formRowCls}>
                            <div className={formLabelCls}>
                                定期发送
                            </div>
                            <div className={formControlCls}>
                                <div>
                                    <Form.Item
                                        required
                                        validateStatus={mailsError ? 'error' : 'success'}
                                        help={mailsError}
                                    >
                                        <MulitEmail
                                            className={formControlCls}
                                            emails={mails}
                                            onInput={this.handleMails}
                                        />
                                    </Form.Item>
                                </div>
                            </div>
                        </div>
                        <div className={`${formRowCls} text-line`}>
                            <div className={`${formLabelCls} text-line`}>
                                有效期
                            </div>
                            <div className={formControlCls}>
                                <div>
                                    一年{isEdit ? <span className={formTipCls}>有效期至：{expireTime}</span> : null}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className={formGroupCls}>
                    <div className={formGroupTitleCls}>数据筛选</div>
                    <div className={formGroupContentCls}>
                        <div className={formGroupContentCls}>
                            <div className={formRowCls}>
                                <div className={formLabelCls}>
                                    <span className={formLabelReqCls}>*</span>
                                    时间范围
                                </div>
                                <div className={formControlCls}>
                                    <div>
                                        <div className={`${formControlCls}-date-pick`}>
                                            <DatePicker.RangePicker
                                                disabled={diableDatePick}
                                                validateMinDate={
                                                    formatDate(validateMinDate)
                                                    || getTargetDate(
                                                        dayjs().diff(dayjs().subtract(27, 'month'), 'day')
                                                    )
                                                }
                                                validateMaxDate={formatDate(validateMaxDate) || getTargetDate(0)}
                                                shortcuts={datePickShortCuts || datePickShortCutsConfig}
                                                value={[startDate, endDate]}
                                                onChange={this.handleDateChange}
                                                getPopupContainer={triggerNode => triggerNode.parentNode}
                                            />
                                        </div>
                                        {!!diableDatePick && (<span className={`${formControlCls}-icon`}>
                                            <IconTip
                                                content={disableDatePickTip}
                                                getPopupContainer={triggerNode => triggerNode.parentNode}
                                            />
                                        </span>)}
                                        <div>
                                            <Form.Item>
                                                {getFieldDecorator('dateRange', {
                                                    initialValue: `${startDate}-${endDate}-${selectedUsers.join(',')}`,
                                                    rules: [{validator: (rule, value, callback) => {
                                                        const [startDate, endDate] = value.split('-');
                                                        const {mccMaxAccount, daysLimit} = mccMaxAccountDaysLimit;
                                                        const daysRange = getDiffDay(startDate, endDate);
                                                        if (
                                                            (mccMaxAccount
                                                                && daysLimit
                                                                && selectedUsers.length > mccMaxAccount)
                                                                && (daysRange >= daysLimit)
                                                        ) {
                                                            callback(`时间范围不可超过${daysLimit}天`);
                                                        } else {
                                                            callback();
                                                        }
                                                    }}]
                                                })(<div
                                                    value={`${startDate}-${endDate}-${selectedUsers.join(',')}`}
                                                />)}
                                            </Form.Item>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {!isNotsupportAll ? <div className={formRowCls}>
                                <div className={formLabelCls}>
                                    时间单位
                                </div>
                                <div className={formControlCls}>
                                    <SelectControl
                                        value={timeUnit}
                                        options={timeUnitOptions}
                                        onChange={this.handleTimeUnitChange}
                                    />
                                </div>
                            </div> : null}
                            {filters.length ? <div className={formRowCls}>
                                <div className={formLabelCls}>
                                    更多筛选维度
                                </div>
                                <div className={formControlCls}>
                                    <div>
                                        {filters.map(filter => {
                                            const isFilterConfigObj = typeof filter === 'object';
                                            const filterInfo = isFilterConfigObj
                                                ? {
                                                    ...(filtersDefault[filter.field] || {}),
                                                    ...filter
                                                }
                                                : filtersDefault[filter];

                                            const fieldNotSupport = filterInfo.fieldNotSupport || {};

                                            const field = filterInfo.field;
                                            const value = formFilters[field] || filterInfo.defaultValue;
                                            const nameMap = {};
                                            const options = filterInfo.option?.filter(item => {
                                                nameMap[item.value] = item.label;
                                                return fieldNotSupport[item.value] !== true;
                                            });

                                            const Component = filterComponentMap[field] || SelectControl;

                                            return (<Component
                                                key={field}
                                                disabled={isEdit}
                                                className={`${formControlCls}-more-filter`}
                                                value={value}
                                                customRenderTarget={value => `${filterInfo.name}：${nameMap[value]}`}
                                                options={options}
                                                onChange={partial(this.handleFiltersChange, field)}
                                                style={filterInfo.style}
                                            />);
                                        })}
                                    </div>
                                </div>
                            </div> : null}
                            {
                                isSplitable ? <div className={formRowCls}>
                                    <div className={formLabelCls}>
                                        细分
                                    </div>
                                    <div className={formControlCls}>
                                        <Cascader {...cascaderProps} />
                                    </div>
                                </div> : null
                            }
                        </div>
                    </div>
                </div>
                <div className={`${formCls}-action`}>
                    <Button {...saveBtnProps} >保存设置</Button>
                    <Button {...cancelBtnProps} >取消</Button>
                </div>
            </Form>
        </div>);
    }
}
export default Form.create({name: 'report_from'})(ReportForm);
