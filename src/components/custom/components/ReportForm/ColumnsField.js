import React, {Fragment, Component} from 'react';
import produce from 'immer';
import PropTypes from 'prop-types';
import {Transfer, Popover} from '@baidu/one-ui';
import ReportSdkCustomReportService from '../../../../services/CustomServices/ReportSdkCustomReportService';
import {getApiError} from '../../../../utils/common';
import {regionNameMap, regionColumns} from '../../config';
import {flattenConflictFields} from '../../../../utils/customConfig';
import {getConflictFieldsConfig} from '../../../../utils/table';

const CandidateItem = item => {
    const {title, disabled, isConflicted, conflictContent} = item;
    return (
        <Fragment>
            {
                disabled && isConflicted
                    ? (<Popover content={conflictContent}>
                        <span>{title}</span>
                    </Popover>)
                    : <span>{title}</span>

            }
        </Fragment>
    );
};

const getConfiltResolvedAllDataMapAndConflictNoAbleSelectedFields = ({
    allDataMap,
    conflictFields,
    unAbleSelectedConflictFields,
    selectedColumns,
    columnConfigs
}) => {
    const mutexFileds = flattenConflictFields({
        selectedFields: selectedColumns,
        conflictFields
    });
    const immutableAllDataMap = produce(allDataMap, draft => {
        unAbleSelectedConflictFields.forEach(field => {
            if (draft[field]) {
                draft[field].disabled = false;
                draft[field].isConflicted = false;
            }
        });
        if (mutexFileds.length > 0) {
            mutexFileds.forEach(column => {
                if (draft[column]) {
                    draft[column].disabled = true;
                    draft[column].isConflicted = true;
                    // 设置互斥话术
                    const {conflictColumns = []} = columnConfigs[column] || {};
                    draft[column].conflictContent = `该选项与"${
                        conflictColumns.filter(column => {
                            return selectedColumns.includes(column) && columnConfigs[column].columnText;
                        }).map(column => columnConfigs[column].columnText).join('、')
                    }"互斥`;
                }
            });
        }
    });
    return [immutableAllDataMap, mutexFileds];
};

export default class ColumnsField extends Component {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }
    constructor(props, context) {
        super(props, context);
        this.state = {
            columnConfigs: {},
            loadingMeta: true,
            conflictFields: [], // 互斥的指标，例如： [['npm', 'cost'], ['click', 'cost']]
            unAbleSelectedConflictFields: [],
            defaultVisibleColumns: [],
            allDataMap: {},
            candidateList: []
        };
    }
    componentDidMount() {
        this.service = new ReportSdkCustomReportService(this.context);
        this.getMetaData(this.props);
    }
    shouldComponentUpdate = (nextProps, prevState) => {
        const nextReportType = nextProps.reportType;
        if (this.props.reportType !== nextReportType) {
            this.getMetaData(nextProps);
        }
        return true;
    }
    componentDidUpdate(prevProps) {
        if (this.props.columns !== prevProps.columns) {
            const [allDataMap, fields] = getConfiltResolvedAllDataMapAndConflictNoAbleSelectedFields({
                ...this.state,
                selectedColumns: this.props.columns
            });
            this.setState({
                unAbleSelectedConflictFields: fields,
                allDataMap
            });
        }
    }
    getMetaData = async ({reportType}) => {
        this.setState({loadingMeta: true});
        try {
            const params = {
                token: this.context.token,
                reportType
            };
            const {
                data: {
                    columnConfigs,
                    columnCategories,
                    defaultVisibleColumns,
                    splitable,
                    splitColumns
                } = {}
            } = await this.service.getTableMetaData({params});
            const allDataMap = {};
            const candidateList = [];
            // 必选字段
            const requiredCols = [];
            defaultVisibleColumns.map(item => !columnConfigs[item].optional && requiredCols.push(item));
            const validCostumColumns = [];
            for (let index in columnCategories) {
                const item = columnCategories[index];
                const {columns} = item || {};
                allDataMap[index] = {
                    key: index,
                    title: item.name,
                    children: []
                };
                if (columns && columns.length) {
                    for (let column of columns) {
                        const columnInfo = columnConfigs[column];
                        if (columnInfo) {
                            const {columnText, conflictColumns = []} = columnInfo || {};
                            validCostumColumns.push(column);
                            allDataMap[column] = {
                                key: column,
                                title: columnText
                            };
                            // 将必选字段置灰
                            if (requiredCols.includes(column)) {
                                allDataMap[column].disabled = true;
                            }
                            allDataMap[index].children.push(column);
                        }
                    }
                }
                candidateList.push(index);
            }
            const columns = defaultVisibleColumns.filter(item => allDataMap[item]);
            const conflictFields = getConflictFieldsConfig(columnConfigs, validCostumColumns);
            const [
                confiltResolvedallDataMap,
                conflictedNoAbleSelectedfields
            ] = getConfiltResolvedAllDataMapAndConflictNoAbleSelectedFields({
                allDataMap,
                conflictFields,
                unAbleSelectedConflictFields: this.state.unAbleSelectedConflictFields,
                selectedColumns: columns
            });
            this.setState({
                columnConfigs,
                defaultVisibleColumns: columns,
                allDataMap: confiltResolvedallDataMap,
                unAbleSelectedConflictFields: conflictedNoAbleSelectedfields,
                conflictFields,
                candidateList: candidateList.filter(item => allDataMap[item] && allDataMap[item].children.length)
            });
            this.props.onColumnsChange(columns);
            this.handleSetSplitColumnsInfo({
                splitable,
                splitColumns,
                columnConfigs
            });
        } catch (err) {
            getApiError(err, {useToast: true});
        }
        this.setState({loadingMeta: false});
    }
    handleSetSplitColumnsInfo = ({
        splitable,
        splitColumns,
        columnConfigs
    }) => {
        const options = [];
        const regionList = [];
        splitColumns.map(column => {
            if (regionColumns.indexOf(column) > -1) {
                regionList.push(column);
            }
            else {
                const data = columnConfigs && columnConfigs[column] || {};
                const {columnText} = data;
                options.push({
                    label: columnText,
                    value: column
                });
            }
        });
        if (regionList.length) {
            options.push({
                label: '地域',
                value: 'region',
                children: regionList.map(r => {
                    return {
                        label: regionNameMap[r],
                        value: r
                    };
                })
            });
        }
        this.props.onSetSplitColumnsInfo({
            splitable,
            splitColumns: options
        });
    }
    handleSelectChange = columns => {
        this.props.onColumnsChange(columns);
    }
    render() {
        const {loadingMeta, allDataMap, candidateList, conflictFields} = this.state;
        const {columns} = this.props;
        const transformProps = {
            treeName: '指标',
            loading: loadingMeta,
            candidateList: candidateList,
            allDataMap,
            CandidateItem,
            selectedList: columns,
            handleSelect: this.handleSelectChange,
            handleSelectAll: this.handleSelectChange,
            handleDelete: this.handleSelectChange,
            handleDeleteAll: this.handleSelectChange,
            showSearchBox: false,
            showSelectAll: !conflictFields.length
        };
        return (<div>
            <Transfer {...transformProps} />
        </div>);
    }
}