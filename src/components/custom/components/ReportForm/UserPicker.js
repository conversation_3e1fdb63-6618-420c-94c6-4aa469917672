import React, {PureComponent} from 'react';
import {Transfer, Overlay, Button} from '@baidu/one-ui';
import {cls} from '../../config';
import {getUserPickData} from '../../handler';

const initState = {
    pickedUsers: [],
    isChanged: false,
    visible: false,
    searchValue: ''
};

const confirmStyle = {
    marginRight: '12px'
};

export default class UserPicker extends PureComponent {
    state = {
        ...initState
    }
    resetState = () => {
        this.setState(initState);
    }
    handlePickUsers = pickedUsers => {
        this.setState({pickedUsers, isChanged: true});
    }
    handleVisibleChange = visible => {
        if (visible === false) {
            this.resetState();
            return;
        }
        this.setState({visible});
    }
    handleConfirm = () => {
        this.props.onPickUsers(this.state.pickedUsers);
        this.setState({visible: false});
    }
    handleSearchChange = e => {
        this.setState({
            searchValue: e.target.value
        });
    }
    render() {
        const {pickedUsers, isChanged, visible, searchValue} = this.state;
        const {
            selectedUsers,
            mccInfo,
            mccMaxAccount
        } = this.props;
        const {allDataMap, candidateList} = getUserPickData(mccInfo);
        const transferProps = {
            treeName: '账户',
            maxSelectedNum: mccMaxAccount,
            searchValue,
            candidateList: searchValue
                ? candidateList.filter(item => {
                    return (allDataMap[item].title || '').includes(searchValue);
                })
                : candidateList,
            allDataMap,
            selectedList: !isChanged ? selectedUsers : pickedUsers,
            handleSelect: this.handlePickUsers,
            handleSelectAll: this.handlePickUsers,
            handleDelete: this.handlePickUsers,
            handleDeleteAll: this.handlePickUsers,
            onSearchChange: this.handleSearchChange
        };
        const confirmProps = {
            type: 'primary',
            onClick: this.handleConfirm,
            style: confirmStyle
        };
        const pickContentCls = `${cls}-form-group-content-control-user-picker-content`;
        const content = (<div className={pickContentCls}>
            <div className={`${pickContentCls}-title`}>请选择账户</div>
            <Transfer {...transferProps} />
            <div className={`${pickContentCls}-button-container`}>
                <Button {...confirmProps} >确定</Button>
                <Button onClick={this.resetState}>取消</Button>
            </div>
        </div>);
        const title = selectedUsers.reduce((memo, userId, index) => {
            let userName = allDataMap[userId] && allDataMap[userId].title;
            if (index !== 0) {
                userName = ',' + userName;
            }
            memo += userName;
            return memo;
        }, '');
        const header = (<div className={`${cls}-form-group-content-control-user-picker`} title={title}>
            {selectedUsers.length ? title : '请选择账户'}
        </div>);
        return (<Overlay
            visible={visible}
            overlay={content}
            trigger="click"
            dropdownMatchSelectWidth={false}
            header={header}
            onVisibleChange={this.handleVisibleChange}
            getPopupContainer={triggerNode => triggerNode.parentNode}
        />);
    }
}