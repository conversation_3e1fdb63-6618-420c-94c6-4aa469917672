import React from 'react';
import {Popover} from '@baidu/one-ui';
import {IconQuestionCircle} from 'dls-icons-react';
import {cls} from '../../config';

export default ({
    title,
    content,
    className = '',
    children,
    preCls = `${cls}-tip-icon`,
    placement = 'bottom',
    ...restPopoverProps
}) => {
    const popoverProps = {
        overlayClassName: `${className} ${preCls}-popover`,
        placement,
        content: <div className={`${className} ${preCls}-content`}>{content}</div>,
        ...restPopoverProps
    };
    if (title) {
        popoverProps.title = (
            <div className={`${className} ${preCls}-title`}>
                {title}
            </div>
        );
    }
    return (
        <Popover {...popoverProps}>
            {children ? children : <IconQuestionCircle className={`${className} ${preCls}`} />}
        </Popover>
    );
};

