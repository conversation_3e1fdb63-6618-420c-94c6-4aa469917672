/**
 * @file 组件类型
 * <AUTHOR>
 * @date 2020/08/31
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Dropdown, CascaderPane, Button} from '@baidu/one-ui';
import {IconChevronDown, IconChevronUp} from '@baidu/one-ui-icon';

export default class SecondComponentType extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    constructor(props) {
        super(props);
        this.state = {
            value: [],
            options: [],
            checkedKeys: [],
            visible: false,
            selectedKeys: []
        };
    }

    onVisibleChange = visible => {
        this.setState({
            visible
        });
    }

    onCheckboxChange = checkedKeys => {
        this.setState({
            checkedKeys
        });
    }

    onComfirm = () => {
        const {checkedKeys} = this.state;
        const {eventHub, config} = this.context;
        const {secondComponentType} = config.filterAreaConfig.filterData;
        const {field, option} = secondComponentType;
        // 剔除掉有子级的一级标签
        const targetKeys = checkedKeys.filter(key => {
            const targetOptionList = option.filter(o => o.value === key);
            if (targetOptionList
                && targetOptionList.length
                && targetOptionList[0]
                && targetOptionList[0].children
                && targetOptionList[0].children.length
            ) {
                return false;
            }
            return true;
        });
        eventHub.trigger('sdk.event.page.filter.changed', {data: {
            [field]: {
                operator: 'IN',
                values: targetKeys
            }
        }});
        this.setState({
            visible: false,
            selectedKeys: targetKeys
        });
    }

    onCancel = () => {
        this.setState({
            visible: false
        });
    }

    getSelectedItemText = () => {
        const {filterAreaConfig} = this.context.config;
        const {option} = filterAreaConfig.filterData.secondComponentType;
        const {selectedKeys} = this.state;
        const selectedTexts = selectedKeys.map(key => {
            let text = '';
            option.filter(o => {
                const {children} = o;
                if (children && children.length) {
                    children.map(child => {
                        if (child.value === key) {
                            text = child.label;
                        }
                    });

                }
                else {
                    if (o.value === key) {
                        text = o.label;
                    }
                }
            });
            return text;
        }).join(',');
        return selectedTexts;
    }

    render() {
        const {classPrefix, filterAreaConfig} = this.context.config;
        const {option, width} = filterAreaConfig.filterData.secondComponentType;
        const {visible, checkedKeys, selectedKeys} = this.state;
        const cascaderPaneProps = {
            showCheckbox: true,
            style: {'min-height': '180px'},
            options: option,
            checkedKeys,
            onCheckboxChange: this.onCheckboxChange,
            className: `${classPrefix}-second-component-type-cascader`
        };
        const content = (
            <div className={`${classPrefix}-second-component-type-overlay-container`}>
                <CascaderPane {...cascaderPaneProps} />
                <div className="btn-container">
                    <Button type="primary" style={{marginRight: '12px'}} onClick={this.onComfirm}>确定</Button>
                    <Button onClick={this.onCancel}>取消</Button>
                </div>
            </div>
        );
        const dropdownProps = {
            overlay: content,
            visible,
            onVisibleChange: this.onVisibleChange,
            trigger: 'click'
        };
        const arrowRender = visible
            ? <span className="indicator-arrow"><IconChevronUp className="indicator-icon" /></span>
            : <span className="indicator-arrow"><IconChevronDown className="indicator-icon" /></span>;
        const selectedTexts = this.getSelectedItemText();
        const dropdownContent = (
            <div className="second-component-type-content">
                <span>组件类型</span>
                <span className="texts">：{selectedKeys?.length ? selectedTexts : '全部'}</span>
                {arrowRender}
            </div>
        );
        const dropdownContainerStyle = {width};
        return (
            <div className="filter-item second-component-type-container" style={dropdownContainerStyle}>
                <Dropdown {...dropdownProps}>
                    {dropdownContent}
                </Dropdown>
            </div>
        );
    }
}
