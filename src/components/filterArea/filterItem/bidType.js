/**
 * @file 出价模式筛选器
 * <AUTHOR>
 * @date 2020/08/12
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Select} from '@baidu/one-ui';
import {allItemValue, itemValue} from '../../../config/default/filterAreaConfig';
import partial from 'lodash/partial';
import {customRenderTarget} from '../../../utils/filterArea';

const Option = Select.Option;

export default class BidType extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    constructor(props, context) {
        super(props);
        this.state = {
            value: context.config.filterAreaConfig.filterData.bidType.defaultValue
        };
    }
    handleChange = value => {
        let data = {
            bidType: {
                operator: 'IN',
                values: [value]
            }
        };
        const {eventHub, config} = this.context;
        const {bidType} = config.filterAreaConfig.filterData;
        const {field} = bidType;
        this.setState({
            value
        });
        const currentOption
            = bidType.option.filter(item => item.value === value);
        const realValue = currentOption[0].realValue;
        if (value === allItemValue || value === itemValue) {
            data = {
                [field]: realValue
                    ? {
                        operator: 'IN',
                        values: realValue
                    }
                    : {}
            };
        }
        eventHub.trigger('sdk.event.page.filter.changed', {data});
    }

    render() {
        const {option, width} = this.context.config.filterAreaConfig.filterData.bidType;
        const {value} = this.state;
        const selectProps = {
            value,
            onChange: this.handleChange,
            trigger: 'click',
            customRenderTarget: partial(customRenderTarget, value, 'bidType', this.context),
            width
        };
        return (
            <div className="filter-item bidType-container">
                <Select {...selectProps}>
                    {
                        option.map((item, index) => {
                            return <Option value={item.value} key={index}>{item.label}</Option>;
                        })
                    }
                </Select>
            </div>
        );
    }
}
