/**
 * @file 生成样式筛选器
 * <AUTHOR>
 * @date 2020/08/13
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Cascader} from '@baidu/one-ui';
import {allItemValue, itemValue} from '../../../config/default/filterAreaConfig';

export default class feedMaterialStyleEnum extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    constructor(props, context) {
        super(props);
        this.state = {
            value: [context.config.filterAreaConfig.filterData.feedMaterialStyleEnum.defaultValue]
        };
    }
    handleChange = value => {
        let data = {};
        const {eventHub, config} = this.context;
        const {field} = config.filterAreaConfig.filterData.feedMaterialStyleEnum;
        this.setState({
            value
        });
        const currentOption
            = config.filterAreaConfig.filterData.feedMaterialStyleEnum.option.filter(item => item.value === value[0]);
        const realValue = currentOption[0].realValue;
        if (value[0] === allItemValue || value === itemValue) {
            data = {
                [field]: realValue
                    ? {
                        operator: 'IN',
                        value: realValue
                    }
                    : {}
            };
        }
        else {
            data = {
                [field]: {
                    operator: 'IN',
                    values: value.filter((item, index) => index === value.length - 1)
                }
            };
        }
        eventHub.trigger('sdk.event.page.filter.changed', {data});
    }

    render() {
        const {option, name} = this.context.config.filterAreaConfig.filterData.feedMaterialStyleEnum;
        const realOption = option.map(item => {
            const {label, value, children} = item;
            if (children) {
                const childrenOption = children.option;
                return {label, value, children: childrenOption};
            }
            return {label, value};
        });
        const cascaderProps = {
            value: this.state.value,
            options: realOption,
            onChange: this.handleChange,
            allowClear: false,
            displayRender: label => {
                const content = `${name}：${label.join('-')}`;
                return <span title={content}>{content}</span>;
            }
        };
        return (
            <div className="filter-item feedMaterialStyleEnum-container">
                <Cascader {...cascaderProps} />
            </div>
        );
    }
}
