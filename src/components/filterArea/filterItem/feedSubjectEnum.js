/**
 * @file 营销目标筛选器
 * <AUTHOR>
 * @date 2020/08/12
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Cascader} from '@baidu/one-ui';
import {allItemValue, itemValue} from '../../../config/default/filterAreaConfig';

export default class FeedSubject extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    constructor(props, context) {
        super(props);
        this.state = {
            value: [context.config.filterAreaConfig.filterData.feedSubjectEnum.defaultValue]
        };
    }
    handleValue = (currentOption, value, i, column) => {
        let data = {};
        if (value[i] === allItemValue || value[i] === itemValue) {
            const realValue = currentOption.realValue;
            data = {
                [column]: realValue
                    ? {
                        operator: 'IN',
                        values: Array.isArray(realValue) ? realValue : [realValue]
                    }
                    : {}
            };
        }
        else {
            data = {
                [column]: {
                    operator: 'IN',
                    values: Array.isArray(value[i]) ? value[i] : [value[i]]
                }
            };
        }
        return data;
    }
    handleChange = value => {
        let data = {};
        const {eventHub, config} = this.context;
        this.setState({
            value
        });
        const currentOptionList
            = config.filterAreaConfig.filterData.feedSubjectEnum.option.filter(item => item.value === value[0]);
        const currentOption = currentOptionList && currentOptionList[0] || {};
        for (let i = 0; i < value.length; i++) {
            const emptyData = {
                operator: 'IN',
                values: []
            };
            if (i === 0) {
                data = {
                    feedIdeaPlugType: emptyData,
                    ...this.handleValue(currentOption, value, i, 'feedSubjectEnum')
                };
            }
            else {
                const currentChildrenOptionList
                    = currentOption.children.option.filter(item => item.value === value[i]);
                const currentChildrenOption = currentChildrenOptionList[0];
                data = {
                    ...data,
                    ...this.handleValue(currentChildrenOption, value, i, currentOption.children.field)
                };
            }
        }
        eventHub.trigger('sdk.event.page.filter.changed', {data});
    }
    render() {
        const {option, name} = this.context.config.filterAreaConfig.filterData.feedSubjectEnum;
        const realOption = option.map(item => {
            const {label, value, children} = item;
            if (children) {
                const childrenOption = children.option;
                return {label, value, children: childrenOption};
            }
            return {label, value};
        });
        const cascaderProps = {
            value: this.state.value,
            options: realOption,
            onChange: this.handleChange,
            width: 200,
            allowClear: false,
            displayRender: label => {
                const content = `${name}：${label.join('-')}`;
                return <span title={content}>{content}</span>;
            }
        };
        return (
            <div className="filter-item feedSubject-container">
                <Cascader {...cascaderProps} />
            </div>
        );
    }
}
