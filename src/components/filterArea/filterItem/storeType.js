/**
 * @file 店铺类型筛选器
 * <AUTHOR>
 * @date 2022/06/09
 */

import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Select} from '@baidu/one-ui';
import {allItemValue, itemValue} from '../../../config/default/filterAreaConfig';
import partial from 'lodash/partial';
import {customRenderTarget} from '../../../utils/filterArea';

const Option = Select.Option;

export default class StoreType extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    constructor(props, context) {
        super(props);
        this.state = {
            value: context.config.filterAreaConfig.filterData.storeType.defaultValue
        };
    }
    handleChange = value => {
        let data = {
            storeType: {
                operator: 'IN',
                values: [value]
            }
        };
        const {eventHub, config} = this.context;
        const {storeType} = config.filterAreaConfig.filterData;
        const {field} = storeType;
        this.setState({
            value
        });
        const currentOption
            = storeType.option.filter(item => item.value === value);
        const realValue = currentOption[0].realValue;
        if (value === allItemValue || value === itemValue) {
            data = {
                [field]: realValue
                    ? {
                        operator: 'IN',
                        values: realValue
                    }
                    : {}
            };
        }
        eventHub.trigger('sdk.event.page.filter.changed', {data});
    }

    render() {
        const {option, width} = this.context.config.filterAreaConfig.filterData.storeType;
        const {value} = this.state;
        const selectProps = {
            value,
            onChange: this.handleChange,
            trigger: 'click',
            customRenderTarget: partial(customRenderTarget, value, 'storeType', this.context),
            width
        };
        return (
            <div className="filter-item storeType-container">
                <Select {...selectProps}>
                    {
                        option.map((item, index) => {
                            return <Option value={item.value} key={item.value}>{item.label}</Option>;
                        })
                    }
                </Select>
            </div>
        );
    }
}
