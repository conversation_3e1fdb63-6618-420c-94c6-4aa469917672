/**
 * @file 内容分类筛选器
 * <AUTHOR>
 * @date 2023/04/10
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Select} from '@baidu/one-ui';
import {allItemValue} from '../../../config/default/filterAreaConfig';
import partial from 'lodash/partial';
import {customRenderTarget} from '../../../utils/filterArea';

const Option = Select.Option;

export default class SubAppId extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    constructor(props, context) {
        super(props);
        this.state = {
            value: context.config.filterAreaConfig.filterData.subAppId.defaultValue
        };
    }
    handleChange = value => {
        let data = {
            subAppId: {
                operator: 'IN',
                values: [value]
            }
        };
        const {eventHub, config} = this.context;
        const {subAppId} = config.filterAreaConfig.filterData;
        const {field} = subAppId;
        this.setState({
            value
        });

        const currentOption = subAppId.option.filter(item => item.value === value);
        const realValue = currentOption[0].realValue;
        if (value === allItemValue) {
            data = {
                [field]: realValue
                    ? {
                        operator: 'IN',
                        value: realValue
                    }
                    : {}
            };
        }
        eventHub.trigger('sdk.event.page.filter.changed', {data});
    }

    render() {
        const {option, width} = this.context.config.filterAreaConfig.filterData.subAppId;
        const {value} = this.state;
        const selectProps = {
            value,
            onChange: this.handleChange,
            trigger: 'click',
            customRenderTarget: partial(customRenderTarget, value, 'subAppId', this.context),
            width
        };
        return (
            <div className="filter-item subAppId-container">
                <Select {...selectProps}>
                    {
                        option.map((item, index) => {
                            return <Option value={item.value} key={index}>{item.label}</Option>;
                        })
                    }
                </Select>
            </div>
        );
    }
}
