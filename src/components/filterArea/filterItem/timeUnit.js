/**
 * @file 时间单位筛选器
 * <AUTHOR>
 * @date 2020/08/11
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Select} from '@baidu/one-ui';
import partial from 'lodash/partial';
import {notSupportCompareUnitDefalutValue} from '../../../config/common';
import {customRenderTarget} from '../../../utils/filterArea';

const Option = Select.Option;

export default class TimeUnit extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    constructor(props, context) {
        super(props);
        const {option, defaultValue} = context.config.filterAreaConfig.filterData.timeUnit;
        this.state = {
            value: defaultValue,
            option
        };
    }

    componentDidMount() {
        const {eventHub: {on}} = this.context;
        on('sdk.event.date.change', this.handleSetChange);
    }

    handleSetChange = ({data}) => {
        const {value} = this.state;
        const {checked} = data;
        const {option, notSupportCompareUnits} = this.context.config.filterAreaConfig.filterData.timeUnit;
        const newValue = notSupportCompareUnits.indexOf(value) > -1 ? notSupportCompareUnitDefalutValue : value;
        if (checked) {
            this.setState({
                value: newValue,
                option: option.map(o => {
                    if (notSupportCompareUnits.indexOf(o.value) > -1) {
                        return {
                            ...o,
                            disabled: true
                        };
                    }
                    return o;
                })
            });
        }
        else {
            this.setState({
                option
            });
        }
    }

    handleChange = value => {
        const {eventHub} = this.context;
        this.setState({
            value
        });
        eventHub.trigger('sdk.event.page.filter.changed', {data: {
            timeUnit: value
        }});
    }
    render() {
        const {width} = this.context.config.filterAreaConfig.filterData.timeUnit;
        const {value, option} = this.state;
        const selectProps = {
            value,
            onChange: this.handleChange,
            trigger: 'click',
            customRenderTarget: partial(customRenderTarget, value, 'timeUnit', this.context),
            width
        };
        return (
            <div className="filter-item time-unit-container">
                <Select {...selectProps}>
                    {
                        option.map((item, index) => {
                            const {value, disabled, label} = item;
                            return <Option value={value} disabled={disabled} key={index}>{label}</Option>;
                        })
                    }
                </Select>
            </div>
        );
    }
}
