/**
 * @file 付费方式筛选器
 * <AUTHOR>
 * @date 2020/08/12
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Select} from '@baidu/one-ui';
import {allItemValue, itemValue} from '../../../config/default/filterAreaConfig';
import partial from 'lodash/partial';
import {customRenderTarget} from '../../../utils/filterArea';

const Option = Select.Option;

export default class OcpcPayMode extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    constructor(props, context) {
        super(props);
        this.state = {
            value: context.config.filterAreaConfig.filterData.ocpcPayMode.defaultValue
        };
    }
    handleChange = value => {
        let data = {
            ocpcPayMode: {
                operator: 'IN',
                values: [value]
            }
        };
        const {eventHub, config} = this.context;
        const {ocpcPayMode} = config.filterAreaConfig.filterData;
        const {field} = ocpcPayMode;
        this.setState({
            value
        });
        const currentOption = ocpcPayMode.option.filter(item => item.value === value);
        const realValue = currentOption[0].realValue;
        if (value === allItemValue || value === itemValue) {
            data = {
                [field]: realValue
                    ? {
                        operator: 'IN',
                        values: realValue
                    }
                    : {}
            };
        }
        eventHub.trigger('sdk.event.page.filter.changed', {data});
    }
    render() {
        const {option, width} = this.context.config.filterAreaConfig.filterData.ocpcPayMode;
        const {value} = this.state;
        const selectProps = {
            value,
            onChange: this.handleChange,
            trigger: 'click',
            customRenderTarget: partial(customRenderTarget, value, 'ocpcPayMode', this.context),
            width
        };
        return (
            <div className="filter-item ocpcPayMode-container">
                <Select {...selectProps}>
                    {
                        option.map((item, index) => {
                            return <Option value={item.value} key={index}>{item.label}</Option>;
                        })
                    }
                </Select>
            </div>
        );
    }
}
