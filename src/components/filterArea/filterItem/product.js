/**
 * @file 推广渠道筛选器
 * <AUTHOR>
 * @date 2020/12/08
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Select} from '@baidu/one-ui';
import {allItemValue} from '../../../config/default/filterAreaConfig';
import partial from 'lodash/partial';
import {customRenderTarget} from '../../../utils/filterArea';

const Option = Select.Option;

export default class Device extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    constructor(props, context) {
        super(props);
        this.state = {
            value: context.config.filterAreaConfig.filterData.product.defaultValue
        };
    }
    handleChange = value => {
        let data = {
            product: {
                operator: 'IN',
                values: [value]
            }
        };
        const {eventHub, config} = this.context;
        const {product} = config.filterAreaConfig.filterData;
        const {field} = product;
        this.setState({
            value
        });

        const currentOption = product.option.filter(item => item.value === value);
        const realValue = currentOption[0].realValue;
        if (value === allItemValue) {
            data = {
                [field]: realValue
                    ? {
                        operator: 'IN',
                        value: realValue
                    }
                    : {}
            };
        }
        eventHub.trigger('sdk.event.page.filter.changed', {data});
    }

    render() {
        const {option, width} = this.context.config.filterAreaConfig.filterData.product;
        const {value} = this.state;
        const selectProps = {
            value,
            onChange: this.handleChange,
            trigger: 'click',
            customRenderTarget: partial(customRenderTarget, value, 'product', this.context),
            width
        };
        return (
            <div className="filter-item product-container">
                <Select {...selectProps}>
                    {
                        option.map((item, index) => {
                            return <Option value={item.value} key={index}>{item.label}</Option>;
                        })
                    }
                </Select>
            </div>
        );
    }
}
