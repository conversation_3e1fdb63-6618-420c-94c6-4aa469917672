import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {But<PERSON>, Checkbox} from '@baidu/one-ui';

/* eslint-disable react/prefer-stateless-function */
export default class PageEnumFilter extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    static propTypes = {
        field: PropTypes.string,
        name: PropTypes.string,
        option: PropTypes.Array,
        pageEditingFilterData: PropTypes.object,
        pageFilterData: PropTypes.object,
        onClearActiveKey: PropTypes.func,
        onChangeMoreVisible: PropTypes.func
    }
    static defaultProps = {
        field: '',
        name: '',
        option: [],
        pageEditingFilterData: {},
        pageFilterData: {},
        onClearActiveKey() {},
        onChangeMoreVisible() {}
    }
    render() {
        const {classPrefix} = this.context.config;
        const {
            field,
            name,
            option,
            pageEditingFilterData,
            pageFilterData,
            onClearActiveKey,
            onChangeMoreVisible
        } = this.props;
        const {eventHub} = this.context;
        const {trigger} = eventHub;
        const filter = pageEditingFilterData[field] || {};
        const enterBtnProps = {
            type: 'text-strong',
            size: 'medium',
            style: {padding: 0},
            onClick: () => {
                // 过滤掉空值，但是数值0及字符串0都不能过滤
                const validValues = pageEditingFilterData[field].values.filter(v => v || String(v) === '0');
                pageEditingFilterData[field].values = validValues;
                const newPageFilterData = {
                    ...pageFilterData,
                    [field]: JSON.parse(JSON.stringify(pageEditingFilterData[field]))
                };
                trigger('sdk.event.page.filter.changed', {data: newPageFilterData});
                onClearActiveKey && onClearActiveKey();
                onChangeMoreVisible && onChangeMoreVisible('');
            }
        };
        const cancelBtnProps = {
            type: 'text-aux',
            size: 'medium',
            style: {padding: 0, marginLeft: 16},
            onClick: () => {
                trigger('sdk.event.filter.filterChangeCancel');
                onClearActiveKey && onClearActiveKey();
                onChangeMoreVisible && onChangeMoreVisible('');
            }
        };
        return (
            <div className={`${classPrefix}-filter-container ${classPrefix}-filter-container-enum`}>
                <div className="filter-title">
                    {name}
                </div>
                <div className="enum-filter-container">
                    {
                        option.map(item => {
                            const selected = filter && filter.values && filter.values.indexOf(item.value) > -1;
                            const props = {
                                checked: selected,
                                onChange: () => {
                                    const newPageEditingFilterData = {
                                        ...pageEditingFilterData,
                                        [field]: {
                                            ...filter,
                                            values: selected
                                                ? filter.values.filter(i => i !== item.value)
                                                : [].concat(filter.values, [item.value])
                                        }
                                    };
                                    trigger('sdk.event.page.filter.changing', {data: newPageEditingFilterData});
                                }
                            };
                            return (
                                <div key={item.value} style={{height: 30}}>
                                    <Checkbox {...props}>{item.label}</Checkbox>
                                </div>
                            );
                        })
                    }
                </div>
                <div className="button-container button-container-enum">
                    <div className="line-sperator" />
                    <Button {...enterBtnProps}>应用</Button>
                    <Button {...cancelBtnProps}>取消</Button>
                </div>
            </div>
        );
    }
}
