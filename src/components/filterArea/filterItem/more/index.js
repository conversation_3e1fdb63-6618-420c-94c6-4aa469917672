/**
 * @file 更多筛选器
 * <AUTHOR>
 * @date 2020/08/13
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Dropdown, Menu, Button} from '@baidu/one-ui';
import PageEnumFilter from './pageEnumFilter';

export default class BidType extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }

    constructor(props, context) {
        super(props);
        this.state = {
            visible: false,
            activeKey: null,
            leftPx: 0
        };
    }
    static propTypes = {
        pageFilterData: PropTypes.object,
        pageEditingFilterData: PropTypes.object,
        onChangeFromWhere: PropTypes.func,
        clickFromWhere: PropTypes.string,
        isMcc: PropTypes.bool
    }
    static defaultProps = {
        pageFilterData: {},
        pageEditingFilterData: {},
        onChangeFromWhere() {},
        clickFromWhere: '',
        isMcc: false
    }
    componentDidMount() {
        const {eventHub} = this.context;
        eventHub.on('sdk.event.page.filter.custom.confirm', this.onClearActiveKey);
        eventHub.on('sdk.event.page.filter.custom.cancel', this.onClearActiveKey);
    }
    onVisibleChange = visible => {
        this.setState({
            visible
        });
    }
    onClick = e => {
        const {filterAreaConfig} = this.context.config;
        const {filterData} = filterAreaConfig;
        const {onChangeFromWhere} = this.props;
        onChangeFromWhere('moreButton');
        this.setState({
            activeKey: e.key,
            visible: false
        });
        if (e.key === 'queryScope') {
            const box = document.querySelector('.report-sdk-filter-area-container');
            const inner = document.querySelector('.report-sdk-more-container');
            const boxWidth = box.offsetWidth;
            const innerLeft = inner.offsetLeft;
            const customRenderWidth = filterData[e.key] && filterData[e.key].width || 614;
            if (boxWidth - innerLeft < customRenderWidth) {
                this.setState({leftPx: boxWidth - innerLeft - customRenderWidth + 44});
            }
        }
    }
    onClearActiveKey = () => {
        this.setState({
            activeKey: null
        });
    }
    resetMore = () => {
        const {eventHub} = this.context;
        const {trigger} = eventHub;
        trigger('sdk.event.page.filter.clearAll');
    }
    getMoreBtnRef = ref => {
        this.moreBtnRef = ref;
    }
    getCustomRenderRef = ref => {
        this.customRenderRef = ref;
    }
    render() {
        const {classPrefix, filterAreaConfig} = this.context.config;
        const {
            pageEditingFilterData,
            pageFilterData,
            clickFromWhere,
            isMcc
        } = this.props;
        const {activeKey, visible, leftPx} = this.state;
        const {moreList, filterData} = filterAreaConfig;
        const activeFilter = activeKey && filterData[activeKey] || {};
        const {name, option} = activeFilter;
        const selectedMoreList = moreList.filter(item => pageFilterData
            && pageFilterData[item] && pageFilterData[item].values && pageFilterData[item].values.length > 0);
        const menu = (
            <Menu onClick={this.onClick}>
                {
                    moreList.map(item => {
                        const targetData = filterData[item] || {};
                        const disabled = selectedMoreList.indexOf(item) > -1
                            || (
                                isMcc
                                && (item === 'queryScope' || targetData.mccDisable)
                                && targetData.partDisable !== true
                            );
                        return <Menu.Item key={targetData.field} disabled={disabled}>{targetData.name}</Menu.Item>;
                    })
                }
            </Menu>
        );
        const dropdownProps = {
            trigger: 'click',
            placement: 'bottomLeft',
            overlay: menu,
            visible,
            onVisibleChange: this.onVisibleChange
        };
        const pageEnumFilterProps = {
            field: activeKey,
            name,
            option,
            pageEditingFilterData,
            pageFilterData,
            onClearActiveKey: this.onClearActiveKey
        };
        const isShowMoreButton = (selectedMoreList && selectedMoreList.length) < moreList.length;
        const targetFilterData = activeKey && filterData && filterData[activeKey] || {};
        const CustomRender = targetFilterData.customRender;
        const isCustomRenderPartDisable = isMcc
            && activeKey === 'queryScope'
            && targetFilterData.partDisable === true;
        const TargetRender = CustomRender
            ? (
                <div className="custom-render-container" style={{left: `${leftPx}px`}} ref={this.getCustomRenderRef}>
                    <CustomRender isCustomRenderPartDisable={isCustomRenderPartDisable} />
                </div>
            )
            : <PageEnumFilter {...pageEnumFilterProps} />;
        return (
            <div className={`${classPrefix}-more-container`}>
                {
                    isShowMoreButton && (
                        <Dropdown {...dropdownProps}>
                            <Button ref={this.getMoreBtnRef} type="text-strong" size="medium" style={{padding: 0}}>
                                更多筛选
                            </Button>
                        </Dropdown>
                    )
                }
                {activeKey && clickFromWhere && clickFromWhere === 'moreButton' && TargetRender}
                {
                    !!(selectedMoreList && selectedMoreList.length) && (
                        <Button
                            type="text-strong"
                            size="medium"
                            onClick={this.resetMore}
                            style={{
                                padding: 0,
                                marginLeft: 16,
                                marginRight: 16
                            }}
                        >
                            还原
                        </Button>
                    )
                }
            </div>
        );
    }
}
