/**
 * @file sdk - 多账户
 * <AUTHOR>
 * @date 2020/08/19
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {isNumber} from 'lodash';
import {Dropdown, Button, Transfer, Radio} from '@baidu/one-ui';
import {IconChevronDown, IconChevronUp} from '@baidu/one-ui-icon';

const RadioGroup = Radio.Group;

export default class Mcc extends PureComponent {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }
    constructor(props) {
        super(props);
        this.state = {
            visible: false,
            allDataMap: {},
            candidateList: [],
            selectedList: [],
            confirmList: [],
            allOption: 'all',
            confirmAllOption: 'all'
        };
    }

    static propTypes = {
        mccData: PropTypes.array
    }
    static defaultProps = {
        mccData: []
    }

    componentDidMount() {
        const {mccData} = this.props;
        const {config, eventHub} = this.context;
        const {mccInfo} = config;
        const suportAllOption = mccInfo && mccInfo.suportAllOption;
        const candidateList = [];
        const selectedList = suportAllOption ? ['0'] : [];
        const initialSelectedCount = mccInfo && mccInfo.initialSelectedCount;
        const allDataMap = mccData.reduce((memo, item, index) => {
            const {userId, userName} = item;
            memo[userId] = {
                key: userId,
                title: userName
            };
            candidateList.push(userId);
            if (initialSelectedCount) {
                if (initialSelectedCount === 'all') {
                    selectedList.push(userId);
                }
                else if (index < initialSelectedCount) {
                    selectedList.push(userId);
                }
            }
            return memo;
        }, {});
        this.setState({
            allDataMap,
            candidateList,
            selectedList,
            confirmList: selectedList
        }, () => {
            eventHub.trigger('sdk.event.mcc.confirm', {
                data: selectedList,
                needGetTableData: false
            });
        });
    }

    handleSelect = (selectedList, allDataMap) => {
        this.setState({
            allDataMap,
            selectedList
        });
    };

    handleSelectAll = (selectedList, allDataMap) => {
        this.setState({
            allDataMap,
            selectedList
        });
    };

    handleDelete = (selectedList, allDataMap) => {
        this.setState({
            allDataMap,
            selectedList
        });
    };

    handleDeleteAll = (selectedList, allDataMap) => {
        this.setState({
            allDataMap,
            selectedList
        });
    };

    getFilterData = () => {
        const {
            candidateList,
            allDataMap,
            searchValue
        } = this.state;
        return candidateList.filter(candidate => {
            const candidateInfo = allDataMap[candidate];
            return (candidateInfo.title || '').includes(searchValue);
        });
    };

    onSearchChange = e => {
        this.setState({
            searchValue: e.target.value
        });
    };

    onVisibleChange = visible => {
        const {confirmAllOption, confirmList} = this.state;
        const {mccInfo = {}} = this.context.config;
        const suportAllOption = mccInfo.suportAllOption;
        this.setState({
            visible,
            ...(suportAllOption
                ? {
                    allOption: confirmAllOption,
                    selectedList: confirmList
                } : {})
        });
    }

    handleConfirm = () => {
        const {selectedList, allOption} = this.state;
        const {eventHub} = this.context;
        eventHub.trigger('sdk.event.mcc.confirm', {
            data: selectedList,
            needGetTableData: true
        });
        this.setState({
            visible: false,
            confirmList: selectedList,
            confirmAllOption: allOption
        });
    }

    handleCancel = () => {
        const {confirmAllOption, confirmList} = this.state;
        const {mccInfo = {}} = this.context.config;
        const suportAllOption = mccInfo.suportAllOption;
        this.setState({
            visible: false,
            ...(suportAllOption
                ? {
                    allOption: confirmAllOption,
                    selectedList: confirmList
                } : {})
        });
    }
    handleAllOptionChange = e => {
        const allOption = e.target.value;
        this.setState({
            allOption,
            selectedList: allOption === 'all' ? ['0'] : []
        });
    }
    render() {
        const {classPrefix, filterAreaConfig, mccInfo = {}} = this.context.config;
        const {maxSelecteMccdNum, isMccSearchAble = false} = filterAreaConfig;
        const {
            visible,
            candidateList,
            allDataMap,
            selectedList,
            confirmList = [],
            searchValue,
            allOption
        } = this.state;
        const transferProps = {
            treeName: '账户',
            candidateList: isMccSearchAble && searchValue ? this.getFilterData() : candidateList,
            allDataMap,
            selectedList,
            handleSelect: this.handleSelect,
            handleSelectAll: this.handleSelectAll,
            handleDelete: this.handleDelete,
            handleDeleteAll: this.handleDeleteAll,
            showSearchBox: false,
            ...(isMccSearchAble ? {
                showSearchBox: true,
                onSearchChange: this.onSearchChange,
                searchValue,
                searchRenderProps: {
                    placeholder: '输入账户名称搜索'
                }
            } : {})
        };
        if (isNumber(maxSelecteMccdNum)) {
            transferProps.maxSelectedNum = parseInt(maxSelecteMccdNum, 10);
        }
        const suportAllOption = mccInfo.suportAllOption;
        const confirmBtnProps = {
            type: 'primary',
            disabled: suportAllOption && !selectedList.length,
            style: {marginRight: '12px', width: '72px'},
            onClick: this.handleConfirm
        };
        const cancelBtnProps = {
            type: 'normal',
            style: {width: '72px'},
            onClick: this.handleCancel
        };
        const displayAccountTran = !suportAllOption || suportAllOption && allOption !== 'all';
        const content = (
            <div className="transfer-container">
                {
                    suportAllOption
                        ? (
                            <RadioGroup
                                className="transfer-container-all-option"
                                value={allOption}
                                options={[{
                                    label: '全部账户',
                                    value: 'all'
                                }, {
                                    label: `部分账户(最多选择${maxSelecteMccdNum}个)`,
                                    value: 'maxCount'
                                }]}
                                defaultValue="all"
                                onChange={this.handleAllOptionChange}
                            />
                        )
                        : <div className="title">请选择账户</div>
                }
                {displayAccountTran ? <Transfer {...transferProps} /> : null}
                <div className="button-container">
                    <Button {...confirmBtnProps}>
                        确定
                    </Button>
                    <Button {...cancelBtnProps}>
                        取消
                    </Button>
                </div>
            </div>
        );
        const dropdownProps = {
            trigger: 'click',
            overlayClassName: `${classPrefix}-mcc-dropdown-container`,
            overlay: content,
            visible,
            onVisibleChange: this.onVisibleChange
        };
        let trrigeAreaContent;
        if (suportAllOption && allOption === 'all') {
            trrigeAreaContent = (<div className="trigger-btn-container">
                <span className="place-holder">全部已绑定账户</span>
                {visible ? <IconChevronUp className="icon-arrow" /> : <IconChevronDown className="icon-arrow" />}
            </div>);
        } else {
            const isAll = confirmList.length === 1 && confirmList[0] === '0';
            const confirmNames = !isAll && confirmList.map(user => allDataMap[user].title).join(',');
            const suffixText = !isAll && confirmList.length > 1 ? `等${confirmList.length}项` : '';
            trrigeAreaContent = !isAll && confirmList.length
                ? (
                    <div className="trigger-btn-container">
                        <span className={`user-names ${suffixText && 'short'}`}>{confirmNames}</span>
                        {suffixText && <span>{suffixText}</span>}
                        {visible
                            ? <IconChevronUp className="icon-arrow" />
                            : <IconChevronDown className="icon-arrow" />}
                    </div>
                )
                : (
                    <div className="trigger-btn-container">
                        <span className="place-holder">请选择账户</span>
                        {visible
                            ? <IconChevronUp className="icon-arrow" />
                            : <IconChevronDown className="icon-arrow" />}
                    </div>
                );
        }
        return (
            <div className={`${classPrefix}-mcc-container`}>
                <div className="title">账户：</div>
                <Dropdown {...dropdownProps}>
                    {trrigeAreaContent}
                </Dropdown>
            </div>
        );
    }
}
