/**
 * @file sdk - 筛选区 - 标题
 * <AUTHOR>
 * @date 2020/08/11
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import {Tabs} from '@baidu/one-ui';
import SdkDatePicker from '../datePicker';
import {datePlacementConfig} from '../../config/common';
import Mcc from './mcc';

const TabPane = Tabs.TabPane;

/* eslint-disable react/prefer-stateless-function */
export default class FilterAreaTitle extends PureComponent {
    static contextTypes = {
        config: PropTypes.object.isRequired
    }
    static propTypes = {
        isMcc: PropTypes.bool,
        mccData: PropTypes.array,
        initDateAndCompare: PropTypes.func,
        mainTableComparable: PropTypes.bool
    }
    static defaultProps = {
        isMcc: false,
        mccData: [],
        initDateAndCompare() {},
        mainTableComparable: false
    }
    state = {
        activeNav: null
    }
    handleNavChange = key => {
        const {
            filterAreaConfig
        } = this.context.config;
        const {onNavChange} = filterAreaConfig;
        typeof onNavChange === 'function' && onNavChange(key);
        this.setState({activeNav: key});
    }
    render() {
        const {isMcc, mccData, initDateAndCompare, mainTableComparable} = this.props;
        const {
            afterPageTitleRender,
            pageTitle,
            classPrefix,
            isNeedCompare,
            isNeedMcc,
            filterAreaConfig,
            datePlacement
        } = this.context.config;
        const {titleTip, navs = [], defaultActiveNav, hideDatePicker = false} = filterAreaConfig;
        const needCompare = isNeedCompare && mainTableComparable;
        const isNav = navs.length;
        const {activeNav} = this.state;
        const topTabsProps = {
            className: `${classPrefix}-title-container-nav`,
            size: 'large',
            activeKey: activeNav || defaultActiveNav || (isNav ? navs[0].value : null),
            onChange: this.handleNavChange
        };
        const rightPartCls = classNames('right-part', {
            'right-part-width-nav': isNav
        });

        const containerCls = classNames(`${classPrefix}-title-container`, {
            [`${classPrefix}-title-container-without-report-title`]: !pageTitle && !isNav
        });

        return (
            <div className={containerCls}>
                {isNav
                    ? <Tabs {...topTabsProps}>
                        {
                            navs.map(tab => <TabPane tab={tab.label} key={tab.value} />)
                        }
                    </Tabs>
                    : pageTitle
                        && (
                            <div className="left-part">
                                <div className="title">{pageTitle}</div>
                                <div className="refresh-data-time">
                                    {afterPageTitleRender && afterPageTitleRender()}
                                </div>
                            </div>
                        )
                }
                <div className={rightPartCls}>
                    {titleTip}
                    {isMcc && isNeedMcc && <Mcc mccData={mccData} />}
                    {(datePlacement === datePlacementConfig.withTitle && hideDatePicker !== true)
                        && <SdkDatePicker isNeedCompare={needCompare} initDateAndCompare={initDateAndCompare} />}
                </div>
            </div>
        );
    }
}
