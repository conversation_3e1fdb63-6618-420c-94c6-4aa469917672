/**
 * @file 枚举筛选器
 * <AUTHOR>
 * @date 2020/07/07
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import className from 'classnames';
import TimeUnit from './filterItem/timeUnit';
import Device from './filterItem/device';
import OcpcPayMode from './filterItem/ocpcPayMode';
import BidType from './filterItem/bidType';
import FeedSubjectEnum from './filterItem/feedSubjectEnum';
import FeedMaterialStyleEnum from './filterItem/feedMaterialStyleEnum';
import SecondComponentType from './filterItem/secondComponentType';
import CampaignType from './filterItem/campaignType';
import StoreType from './filterItem/storeType';
import More from './filterItem/more';
import MoreFilterAnchor from './moreFilterAnchor';
import Product from './filterItem/product';
import SubAppId from './filterItem/subAppId';

const filterNodeMap = {
    timeUnit: TimeUnit,
    device: Device,
    ocpcPayMode: OcpcPayMode,
    bidType: BidType,
    feedSubjectEnum: FeedSubjectEnum,
    feedMaterialStyleEnum: FeedMaterialStyleEnum,
    secondComponentType: SecondComponentType,
    product: Product,
    campaignType: CampaignType,
    storeType: StoreType,
    subAppId: SubAppId
};

export default class FilterList extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }

    constructor(props) {
        super(props);
        this.state = {
            moreFilterVisibleKey: null,
            clickFromWhere: null
        };
    }
    static propTypes = {
        pageFilterData: PropTypes.object,
        pageEditingFilterData: PropTypes.object,
        isMcc: PropTypes.bool
    }
    static defaultProps = {
        pageFilterData: {},
        pageEditingFilterData: {},
        isMcc: false
    }
    componentDidMount() {
        const {eventHub} = this.context;
        eventHub.on('sdk.event.page.filter.custom.confirm', () => this.onChangeMoreVisible());
        eventHub.on('sdk.event.page.filter.custom.cancel', () => this.onChangeMoreVisible());
    }

    onChangeMoreVisible = key => {
        this.setState({
            moreFilterVisibleKey: key
        });
    }

    onChangeFromWhere = key => {
        this.setState({
            clickFromWhere: key
        });
    }

    render() {
        const {filterAreaConfig, classPrefix} = this.context.config;
        const {filterList, moreList, filterData, filterNodeMap: outerFilterNodeMap} = filterAreaConfig;
        const {
            pageEditingFilterData,
            pageFilterData,
            isMcc
        } = this.props;
        const {clickFromWhere, moreFilterVisibleKey} = this.state;
        const moreProps = {
            pageEditingFilterData,
            pageFilterData,
            onChangeFromWhere: this.onChangeFromWhere,
            clickFromWhere,
            isMcc
        };
        const selectedMoreList = moreList.filter(item => {
            return pageFilterData
                && pageFilterData[item]
                && pageFilterData[item].values
                && pageFilterData[item].values.length > 0;
        });
        const targetFilterNodeMap = {
            ...filterNodeMap,
            ...outerFilterNodeMap
        };
        const haveFilters = (filterList && filterList.length)
            || (selectedMoreList && selectedMoreList.length);
        const listCls = className(`${classPrefix}-page-filter-list-container`, {
            [`${classPrefix}-page-filter-list-container-empty`]: !haveFilters
        });
        return (
            <div className={listCls}>
                {filterList.map((filter, index) => {
                    const FilterRender = targetFilterNodeMap[filter];
                    return <FilterRender key={index} isMcc={isMcc} />;
                })}
                {selectedMoreList && selectedMoreList.length > 0 && selectedMoreList.map((filter, index) => {
                    const data = filterData[filter];
                    const moreFilterAnchorProps = {
                        onChangeFromWhere: this.onChangeFromWhere,
                        onChangeMoreVisible: this.onChangeMoreVisible,
                        clickFromWhere,
                        moreFilterVisibleKey,
                        pageEditingFilterData,
                        pageFilterData,
                        selectedMoreList,
                        data,
                        isMcc
                    };
                    return <MoreFilterAnchor key={index} {...moreFilterAnchorProps} />;
                })}
                <More {...moreProps} />
            </div>
        );
    }
}
