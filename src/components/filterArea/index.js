/**
 * @file sdk - 筛选区
 * <AUTHOR>
 * @date 2020/08/11
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import Title from './title';
import FilterList from './filterList';

/* eslint-disable react/prefer-stateless-function */
export default class Filter<PERSON>rea extends PureComponent {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }
    static propTypes = {
        pageFilterData: PropTypes.object,
        pageEditingFilterData: PropTypes.object,
        isMcc: PropTypes.bool,
        mccData: PropTypes.array,
        initDateAndCompare: PropTypes.func,
        mainTableComparable: PropTypes.bool
    }
    static defaultProps = {
        pageFilterData: {},
        pageEditingFilterData: {},
        isMcc: false,
        mccData: [],
        initDateAndCompare() {},
        mainTableComparable: true
    }
    render() {
        const {
            pageEditingFilterData,
            pageFilterData,
            isMcc,
            mccData,
            initDateAndCompare,
            mainTableComparable
        } = this.props;
        const {classPrefix} = this.context.config;
        const filterListProps = {
            pageEditingFilterData,
            pageFilterData,
            isMcc
        };
        const titleProps = {
            mainTableComparable,
            isMcc,
            mccData,
            initDateAndCompare
        };
        return (
            <div className={`${classPrefix}-filter-area-container`}>
                <Title {...titleProps} />
                <FilterList {...filterListProps} />
            </div>
        );
    }
}
