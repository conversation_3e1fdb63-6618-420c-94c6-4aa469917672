@import "../../default.less";

.@{report-prefix-cls} {
    &-title-container {
        position: relative;
        display: flex;
        flex: 1 0 100%;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;

        &-without-report-title {
            order: 2;
            flex: none;
            margin-left: auto;
        }

        .left-part {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-items: center;

            .title {
                font-size: @dls-font-size-3;
                color: @dls-foreground-color-neutral;
            }

            .refresh-data-time {
                display: inline-block;
                margin-bottom: @dls-padding-unit;
            }
        }

        .@{report-prefix-cls}-mcc-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-items: center;
            font-size: @dls-font-size-1;
            margin-left: @dls-padding-unit * 3;

            .title {
                font-size: @dls-font-size-1;
            }

            .trigger-btn-container {
                width: @dls-padding-unit * 36.5;
                box-sizing: border-box;
                border: 1px solid @dls-color-gray-4;
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-start;
                align-items: center;
                position: relative;
                cursor: pointer;
                padding: 0 @dls-padding-unit * 3;
                line-height: @dls-padding-unit * 7.5;
                border-radius: @dls-padding-unit;

                .place-holder {
                    display: inline-block;
                }

                .icon-arrow {
                    position: absolute;
                    right: @dls-padding-unit * 3;
                    top: @dls-padding-unit * 2;
                    color: @dls-color-gray-7;
                    width: @dls-padding-unit * 2.5;
                }

                .user-names {
                    width: auto;
                    width: @dls-padding-unit * 25;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                }

                .short {
                    width: @dls-padding-unit * 15;
                }
            }
        }

        .right-part {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-items: center;

            &-width-nav {
                position: absolute;
                right: 0;
            }
        }

        &-nav.one-tabs {
            width: 100%;

            .one-tabs-bar {
                margin-bottom: 0;

                .one-tabs-nav-container {
                    padding: 0;
                }
            }

            &-hidden {
                display: none;
            }
        }
    }

    &-page-filter-list-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-items: center;
        margin-top: @dls-padding-unit * 4;

        &-empty {
            display: none;
        }

        .filter-item {
            margin-right: @dls-padding-unit * 3;
            margin-bottom: @dls-padding-unit * 2;

            .filter-item-custom-render {
                color: @dls-foreground-color-neutral-light;
            }
        }

        .second-component-type-container {
            height: @dls-padding-unit * 7.5;
            line-height: @dls-padding-unit * 7.5;
            font-size: @dls-font-size-1;
            color: @dls-color-gray-9;
            border: 1px solid @dls-color-gray-4;
            border-radius: @dls-padding-unit;
            // padding: 0 @dls-padding-unit * 3 0 0;
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-items: center;
            cursor: pointer;
            position: relative;

            .second-component-type-content {
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-start;
                align-items: center;
                padding-left: @dls-padding-unit * 3;

                .second-component-type-title {
                    width: @dls-padding-unit * 40;
                }

                .texts {
                    width: @dls-padding-unit * 25;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                }
            }

            .indicator-arrow {
                position: absolute;
                top: @dls-padding-unit * 0.5;
                right: @dls-padding-unit * 3;
                display: inline-block;
                margin-left: @dls-padding-unit * 2;

                .indicator-icon {
                    color: @dls-color-gray-7;
                    width: @dls-padding-unit * 2.5;
                }
            }
        }

        .@{report-prefix-cls}-more-container {
            position: relative;
            margin-bottom: @dls-padding-unit * 2;

            .@{report-prefix-cls}-filter-container {
                position: absolute;
                left: 0;
                top: -@dls-padding-unit;
                z-index: 3;

                .enum-filter-container {
                    height: auto;
                    max-height: @dls-padding-unit * 52;
                }
            }

            .custom-render-container {
                position: absolute;
                left: 0;
                top: -@dls-padding-unit;
                z-index: 10;
            }
        }

        .filter-result-label {
            height: @dls-padding-unit * 5;
            background: @dls-background-color-neutral;
            padding: @dls-padding-unit * 1.5 @dls-padding-unit * 2;
            border-radius: @dls-padding-unit;
            color: @dls-foreground-color-neutral;
            margin-right: @dls-padding-unit * 3;
            position: relative;
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-bottom: @dls-padding-unit * 2;
            box-sizing: content-box;

            .filter-label {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-size: @dls-font-size-1;
                line-height: @dls-padding-unit * 6;
                margin-right: @dls-padding-unit * 2;

                .filter-anchor-label {
                    max-width: @dls-padding-unit * 65;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .filter-anchor-number {
                    color: @dls-foreground-color-neutral-weak;
                }
            }

            .close-btn {
                color: @dls-foreground-color-neutral-weak;
                cursor: pointer;

                .close-icon {
                    font-size: @dls-font-size-1;
                }
            }
        }

        .filter-result-label-active {
            background: @dls-background-color-neutral-hover;
        }
    }

    &-filter-area-container {
        display: flex;
        flex-wrap: wrap;
        background: @dls-color-gray-0;
        border-radius: @dls-padding-unit * 1.5;
        padding: @dls-padding-unit * 4 @dls-padding-unit * 6 @dls-padding-unit * 5;
        margin-bottom: @dls-padding-unit * 6;
    }

    &-second-component-type-overlay-container {
        background: @dls-color-gray-0;
        box-shadow: @dls-shadow-1;

        .btn-container {
            padding: @dls-padding-unit * 4;
            border-top: 1px solid @dls-color-gray-4;
        }
    }

    &-second-component-type-cascader {
        .one-cascader-pane-menus {
            box-shadow: none;

            .one-cascader-pane-menu {
                min-width: @dls-padding-unit * 43;
                height: @dls-padding-unit * 58;
            }
        }
    }

    &-mcc-dropdown-container {
        .transfer-container {
            padding: @dls-padding-unit * 5;
            background: @dls-color-gray-0;
            border-radius: @dls-padding-unit;
            box-shadow: @dls-shadow-1;

            .title {
                font-size: @dls-font-size-1;
                margin-bottom: @dls-padding-unit * 3.5;
            }

            .button-container {
                margin-top: @dls-padding-unit * 4;
            }

            &-all-option {
                margin-bottom: @dls-padding-unit * 2.5;
            }
        }
    }
}