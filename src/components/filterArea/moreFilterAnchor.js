/**
 * @file 页面筛选-更多筛选结果
 * <AUTHOR>
 * @date 2020/8/14
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {IconTimes} from 'dls-icons-react';
import {Dropdown} from '@baidu/one-ui';
import PageEnumFilter from './filterItem/more/pageEnumFilter';
import {getInitPageEditingFilterData} from '../../utils/filterArea';

/* eslint-disable react/prefer-stateless-function */
export default class MoreFilterAnchor extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    static propTypes = {
        pageEditingFilterData: PropTypes.object,
        pageFilterData: PropTypes.object,
        onChangeFromWhere: PropTypes.func,
        onChangeMoreVisible: PropTypes.func,
        clickFromWhere: PropTypes.string,
        moreFilterVisibleKey: PropTypes.string,
        data: PropTypes.object
    }
    static defaultProps = {
        pageEditingFilterData: {},
        pageFilterData: {},
        onChangeFromWhere() {},
        onChangeMoreVisible() {},
        clickFromWhere: '',
        moreFilterVisibleKey: '',
        data: {}
    }
    render() {
        const {config, eventHub} = this.context;
        const {classPrefix, filterAreaConfig} = config;
        const {filterData} = filterAreaConfig;
        const {
            onChangeFromWhere,
            onChangeMoreVisible,
            clickFromWhere,
            moreFilterVisibleKey,
            pageEditingFilterData,
            pageFilterData,
            data,
            isMcc
        } = this.props;
        const {name, field, option} = data;
        const initPageFilterData = getInitPageEditingFilterData(filterAreaConfig);
        const selectedValues = pageFilterData[field] && pageFilterData[field].values;
        const customLabels = pageFilterData[field] && pageFilterData[field].labels;
        const labels = customLabels || selectedValues.map(item => {
            const targetOption = option.filter(o => o.value === item)[0];
            return targetOption.label;
        });
        const labelNum = labels && labels.length || 0;
        const labelStr = labels.join('、');
        const pageEnumFilterProps = {
            field,
            name,
            option,
            pageEditingFilterData,
            pageFilterData,
            onChangeMoreVisible
        };
        const CustomRender = filterData[field] && filterData[field].customRender;
        const initCustomRenderData = pageFilterData[field] && pageFilterData[field].values;
        const isCustomRenderPartDisable = isMcc
            && field === 'queryScope'
            && filterData[field] && filterData[field].partDisable === true;
        const TargetRender = CustomRender
            ? (<div className="custom-render-container">
                <CustomRender isCustomRenderPartDisable={isCustomRenderPartDisable} initData={initCustomRenderData} />
            </div>)
            : <PageEnumFilter {...pageEnumFilterProps} />;
        const dropdownProps = {
            trigger: 'click',
            visible: clickFromWhere === 'moreFilterList' && moreFilterVisibleKey === field,
            overlayClassName: `${classPrefix}-table-filter-dropdown-layer`,
            placement: 'bottomLeft',
            onVisibleChange: v => {
                onChangeFromWhere(v ? 'moreFilterList' : '');
                onChangeMoreVisible(v ? field : '');
            },
            overlay: TargetRender
        };
        const popAnchorProps = {
            className: 'filter-label',
            title: `${name}：${labelStr}`
        };
        const containerClass = moreFilterVisibleKey === field ? ' filter-result-label-active' : '';
        const clearFilter = () => {
            const newPageFilterData = {
                ...pageFilterData,
                [field]: initPageFilterData[field]
            };
            const newPageEditingFilterDataa = {
                ...pageEditingFilterData,
                [field]: initPageFilterData[field]
            };
            eventHub.trigger('sdk.event.page.filter.clear', {
                data: {
                    pageFilterData: newPageFilterData,
                    pageEditingFilterData: newPageEditingFilterDataa
                }
            });
        };
        return (
            <div className={`filter-result-label${containerClass}`}>
                <Dropdown {...dropdownProps}>
                    <span {...popAnchorProps}>
                        <span className="filter-anchor-label">{`${name}：${labelStr}`}</span>
                        {
                            labelNum && labelNum > 1 && <span className="filter-anchor-number">共{labelNum}项</span>
                        }
                    </span>
                </Dropdown>
                <span className="close-btn">
                    <IconTimes className="close-icon" onClick={clearFilter} />
                </span>
            </div>
        );
    }
}
