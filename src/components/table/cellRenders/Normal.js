import React from 'react';
import _ from 'lodash';
import {splitNum, formatText} from '../../../utils/table';
import {INFINITY} from '../../../config/default/cell';
import {numberTypes} from '../../../config/default/filter';

export default ({
    column,
    columnName,
    columnInfo = {}
}) => {
    const {feConfig, isPercentage, precision, columnType} = columnInfo;
    const {textDirection, cellAlign} = feConfig || {};

    const isNumber = ~numberTypes.indexOf(columnType);
    const baseTextStyle = {
        lineHeight: '19px',
        textAlign: textDirection
            ? textDirection
            : (isNumber && cellAlign !== 'left' ? 'right' : 'left'),
        ...(isNumber
            ? {
                wordBreak: 'keep-all',
                overflow: 'hidden',
                whiteSpace: 'nowrap'
            } : {
                display: '-webkit-box',
                overflow: 'hidden',
                'WebkitLineClamp': 2,
                'WebkitBoxOrient': 'vertical',
                whiteSpace: 'pre-line'
            }
        )
    };
    const textStyle = {
        ...baseTextStyle,
        ...(isNumber
            ? {
                height: '38px',
                lineHeight: '38px'
            } : {}
        )
    };

    let value = column[columnName];
    const isSummary = column.isSummaryRow;

    // 会存在无限形式，做下处理
    if (value && value.length === 1 && parseFloat(value) > INFINITY) {
        value = '-';
    }
    return (
        <div>
            {
                Array.isArray(value)
                    ? (value.length > 1
                        ? (isSummary && columnName === 'date')
                            ? <span style={textStyle} title={value[3]}>{value[3]}</span>
                            : value.map((text, index) => {
                                if (isNumber) {
                                    const isRenderRate = index >= value.length - 1
                                        && _.isNumber(text)
                                        && !_.isNaN(text);
                                    isRenderRate
                                        ? (text = getRateChangeText(text, precision))
                                        : (text = formatText(text, isPercentage, precision));
                                }
                                return (
                                    <div style={baseTextStyle} key={index} title={text}>
                                        {text || '-'}
                                    </div>
                                );
                            })
                        : <div style={textStyle}>
                            {value.length
                                ? (isNumber
                                    ? <span title={formatText(value[0], isPercentage, precision)}>
                                        {formatText(value[0], isPercentage, precision)}
                                    </span>
                                    : <span title={Object.values(value).join('\n')}>
                                        {Object.values(value).join('\n')}
                                    </span>
                                )
                                : '-'
                            }
                        </div>
                    )
                    : <div style={{...textStyle}}>-</div>
            }
        </div>
    );
};


function getRateChangeText(rateChange, precision = 2) {
    // rateChange 可能返回无穷大，做一下处理
    if (rateChange > INFINITY) {
        return <span>-</span>;
    }

    let rateTextStyle = {
        color: '#CC1800'
    };

    if (rateChange < 0) {
        rateTextStyle.color = '#00BF5C';
    }

    rateChange = splitNum(+(rateChange * 100).toFixed(precision)) + '%';

    return <span style={{...rateTextStyle}}>{rateChange}</span>;
}
