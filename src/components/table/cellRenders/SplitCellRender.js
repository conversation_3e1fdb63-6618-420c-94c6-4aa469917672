import React from 'react';
import _ from 'lodash';
import {formatText} from '../../../utils/table';
import {INFINITY} from '../../../config/default/cell';
import {numberTypes} from '../../../config/default/filter';

export default ({
    column,
    columnName,
    columnInfo = {}
}) => {
    const {feConfig, isPercentage, precision, columnType} = columnInfo;
    const {textDirection, cellAlign} = feConfig || {};
    const isNumber = ~numberTypes.indexOf(columnType);
    const textStyle = {
        lineHeight: '19px',
        textAlign: textDirection
            ? textDirection
            : (isNumber && cellAlign !== 'left' ? 'right' : 'left'),
        whiteSpace: isNumber ? 'nowrap' : 'pre-line'
    };
    let value = column[columnName];
    // 会存在无限形式，做下处理
    if (value && value.length === 1 && parseFloat(value) > INFINITY) {
        value = '-';
    }
    return (
        <div>
            {
                Array.isArray(value)
                    ? (value.length > 1
                        ? value.map((text, index) => {
                            if (isNumber) {
                                // 细分时第一个时全部，不做处理
                                text = formatText(text, isPercentage, precision);
                            }
                            return (
                                <div style={textStyle} key={index}>
                                    {text || '-'}
                                </div>
                            );
                        })
                        : <div style={textStyle}>
                            {value.length
                                ? (isNumber
                                    ? formatText(value[0], isPercentage, precision)
                                    : Object.values(value).join('\n')
                                )
                                : '-'
                            }
                        </div>
                    )
                    : <div style={{...textStyle}}>{value || '-'}</div>
            }
        </div>
    );
};
