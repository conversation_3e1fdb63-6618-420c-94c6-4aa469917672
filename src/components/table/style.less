@import "../../default.less";
.@{report-prefix-cls} {
    color: @dls-color-gray-9;
    .show-generate-rgb {
        span {
            width: @dls-padding-unit * 8;
            height: @dls-padding-unit * 8;
            display: inline-block;
        }
    }
    .show-detail-btn {
        font-size: @dls-font-size-1;
        color: @dls-color-brand-7;
        font-weight: @dls-font-weight-1;
        cursor: pointer;
    }
    &-table {
        display: flex;
        flex-direction: column;
        position: relative;
        &-info-alert {
            margin-bottom: @dls-padding-unit * 4;
        }
        &-top-wrap {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-items: center;
        }
        &-top {
            display: inline-block;
            height: 20px;
            background: #ebedf5;
            padding: 4px 8px;
            border-radius: 4px;
            color: #282c33;
            margin-right: 4px;
            position: relative;
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            box-sizing: content-box;

            .close-icon {
                color: #848b99;
                cursor: pointer;
                margin-left: 8px;
            }
        }
        &-info-list {
            font-size: @dls-font-size-0;
            color: @dls-color-gray-6;
            line-height: @dls-padding-unit * 4;
            margin-top: @dls-padding-unit * 4;
        }
        .table-title {
            font-size: @dls-padding-unit * 4;
        }
        .table-search-container {
            margin-right: @dls-padding-unit * 4;
        }
        &-pagination {
            margin-top: @dls-padding-unit * 4;
        }
        .@{report-prefix-cls}-main-table {
            .one-table-thead {
                tr {
                    th {
                        line-height: @dls-padding-unit * 4 +  @dls-padding-unit / 2;
                    }
                }
                & >:last-child {
                    th {
                        height: @dls-padding-unit * 14;
                    }
                }
            }
            .one-table-tbody {
                >tr >td:first-child {
                    padding-left: @dls-padding-unit * 2;
                }
                >tr >td {
                    line-height: @dls-padding-unit * 4 + @dls-padding-unit * 3 / 4;
                }
                tr {
                    // 隐藏总计行的不可用的多选框
                    td.one-table-selection-column {
                        padding-left: @dls-padding-unit * 4;
                        .one-checkbox-wrapper-disabled {
                            display: none;
                        }
                    }
                }
            }
        }
        .@{report-prefix-cls}-main-table-header {
            display: flex;
            align-items: center;
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 100%;
            padding: 0 @dls-padding-unit * 2;
            box-sizing: border-box;
            cursor: pointer;
            .title-container {
                height: 100%;
                display: flex;
                align-items: center;
                .text {
                    margin-right: @dls-padding-unit;
                    max-width: @dls-padding-unit * 22;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                }
            }
            .sorter-box {
                display: inline-block;
                position: relative;
                .sort-normal {
                    color: @dls-background-color-neutral-primary;
                    position: relative;
                    height: @dls-padding-unit * 3 + @dls-padding-unit / 4;
                    width: @dls-padding-unit * 2 + @dls-padding-unit / 2;
                    font-size: @dls-padding-unit * 3 + @dls-padding-unit / 4;
                    .asc-sorter {
                        position: absolute;
                        top: 0;
                        left: 0;
                    }
                    .desc-sorter {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                    }
                    &:hover:not(.sort-normal-active) {
                        color: @dls-color-gray-7;
                    }
                }
                .sort-active {
                    color: @dls-background-color-primary;
                    position: absolute;
                    left: 0;
                    top: 0;
                }
            }
            .filter-box {
                display: none;
                color: @dls-background-color-neutral-primary;
                font-size: @dls-padding-unit * 2.5;
                padding-left: @dls-padding-unit;
                padding-right: @dls-padding-unit*2;
                &:hover:not(.filter-box-active) {
                    color: @dls-color-gray-7;
                }
            }
            .filter-box-active {
                display: block;
                color: @dls-background-color-primary;
            }
        }
        .@{report-prefix-cls}-main-table-header-right {
            justify-content: flex-end;
        }
        .one-table-thead > tr > th {
            &:hover {
                .filter-box {
                    display: block;
                }
            }
        }
        &-split-container {
            // min-width: @dls-padding-unit * 30 !important;
            .one-cascader-input {
                .one-input-detail {
                    // min-width: @dls-padding-unit * 30 !important;
                }
            }
            .one-input-detail {
                .one-input {
                    color: @dls-color-gray-9;
                }
            }
        }
        &-split-popup-container {
            .one-cascader-menu-small {
                height: auto;
            }
        }
    }
    &-filter-list-container {
        display: inline-flex;
        max-height: @dls-padding-unit * 18;
        overflow-y: auto;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-items: center;
        .filter-result-label {
            height: @dls-padding-unit * 5;
            background: @dls-background-color-neutral;
            padding: @dls-padding-unit @dls-padding-unit * 2;
            border-radius: @dls-padding-unit;
            color: @dls-foreground-color-neutral;
            margin-right: @dls-padding-unit;
            position: relative;
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-bottom: @dls-padding-unit * 2;
            box-sizing: content-box;
            .filter-label {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                font-size: @dls-font-size-1;
                line-height: @dls-padding-unit * 6;
                margin-right: @dls-padding-unit * 2;
                .filter-anchor-label {
                    max-width: @dls-padding-unit * 65;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                .filter-anchor-number {
                    color: @dls-foreground-color-neutral-weak;
                }
            }
            .close-btn {
                color: @dls-foreground-color-neutral-weak;
                cursor: pointer;
                .close-icon {
                    font-size: @dls-font-size-1;
                }
            }
        }
        .filter-result-label-active {
            background: @dls-background-color-neutral-hover;
        }
        .clear-all-btn {
            padding: 0;
            margin-top: -@dls-padding-unit * 2;
            margin-left: @dls-padding-unit * 3;
        }
    }
    .filter-list-space-cls {
        margin-top: @dls-padding-unit * 2;
    }
    &-table-filter-dropdown-layer {
        .one-popover-arrow {
            display: none;
        }
        .one-popover-inner {
            border-radius: @dls-padding-unit;
        }
    }
    &-filter-container {
        background: #fff;
        padding: @dls-padding-unit * 4;
        border-radius: @dls-padding-unit;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
        .filter-title {
            color: @dls-foreground-color-neutral-light;
            font-size: @dls-font-size-1;
            margin-bottom: @dls-padding-unit * 3;
        }
        .button-container {
            position: relative;
            padding-top: @dls-padding-unit * 2;
            margin-top: @dls-padding-unit * 2;
            .line-sperator {
                position: absolute;
                border-top: 1px solid @dls-border-color-neutral-disabled;
                top: 0;
                left: -@dls-padding-unit * 4;
                width: calc(100% + @dls-padding-unit * 8);
            }
        }
        .enum-filter-container {
            min-width: @dls-padding-unit * 36;
            height: auto;
            max-height: @dls-padding-unit * 52;
            overflow-y: auto;
        }
        .string-filter-container {
            .string-selector {
                margin-bottom: @dls-padding-unit * 3;
                .operator-selector {
                    color: @dls-foreground-color-neutral-weak;
                }
            }
            .error-msg {
                color: @dls-color-error;
                font-size: @dls-font-size-1;
                margin-top: @dls-padding-unit * 2;
            }
        }
        .number-filter-container {
            padding-bottom: @dls-padding-unit * 2;
            display: flex;
            align-items: center;
            .operator-selector {
                color: @dls-foreground-color-neutral-weak;
                margin-right: @dls-padding-unit * 2;
            }
        }
        .date-time-filter-container {
            margin-bottom: @dls-padding-unit * 12.5;
            padding: @dls-padding-unit * 3;
        }
        .checkbox-filter-container {
            .checkbox-group {
                width: 100%;
                max-height: 200px;
                overflow: auto;
            }
        }
    }
    &-filter-container-string {
        .one-ui-pro-textline-error {
            display: none;
        }
    }
    &-table-header-panel {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-bottom: @dls-padding-unit * 4;
        &-field {
            display: flex;
            align-items: center;
            .update-time-content {
                font-size: @dls-font-size-1;
                margin-right: @dls-padding-unit * 4;
                color: @dls-background-color-neutral-primary-hover;
            }
            &-left {
                justify-content: flex-start;
                .indicator-btn {
                    .indicator-arrow {
                        display: inline-block;
                        margin-left: @dls-padding-unit * 2;
                        .indicator-icon {
                            width: @dls-padding-unit * 2;
                        }
                    }
                }
            }
            &-right {
                justify-content: flex-end;
                > * {
                    margin-right: 16px;
                }
                >:last-child {
                    margin-right: 0;
                }
                .sortable-selector-wrap {
                    height: @dls-padding-unit * 7;
                    .mark-dot {
                        color: @dls-color-success-7;
                    }
                }
            }
        }
    }
    // 适配hack one-ui样式
    &-custom-columns-modal {
        display: flex !important;
        align-items: center;
        .one-dialog {
            overflow-y: initial;
            position: initial;
            transform: initial;
            margin: auto;
            .one-dialog-content {
                max-height: initial;
                .one-dialog-body {
                    overflow: initial;
                    max-height: initial;
                    min-width: 1064px;
                }
            }
        }
        &::before {
            height: initial;
        }
    }
    &-main-table-header-tip-layer {
        width: @dls-padding-unit * 52;
    }
    &-email {
        display: inline-block;
        &-dropdown {
            .one-button {
               span {
                   width: 100%;
                   justify-content: space-between;
               }
            }
        }
        &-required {
            color: @dls-color-error;
        }
        &-tags {
            overflow: hidden;
            &-text {
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: @dls-padding-unit * 60;
            }
        }
        &-content {
            width: @dls-padding-unit * 75;
            box-sizing: border-box;
            background: @dls-background-color-base-1;
            border: 1px solid @dls-border-color-fillable;
            border-radius: @dls-border-radius-1;
            padding: @dls-spacing-unit * 2.5;
            .one-tag {
                margin: 0 0 (@dls-spacing-unit * 2) 0;
                float: left;
                clear: both;
            }
            &-input {
                input {
                    width: @dls-padding-unit * 58;
                    border: 0;
                    outline: 0;
                    line-height: @dls-line-height-2;
                    font-size: @dls-font-size-1;
                }
                span {
                    float: right;
                    font-size: @dls-font-size-0;
                    color: @dls-color-gray-9;
                    letter-spacing: 0;
                    line-height: @dls-line-height-2;
                }
            }
            &.error-input {
                border-color: @dls-border-color-error-focus;
            }
        }
        &-setting {
            .question-icon {
                margin-left: @dls-spacing-unit;
            }
        }
        &-nextDate {
            font-size: @dls-font-size-1;
            margin-left: @dls-spacing-unit * 4;
            color: @dls-color-gray-7;
            letter-spacing: 0;
        }
        &-tip-content {
            width: 200px;
            font-size: @dls-font-size-0;
            &-line {
                margin-bottom: @dls-spacing-unit * 3;
                word-wrap: break-word;
                word-break: normal;
                line-height: @dls-line-height-1;
            }
        }
        &-group {
            margin-top: @dls-padding-unit * 5;
            margin-bottom: @dls-padding-unit;
        }
    }
    &-including-del-setting {
        display: flex;
        align-items: center;
        &-title {
            margin-right: @dls-padding-unit;
        }
        &-switch {
            margin: 0 @dls-padding-unit*3 0 @dls-padding-unit*6;
        }
        &-tip-question {
            cursor: pointer;
        }
    }
}
