/**
 * @file sdk - 细分
 * <AUTHOR>
 * @date 2020/08/21
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Cascader} from '@baidu/one-ui';
import {regionNameMap, regionColumns} from '../../../config/common';

export default class Split extends PureComponent {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }
    constructor(props) {
        super(props);
        this.state = {
            value: []
        };
    }

    static propTypes = {
        splitColumns: PropTypes.array,
        columnConfigs: PropTypes.object,
        onSplitChange: PropTypes.func
    }
    static defaultProps = {
        splitColumns: [],
        columnConfigs: {},
        onSplitChange() {}
    }

    componentDidMount() {
        const {eventHub: {on}} = this.context;
        on('sdk.event.table.changeCommonFilter', this.handleClear);
    }

    handleClear = ({data}) => {
        const {compareStartDate} = data;
        if (compareStartDate) {
            this.setState({
                value: []
            });
        }
    }

    handleChange = value => {
        const {eventHub: {trigger}} = this.context;
        this.setState({
            value
        }, () => {
            trigger('sdk.event.table.split.change', {data: value && value[value.length - 1]});
        });
    }

    render() {
        const {splitColumns, columnConfigs, disabled, className, popupClassName} = this.props;
        const options = [];
        const regionList = [];
        splitColumns.map(column => {
            if (regionColumns.indexOf(column) > -1) {
                regionList.push(column);
            }
            else {
                const data = columnConfigs && columnConfigs[column] || {};
                const {columnText} = data;
                options.push({
                    label: columnText,
                    value: column
                });
            }
        });
        if (regionList.length) {
            options.push({
                label: '地域',
                value: 'region',
                children: regionList.map(r => {
                    return {
                        label: regionNameMap[r],
                        value: r
                    };
                })
            });
        }
        const cascaderProps = {
            placeholder: '细分',
            size: 'small',
            value: this.state.value,
            disabled,
            options,
            onChange: this.handleChange,
            width: 160,
            style: {marginRight: '16px'},
            allowClear: true,
            className,
            popupClassName,
            displayRender: label => {
                const content = label && label.length ? `细分：${label.join('-')}` : '';
                return <span title={content}>{content}</span>;
            }
        };
        return (
            <Cascader {...cascaderProps} />
        );
    }
}
