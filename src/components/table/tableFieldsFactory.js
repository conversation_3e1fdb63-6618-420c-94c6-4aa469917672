import React from 'react';
import {
    getTableCustomerFields,
    sortColumns,
    getFilterAdditionFields,
    getWithSplitColumns,
    getSelectedFields
} from '../../utils/table';
import TableHeader from './tableHeader';
import tableCellsFactory from './tableCellsFactory';

export default ({state, context, props}) => {
    const {
        tableHeaderData,
        editingFilterData,
        filterData,
        sortRule,
        filterDropdownVisibleMap,
        filterFromWhere,
        tableIndicatorKey,
        splitColumn,
        additionFields,
        commonFilterInfo,
        columnWidthStorageMap
    } = state;
    const {
        mainAreaConfig = {},
        topTabKey
    } = props;
    const {tableAreaConfig = {}} = mainAreaConfig;
    const {config} = context;
    const {withNewSortableSelector = false} = tableAreaConfig;
    const customerFields = getTableCustomerFields({
        context,
        tableAreaConfig,
        tableIndicatorKey,
        topTabKey
    });
    const {cellRenderConfig, columnWidthDefaultMap} = config;
    const {
        customColumns = [],
        defaultVisibleColumns = [],
        columnConfigs = {},
        columnGroups,
        visibleColumns,
        columnCategories,
        newColumnCategories,
        templateId,
        templateColumns = []
    } = tableHeaderData;
    if (!Object.keys(columnConfigs).length) {
        return;
    }
    const targetColumns = getSelectedFields({
        withNewSortableSelector,
        defaultVisibleColumns,
        customColumns,
        templateId,
        templateColumns
    });
    let targetColumnsWithSplitColumn = splitColumn
        ? getWithSplitColumns({
            columnCategories: columnCategories || newColumnCategories,
            targetColumns,
            splitColumn
        }) || targetColumns.concat(splitColumn)
        : targetColumns;
    const {filterFields} = commonFilterInfo;
    const filterAdditionFields = getFilterAdditionFields(tableAreaConfig, filterFields);
    targetColumnsWithSplitColumn = Array.from(new Set([
        ...targetColumnsWithSplitColumn,
        ...filterAdditionFields,
        ...additionFields
    ]));
    const fields = [];
    for (let i = 0; i < targetColumnsWithSplitColumn.length; i++) {
        const columnInfo = columnConfigs[targetColumnsWithSplitColumn[i]] || {};
        const {columnName, columnType, feConfig} = columnInfo;
        const {columnWidth, fixed, draggable} = feConfig || {};
        const tableHeaderProps = {
            stateData: {
                editingFilterData,
                filterData,
                sortRule,
                filterDropdownVisibleMap,
                filterFromWhere
            },
            columnInfo,
            tableAreaConfig
        };
        const {isDraggable = true} = tableAreaConfig;
        const columnMap = {
            title: <TableHeader {...tableHeaderProps} />,
            key: columnName,
            dataIndex: columnName,
            render: tableCellsFactory({
                columnName,
                columnInfo,
                cellRenderConfig,
                splitColumn,
                columnCategories: columnCategories || newColumnCategories
            }),
            width: columnWidthStorageMap[columnName]?.width || columnWidth || columnWidthDefaultMap[columnType] || 112,
            minWidth: 80,
            draggable: draggable !== undefined ? draggable : isDraggable,
            fixed: fixed ? fixed : false
        };
        fields.push(columnMap);
    }
    const isAdditionFields = (filterAdditionFields && filterAdditionFields.length)
        || (additionFields && additionFields.length);
    sortColumns(fields, isAdditionFields ? visibleColumns : targetColumnsWithSplitColumn);
    // 添加表头分组
    const targetColumnGroups = columnGroups && columnGroups.filter(g => g.name) || [];
    const allGroupColumn = targetColumnGroups && targetColumnGroups.reduce((memo, item) => {
        return memo.concat(item.columns);
    }, []);
    const fieldsNotInGroup
        = fields && fields.filter(field => allGroupColumn.indexOf(field.key) < 0) || [];
    const columnGroupsFields = targetColumnGroups && targetColumnGroups.map((group, index) => {
        const columns = group.columns;
        const groupFields = [];
        for (let i = 0; i < fields.length; i++) {
            if (columns.indexOf(fields[i].key) > -1) {
                groupFields.push(fields[i]);
            }
        }
        return {
            title: group.name,
            children: groupFields
        };
    }) || [];
    const newFields = fieldsNotInGroup.concat(columnGroupsFields);
    if (newFields.length && customerFields && customerFields.length) {
        customerFields.forEach(customerField => {
            // position,位置信息，group表示所在分组序号，column表示所在分组列的序号，所有序号从1开始计算
            const {width, position = {}, columnText, cellRender, key = 'operate', fixed} = customerField;
            const {group: gruopIndex, column: columnIndex} = position;
            const tableHeaderProps = {
                stateData: {},
                columnInfo: {columnText}
            };
            const columnInfo = {
                title: <TableHeader {...tableHeaderProps} />,
                key,
                render: cellRender,
                width: columnWidthStorageMap[key]?.width || width || 80,
                minWidth: 80,
                fixed: fixed ? fixed : false
            };
            const fieldsNotInGroupLen = fieldsNotInGroup && fieldsNotInGroup.length || 0;
            if (gruopIndex && targetColumnGroups && targetColumnGroups.length >= gruopIndex) {
                // gruopIndex从1开始计算，所以-1
                newFields[fieldsNotInGroupLen + gruopIndex - 1].children.splice(columnIndex || 0, 0, columnInfo);
            }
            else {
                if (columnIndex) {
                    newFields.splice(columnIndex, 0, columnInfo);
                }
                else {
                    newFields.push(columnInfo);
                }
            }
        });
    }
    return newFields;
};
