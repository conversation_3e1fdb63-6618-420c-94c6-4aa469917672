import React from 'react';
import _ from 'lodash';
import cellRenders from './cellRenders';
import {regionColumns} from '../../config/common';

export default ({
    columnName,
    columnInfo = {},
    cellRenderConfig = {},
    splitColumn = '',
    columnCategories
}) => (rowText, column, rowIndex) => {
    const columnData = _.cloneDeep(column);
    const {feConfig, columnType} = columnInfo;
    const {renderKey, formatKey} = feConfig || {};
    const {renders = {}, contentFormats = {}} = cellRenderConfig;
    const propertyCategoriesList = columnCategories.filter(category => category.name === '属性') || [];
    const propertyColumns
        = (propertyCategoriesList && propertyCategoriesList[0] && propertyCategoriesList[0].columns) || [];
    let newFormatKey = formatKey;
    // 为一些列添加默认的format start
    // 在属性分组中的列如果不配置formatKey，则默认显示一行
    if (propertyColumns.indexOf(columnName) > -1) {
        newFormatKey = formatKey || 'singleFormat';
    }
    else if (regionColumns.indexOf(columnName) > -1) {
        newFormatKey = formatKey || 'regionFormat';
    }

    // 为一些列添加默认的format end
    let formater = contentFormats[newFormatKey];
    if (typeof formater === 'function') {
        // 函数处理
        columnData.renderContent = formater({column: columnData, columnInfo, columnName});
    }
    else if (columnType === 'STRING' || (columnName || '').includes('Id')) {
        const field = columnData[columnName];
        if (columnName !== 'date') {
            columnData.renderContent = Array.isArray(field) ? field[0] : field;
        }
        else {
            if (splitColumn) {
                columnData.renderContent = Array.isArray(field) ? field[0] : field;
            }
        }
    }
    const renderName = splitColumn ? 'SplitCellRender' : 'Normal';
    const CustomerRender = {...cellRenders, ...renders}[renderKey || renderName] || cellRenders[renderName];
    const customerRenderProps = {
        column: columnData,
        columnInfo,
        columnName,
        rowText,
        rowIndex
    };
    return columnData.renderContent !== undefined
        ? <span
            style={{
                display: '-webkit-box',
                overflow: 'hidden',
                ...(splitColumn !== columnName ? {'WebkitLineClamp': 2} : {}),
                'WebkitBoxOrient': 'vertical'
            }}
            title={columnData.renderContent || '-'}
        >{columnData.renderContent || '-'}</span>
        : <CustomerRender {...customerRenderProps} />;
};
