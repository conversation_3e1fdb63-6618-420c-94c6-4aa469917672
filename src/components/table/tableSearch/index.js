/**
 * @file 报表表格搜索框
 * <AUTHOR>
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {SearchBox} from '@baidu/one-ui';

export default class TableSearch extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    static propTypes = {
        /** 搜索名 */
        searchName: PropTypes.string,
        searchMetrics: PropTypes.string
    }

    constructor(props, context) {
        super(props, context);

        this.state = {
            searchContent: '',
            options: []
        };
    }

    onChange = event => {
        const value = event.target.value;
        const {config} = this.context;
        const {searchName = ''} = this.props;
        const {filter} = config;
        const {stringFilterMap} = filter;
        const options = Object.keys(stringFilterMap).map(v => {
            return {
                label: `${stringFilterMap[v]}"${value}"${searchName ? '的' + searchName : ''}`,
                value: v
            };
        });

        this.setState({
            searchContent: value,
            options
        });
    }

    onClearClick = () => {
        this.setState({
            searchContent: '',
            options: []
        });
    }

    onSearch = () => {
        this.sendSearchInfo('LIKE');
    }

    handleMenuClick = event => {
        this.sendSearchInfo(event.key);
    }

    sendSearchInfo = type => {
        const {eventHub: {trigger}} = this.context;
        const {searchMetrics} = this.props;
        trigger('sdk.event.table.search', {
            data: {
                content: this.state.searchContent,
                type: type,
                searchMetrics
            }
        });
        this.onClearClick();
    }

    render() {
        const {options, searchContent} = this.state;
        const {searchName = ''} = this.props;
        const placeholder = '请搜索' + searchName;
        return (
            <SearchBox
                size="small"
                value={searchContent}
                options={options}
                placeholder={placeholder}
                onChange={this.onChange}
                onSearch={this.onSearch}
                handleMenuClick={this.handleMenuClick}
                onClearClick={this.onClearClick}
                width={180}
                className="table-search-container"
            />
        );
    }
}