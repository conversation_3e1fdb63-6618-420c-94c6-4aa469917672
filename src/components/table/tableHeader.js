import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Popover, Loading} from '@baidu/one-ui';
import {IconFilterWS} from '@baidu/one-ui-icon';
import {IconSortDesc, IconSortAsc} from 'dls-icons-react';
import ReportSdkTableService from '../../services/TableServices/ReportSdkTableService';
import FilterWraper from './filters/filterWrapper';

/* eslint-disable react/prefer-stateless-function */
export default class TableHeader extends PureComponent {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }
    static propTypes = {
        stateData: PropTypes.object,
        columnInfo: PropTypes.object
    }
    static defaultProps = {
        stateData: {},
        columnInfo: {}
    }
    constructor() {
        super();
        this.state = {
            tipContent: '',
            isFetching: false,
            hasFetched: false
        };
    }
    componentDidMount() {
        this.service = new ReportSdkTableService(this.context);
    }
    // 使用commentKey获取提示文案
    getTipContent = (commentKey, comment, columnText, {isOComment = false, oCommentAppId}) => {
        const {isFetching, tipContent, hasFetched} = this.state;
        const fetchErrorState = () => this.setState({
            tipContent: comment ? comment : columnText,
            isFetching: false,
            hasFetched: true
        });
        // 如果已经请求过 直接返回
        if (hasFetched) {
            return;
        }
        // commentKey存在 且 非请求中 且 从是从O端获取信息
        if (commentKey && !isFetching && isOComment && !tipContent) {
            this.setState({isFetching: true});
            this.service.getCommonKeyContent({params: {helpKey: commentKey, appid: +oCommentAppId}}).then(res => {
                res.data.content
                    ? this.setState({
                        tipContent: res.data.content,
                        isFetching: false,
                        hasFetched: true
                    })
                    : fetchErrorState();
            }).catch(err => {
                fetchErrorState();
            });
        };
        this.setState({
            tipContent: comment ? comment : columnText
        });
    };
    render() {
        const {stateData, columnInfo, tableAreaConfig} = this.props;
        const {
            editingFilterData,
            filterData,
            sortRule,
            filterDropdownVisibleMap,
            filterFromWhere
        } = stateData;
        const {columnText, columnName, comment, commentKey, filterable, sortable, columnType, feConfig} = columnInfo;
        const {textDirection, cellAlign} = feConfig || {};
        const {eventHub, config} = this.context;
        const {filter, classPrefix} = config;
        const {numberTypes} = filter;
        const showSorter = sortRule && sortRule.sortName === columnName;
        const isAscend = sortRule && sortRule[columnName] && sortRule[columnName].isAscend;
        const ascSortCls = showSorter && isAscend ? 'sort-active' : '';
        const descSortCls = showSorter && !isAscend ? 'sort-active' : '';
        const sorterProps = {
            onClick: () => {
                const sortStatus = sortRule[columnName] ? !sortRule[columnName].isAscend : false;
                const newData = isAscend
                    ? {}
                    : {
                        ...sortRule,
                        [columnName]: {
                            isAscend: sortStatus
                        },
                        sortName: columnName
                    };
                eventHub.trigger('sdk.event.filter.sorterChange', {
                    data: newData
                });
            }
        };
        const filterProps = {
            editingFilterData,
            filterData,
            filterDropdownVisibleMap,
            filterFromWhere,
            columnInfo,
            fromWhere: 'tableHeader'
        };
        const setFilterFromWhere = () => {
            eventHub.trigger('sdk.event.filter.fromWhere', {data: 'tableHeader'});
        };
        const filterClass = filterable && (filterData
            && filterData[columnName]
            && filterData[columnName].values
            && filterData[columnName].values.length
            || filterDropdownVisibleMap[columnName])
            ? 'filter-box-active'
            : '';
        const isIdColumn = (columnName || '').includes('Id');
        const isRight = textDirection === 'right' || cellAlign === 'right';
        const isLeft = textDirection === 'left' || cellAlign === 'left';
        const alignClass = ((numberTypes.indexOf(columnType) > -1 && !isIdColumn && !isLeft)
            || isRight)
            ? `${classPrefix}-main-table-header-right`
            : '';
        const renderContent = () => {
            const {isFetching} = this.state;
            const getPopverContent = () => {
                if (isFetching) {
                    return <Loading size="small" tip="正在加载..." />;
                }
                return this.state.tipContent;
            };
            let popoverProps = {
                content: getPopverContent(),
                overlayClassName: `${classPrefix}-main-table-header-tip-layer`,
                onVisibleChange: () => this.getTipContent(commentKey, comment, columnText, tableAreaConfig)
            };
            // comment和commentKey不存在
            if (!commentKey && !comment) {
                return <div className="title-container"><div className="text">{columnText}</div></div>;
            }
            return (<Popover {...popoverProps}>
                <div className="title-container"><div className="text">{columnText}</div></div>
            </Popover>
            );
        };
        const headerRender = (
            <div className={`${classPrefix}-main-table-header ${alignClass}`} onClick={setFilterFromWhere}>
                {
                    renderContent()
                }
                {sortable ? (
                    <div className="sorter-box" {...sorterProps}>
                        <div className={`sort-normal ${(ascSortCls || descSortCls) ? 'sort-normal-active' : ''}`}>
                            <IconSortAsc className={`asc-sorter ${ascSortCls}`} />
                            <IconSortDesc className={`desc-sorter ${descSortCls}`} />
                        </div>
                    </div>
                ) : null}
                {
                    filterable
                        ? (
                            <div className={`filter-box ${filterClass}`}>
                                <FilterWraper {...filterProps}>
                                    <IconFilterWS className="filter-nomal" />
                                </FilterWraper>
                            </div>
                        )
                        : null
                }
            </div>
        );
        return headerRender;
    }
}
