import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import FilterAnchor from './filterAnchor';
import FilterClear from './filterClear';

/* eslint-disable react/prefer-stateless-function */
export default class FilterList extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    static propTypes = {
        tableHeaderData: PropTypes.object.isRequired,
        initFilterData: PropTypes.object.isRequired,
        editingFilterData: PropTypes.object.isRequired,
        filterData: PropTypes.object.isRequired,
        filterDropdownVisibleMap: PropTypes.object.isRequired,
        filterFromWhere: PropTypes.string,
        className: PropTypes.string,
        rowCount: PropTypes.number
    }

    static defaultProps = {
        filterFromWhere: '',
        className: ''
    }

    render() {
        const {
            tableHeaderData,
            initFilterData,
            filterData,
            editingFilterData,
            filterDropdownVisibleMap,
            filterFromWhere,
            className
        } = this.props;
        const {eventHub, config} = this.context;
        const {classPrefix} = config;
        const {columnConfigs} = tableHeaderData;
        const selectedFilters = Object.keys(filterData).filter(key => filterData[key]
            && filterData[key].values
            && filterData[key].values.length);
        const setFilterFromWhere = () => {
            eventHub.trigger('sdk.event.filter.fromWhere', {data: 'filterList'});
        };
        return selectedFilters.length
            ? (
                <div className={`${classPrefix}-filter-list-container ${className}`} onClick={setFilterFromWhere}>
                    {selectedFilters.map(key => {
                        const columnInfo = columnConfigs[key];
                        const filterAnchorProps = {
                            initFilterData,
                            filterData,
                            editingFilterData,
                            filterDropdownVisibleMap,
                            columnInfo,
                            filterFromWhere
                        };
                        return <FilterAnchor key={key} {...filterAnchorProps} />;
                    })}
                    <FilterClear />
                </div>
            )
            : null;
    }
}
