/**
 * @file 筛选条件显示器
 * <AUTHOR>
 * @date 2019/4/22
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {IconTimes} from 'dls-icons-react';
import {tools} from '@baidu/one-ui-pro';
import FilterWraper from './filterWrapper';
import {checkboxFilterMapConfig} from '../../../config/default/filter';
import {regionFilterType, checkboxFilterType} from '../../../config/default/table';

const {newCityRegionName, cityRegionName} = tools.region;

/* eslint-disable react/prefer-stateless-function */
export default class FilterAnchor extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    static propTypes = {
        columnInfo: PropTypes.object.isRequired,
        initFilterData: PropTypes.object.isRequired,
        editingFilterData: PropTypes.object.isRequired,
        filterData: PropTypes.object.isRequired,
        filterDropdownVisibleMap: PropTypes.object.isRequired
    }

    render() {
        const {
            columnInfo,
            initFilterData,
            filterData,
            editingFilterData,
            filterDropdownVisibleMap
        } = this.props;
        let values = '';
        let operator = '';
        let labelNum = 0;
        const {eventHub, config} = this.context;
        const {filterRenderConfig, filter} = config;
        const {
            numberFilterMap,
            stringFilterMap,
            enumFilterMap,
            customFilterMap,
            numberTypes,
            innerCustomFilterMap = {},
            useNewRegionDatasource
        } = filter;
        const {columnName, columnText, columnType, enumValues, feConfig = {}} = columnInfo;
        const filterItem = filterData[columnName];
        const title = columnText;
        const customFilter = feConfig && filterRenderConfig && filterRenderConfig[feConfig.customFilter];
        const realRegionName = useNewRegionDatasource ? newCityRegionName : cityRegionName;
        if (customFilter) {
            // 自定义筛选器
            labelNum = filterItem.labels && filterItem.labels.length;
            values = filterItem.labels && filterItem.labels.join(',');
            operator = customFilterMap[filterItem.operator];
        }
        else if (regionFilterType.indexOf(columnName) > -1) {
            // 地域
            labelNum = filterItem.values && filterItem.values.length;
            values = filterItem.values.map(item => realRegionName[item]).join(',');
            operator = stringFilterMap[filterItem.operator];
        }
        else if (checkboxFilterType.indexOf(columnName) > -1) {
            // 多选筛选
            labelNum = filterItem.values && filterItem.values.length;
            const labels = checkboxFilterMapConfig[columnName];
            const labelMap = labels.reduce((cur, item) => {
                return {
                    ...cur,
                    [item.value]: item.label
                };
            }, {});
            values = filterItem.values.map(v => labelMap[v]).join(',');
            operator = stringFilterMap[filterItem.operator];
        }
        else if (columnType === 'ENUM') {
            // 枚举
            labelNum = filterItem.values && filterItem.values.length;
            values = filterItem.values.map(v => enumValues[v]).join(',');
            operator = enumFilterMap[filterItem.operator];
        }
        else if (columnType === 'STRING') {
            // 字符串
            labelNum = filterItem.values && filterItem.values.length;
            values = filterItem.values.join(',');
            operator = stringFilterMap[filterItem.operator];
        }
        else if (numberTypes.indexOf(columnType) > -1) {
            // 数值
            labelNum = filterItem.values && filterItem.values.length;
            values = filterItem.values.join(',');
            operator = numberFilterMap[filterItem.operator];
        }
        const label = `${operator} ${values}`;
        const clearFilter = () => {
            const fieldFilterInfo = initFilterData && initFilterData[columnName] || {};
            const {operator, connotSubmit} = fieldFilterInfo;
            // 真正清空，values就得为空数组，即使有默认值也得清除
            const newFilterData = {
                ...filterData,
                // [columnName]: initFilterData[columnName]
                [columnName]: {
                    operator,
                    values: [],
                    connotSubmit
                }
            };
            const newEditingFilterData = {
                ...editingFilterData,
                // [columnName]: initFilterData[columnName]
                [columnName]: {
                    operator,
                    values: [],
                    connotSubmit
                }
            };
            eventHub.trigger('sdk.event.filter.clear', {
                data: {
                    filterData: newFilterData,
                    editingFilterData: newEditingFilterData
                }
            });
        };
        const containerClass = filterDropdownVisibleMap[columnName] ? ' filter-result-label-active' : '';
        const innerCustomFilterFormat = innerCustomFilterMap[feConfig.customFilter]
            && innerCustomFilterMap[feConfig.customFilter].filterLabelFormat;
        const innerCustomFilterLabel = typeof innerCustomFilterFormat === 'function'
            ? innerCustomFilterFormat(filterItem.values) || ''
            : innerCustomFilterFormat;
        const popAnchorProps = {
            className: 'filter-label',
            title: innerCustomFilterLabel || `${title}：${label}`
        };
        return (
            <div className={`filter-result-label${containerClass}`}>
                <FilterWraper {...this.props} fromWhere="filterList">
                    {
                        innerCustomFilterLabel
                            ? (
                                <span {...popAnchorProps}>
                                    <span className="filter-anchor-label">{innerCustomFilterLabel}</span>
                                </span>
                            )
                            : (
                                <span {...popAnchorProps}>
                                    <span className="filter-anchor-label">{title}：{label}</span>
                                    {
                                        labelNum && labelNum > 1 && (
                                            <span className="filter-anchor-number">
                                                共{labelNum}项
                                            </span>
                                        )
                                    }
                                </span>
                            )
                    }
                </FilterWraper>
                <span className="close-btn">
                    <IconTimes className="close-icon" onClick={clearFilter} />
                </span>
            </div>
        );
    }
}
