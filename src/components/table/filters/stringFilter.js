/**
 * @file 字符串筛选器
 * <AUTHOR>
 * @date 2020/07/13
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Select} from '@baidu/one-ui';
import {TextLine} from '@baidu/one-ui-pro';

const Option = Select.Option;
const stringTitle = '条件';

/* eslint-disable react/prefer-stateless-function */
export default class NumberFilter extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    static propTypes = {
        field: PropTypes.string.isRequired,
        options: PropTypes.array.isRequired,
        editingFilterData: PropTypes.object.isRequired,
        feConfig: PropTypes.object
    }
    static defaultProps = {
        feConfig: {}
    }
    constructor(props, context) {
        super(props, context);
        const {feConfig = {}, editingFilterData, field} = props;
        const {defaultFilterableOperator, multipleCount} = feConfig;
        const {config} = context;
        const {filter} = config;
        const currentFilterOperator = editingFilterData[field].operator;
        const {stringFilterMaxLine, stringSingleFilters, defaultFilterOperatorMap} = filter;
        const defaultOperator = currentFilterOperator || defaultFilterableOperator || defaultFilterOperatorMap.STRING;
        const multipleMaxLine = multipleCount || stringFilterMaxLine;
        this.state = {
            maxLine: stringSingleFilters.indexOf(defaultOperator) === -1 ? multipleMaxLine : 1,
            errorMessage: ''
        };
    }
    handleChangeMode = operator => {
        const {field, editingFilterData} = this.props;
        const {eventHub, config} = this.context;
        const {trigger} = eventHub;
        const editingFilter = editingFilterData[field];
        const values = editingFilterData[field]?.values;
        const {filter} = config;
        const {stringSingleFilters, stringFilterMaxLine} = filter;
        let newEditingFilterData = {};
        let errorMessage = '';
        if (stringSingleFilters.indexOf(operator) > -1) {
            this.setState({
                maxLine: 1
            });
            errorMessage = values.length > 1 ? `${stringTitle}已超过${values.length - 1}个` : '';
            if (values.length > 1) {
                newEditingFilterData = {
                    ...editingFilterData,
                    [field]: {
                        ...editingFilter,
                        operator,
                        connotSubmit: true
                    }
                };
            }
            else {
                newEditingFilterData = {
                    ...editingFilterData,
                    [field]: {
                        ...editingFilter,
                        operator
                    }
                };
            }
            this.setState({errorMessage});
            trigger('sdk.event.filter.filterChanging', {data: newEditingFilterData});
        }
        else {
            this.setState({
                maxLine: stringFilterMaxLine
            });
            const errorMessage = values.length < (stringFilterMaxLine + 1)
                ? ''
                : `${stringTitle}已超过${values.length - stringFilterMaxLine}个`;
            const connotSubmit = values.length > 10;
            this.setState({errorMessage});
            newEditingFilterData = {
                ...editingFilterData,
                [field]: {
                    ...editingFilter,
                    operator,
                    connotSubmit
                }
            };
            trigger('sdk.event.filter.filterChanging', {data: newEditingFilterData});
        }
    }
    render() {
        const {options, field, editingFilterData, feConfig = {}} = this.props;
        const {errorMessage, maxLine} = this.state;
        const {eventHub, config} = this.context;
        const {defaultFilterableOperator} = feConfig;
        const {filter} = config;
        const {stringFilterMaxLength} = filter;
        const {trigger} = eventHub;
        const editingFilter = editingFilterData[field];
        const selectProps = {
            value: editingFilter?.operator,
            className: 'operator-selector',
            width: 120,
            onChange: e => {
                this.handleChangeMode(e);
            }
        };
        const textLineProps = {
            title: stringTitle,
            value: editingFilter?.values,
            maxLine: maxLine,
            maxLen: stringFilterMaxLength,
            minLine: 1,
            width: 304,
            height: 172,
            delLabel: '清空',
            onChange: e => {
                const validValues = e.value.filter(v => v || v === '0');
                const textLineErrorMsg = Object.keys(e.errorMessageArrayObj);
                const connotSubmit = (textLineErrorMsg && textLineErrorMsg.length)
                    || (validValues && (validValues.length === 0 || validValues.length > maxLine));
                let errorMessage = '';
                const newEditingFilterData = {
                    ...editingFilterData,
                    [field]: {
                        ...editingFilter,
                        values: e.value,
                        connotSubmit
                    }
                };
                if (validValues.length > maxLine) {
                    errorMessage = `${stringTitle}已超过${validValues.length - maxLine}个`;
                }
                else if (validValues.length === 0) {
                    errorMessage = `请输入${stringTitle}`;
                }
                this.setState({errorMessage});
                trigger('sdk.event.filter.filterChanging', {data: newEditingFilterData});
            }
        };
        return (
            <div className="string-filter-container">
                <div className="string-selector">
                    <Select {...selectProps}>
                        {
                            options.map(option => (
                                <Option key={option.value} value={option.value}>{option.label}</Option>
                            ))
                        }
                    </Select>
                </div>
                <TextLine {...textLineProps} />
                {errorMessage && <div className="error-msg">{errorMessage}</div>}
            </div>
        );
    }
}
