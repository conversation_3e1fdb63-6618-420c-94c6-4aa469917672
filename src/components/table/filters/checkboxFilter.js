/**
 * @file 多选框筛选器
 * <AUTHOR>
 * @date 2020/12/15
 */

import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Checkbox} from '@baidu/one-ui';

const CheckboxGroup = Checkbox.Group;

/* eslint-disable react/prefer-stateless-function */
export default class RegionFilter extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired
    }
    static propTypes = {
        field: PropTypes.string.isRequired,
        options: PropTypes.oneOfType([PropTypes.array, PropTypes.object]).isRequired,
        editingFilterData: PropTypes.object.isRequired
    }
    render() {
        const {field, editingFilterData, options} = this.props;
        const {eventHub} = this.context;
        const {trigger} = eventHub;
        const filter = editingFilterData[field] || [];
        const checkboxProps = {
            value: filter.values,
            className: 'checkbox-group',
            direction: 'column',
            options,
            onChange: e => {
                const newEditingFilterData = {
                    ...editingFilterData,
                    [field]: {
                        ...filter,
                        values: e,
                        operator: 'IN'
                    }
                };
                trigger('sdk.event.filter.filterChanging', {
                    data: newEditingFilterData
                });
            }
        };
        return (
            <div className="checkbox-filter-container">
                <CheckboxGroup {...checkboxProps} />
            </div>
        );
    }
}
