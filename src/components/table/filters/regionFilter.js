/**
 * @file 地域筛选器
 * <AUTHOR>
 * @date 2020/12/15
 */

import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Region, tools} from '@baidu/one-ui-pro';

const {
    cityRegionName,
    newCityRegionName,
    directCityCode,
    newRegionFiliationMapWithCity,
    fcNewRegionCityAncestorsMap,
    singleRegionExceptionKeys,
    topLevel,
    ancestorsRegionMap,
    cityRegionFiliationMapWithCountry
} = tools.region;

const onlyProvince = newRegionFiliationMapWithCity['998']
    .filter(r => singleRegionExceptionKeys.indexOf(+r) === -1 && directCityCode.indexOf(+r) === -1);

/* eslint-disable react/prefer-stateless-function */
export default class RegionFilter extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    static propTypes = {
        field: PropTypes.string.isRequired,
        editingFilterData: PropTypes.object.isRequired
    }
    render() {
        const {field, editingFilterData} = this.props;
        const {eventHub, config} = this.context;
        const {trigger} = eventHub;
        const filter = editingFilterData[field] || [];
        const useNewRegionDatasource = config.filter.useNewRegionDatasource;
        const regionProps = {
            selectedValue: filter.values,
            onChange: e => {
                let selectedProvince = e.selectedValue.filter(s => topLevel.indexOf(+s) === -1);
                if (field === 'provinceCityName') {
                    // 市级需要取交集(去掉省、大区Id)
                    selectedProvince = e.selectedValue
                        .filter(s => onlyProvince.indexOf(+s) === -1 && topLevel.indexOf(+s) === -1);
                }
                const newEditingFilterData = {
                    ...editingFilterData,
                    [field]: {
                        ...filter,
                        values: selectedProvince,
                        operator: 'IN'
                    }
                };
                trigger('sdk.event.filter.filterChanging', {
                    data: newEditingFilterData
                });
            },
            showCity: field === 'provinceCityName',
            showDistrict: false,
            customRegion: {
                regionNames: useNewRegionDatasource ? newCityRegionName : cityRegionName,
                regionFiliationMap: useNewRegionDatasource
                    ? newRegionFiliationMapWithCity : cityRegionFiliationMapWithCountry,
                ancestorsRegionMap: useNewRegionDatasource ? fcNewRegionCityAncestorsMap : ancestorsRegionMap,
                topLevel: [998, 999]
            }
        };
        return (
            <div className="region-filter-container">
                <Region {...regionProps} />
            </div>
        );
    }
}
