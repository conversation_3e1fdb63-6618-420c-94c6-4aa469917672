import React from 'react';
import PropTypes from 'prop-types';
import {Button} from '@baidu/one-ui';

const FilterClear = (e, context) => {
    const onClick = () => {
        context.eventHub.trigger('sdk.event.filter.clearAll');
    };
    return (
        <Button className="clear-all-btn" type="text-strong" size="medium" onClick={onClick}>
            清空
        </Button>
    );
};

FilterClear.contextTypes = {
    eventHub: PropTypes.object
};

export default FilterClear;
