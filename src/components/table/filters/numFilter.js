/**
 * @file 数字筛选器
 * <AUTHOR>
 * @date 2020/07/07
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {NumberInput, Select} from '@baidu/one-ui';

const Option = Select.Option;
const filterConfig = ['type', 'fixed'];

/* eslint-disable react/prefer-stateless-function */
export default class NumberFilter extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }
    static propTypes = {
        field: PropTypes.string.isRequired,
        options: PropTypes.array.isRequired,
        editingFilterData: PropTypes.object.isRequired,
        feConfig: PropTypes.object
    }
    static defalutProps = {
        feConfig: {}
    }
    render() {
        const {options, field, editingFilterData, feConfig = {}} = this.props;
        const {eventHub} = this.context;
        const {trigger} = eventHub;
        const filter = editingFilterData[field];
        const selectProps = {
            value: editingFilterData[field]?.operator,
            className: 'operator-selector',
            width: 60,
            onChange: e => {
                const newEditingFilterData = {
                    ...editingFilterData,
                    [field]: {
                        ...filter,
                        operator: e
                    }
                };
                trigger('sdk.event.filter.filterChanging', {data: newEditingFilterData});
            }
        };
        let numberInputProps = {
            min: 0,
            placeholder: '请输入数字',
            value: editingFilterData[field]?.values[0],
            width: 130,
            onChange: e => {
                const newEditingFilterData = {
                    ...editingFilterData,
                    [field]: {
                        ...filter,
                        values: [e.target.value]
                    }
                };
                trigger('sdk.event.filter.filterChanging', {
                    data: newEditingFilterData
                });
            }
        };
        const configKeys = Object.keys(feConfig).filter(config => filterConfig.indexOf(config) > -1);
        const configMap = configKeys.reduce((memo, key) => {
            memo[key] = feConfig[key];
            return memo;
        }, {});
        if (configKeys.length > 0) {
            numberInputProps = {
                ...numberInputProps,
                ...configMap
            };
        }
        return (
            <div className="number-filter-container">
                <Select {...selectProps}>
                    {
                        options.map(option => <Option key={option.value} value={option.value}>{option.label}</Option>)
                    }
                </Select>
                <NumberInput {...numberInputProps} />
            </div>
        );
    }
}
