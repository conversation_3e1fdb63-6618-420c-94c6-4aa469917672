/**
 * @file 枚举筛选器
 * <AUTHOR>
 * @date 2020/07/07
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Checkbox} from '@baidu/one-ui';

/* eslint-disable react/prefer-stateless-function */
export default class EnumFilter extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired
    }
    static propTypes = {
        field: PropTypes.string.isRequired,
        options: PropTypes.array.isRequired,
        editingFilterData: PropTypes.object.isRequired
    }
    render() {
        const {options, field, editingFilterData} = this.props;
        const {eventHub} = this.context;
        const {trigger} = eventHub;
        const filter = editingFilterData[field];
        return (
            <div className="enum-filter-container">
                {
                    options.map(item => {
                        const selected = filter?.values?.indexOf(item.value) > -1;
                        const props = {
                            checked: selected,
                            onChange: () => {
                                const newEditingFilterData = {
                                    ...editingFilterData,
                                    [field]: {
                                        ...filter,
                                        values: selected
                                            ? filter.values.filter(i => i !== item.value)
                                            : [].concat(filter.values, [item.value])
                                    }
                                };
                                trigger('sdk.event.filter.filterChanging', {data: newEditingFilterData});
                            }
                        };
                        return (
                            <div key={item.value} style={{height: 30}}>
                                <Checkbox {...props}>{item.label}</Checkbox>
                            </div>
                        );
                    })
                }
            </div>
        );
    }
}
