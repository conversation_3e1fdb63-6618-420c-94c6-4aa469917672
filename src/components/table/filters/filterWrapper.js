import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Button, Dropdown} from '@baidu/one-ui';
import getFilterConfig from './getFilterConfig';

/* eslint-disable react/prefer-stateless-function */
export default class FilterWraper extends PureComponent {
    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired
    }

    static propTypes = {
        columnInfo: PropTypes.object.isRequired,
        editingFilterData: PropTypes.object.isRequired,
        filterData: PropTypes.object.isRequired,
        filterDropdownVisibleMap: PropTypes.object,
        filterFromWhere: PropTypes.string,
        fromWhere: PropTypes.string
    }

    static defaultProps = {
        filterDropdownVisibleMap: {},
        filterFromWhere: '',
        fromWhere: ''
    }

    render() {
        const {
            editingFilterData,
            filterData,
            filterDropdownVisibleMap,
            filterFromWhere,
            columnInfo,
            fromWhere
        } = this.props;
        const {columnName, columnText, columnType, feConfig = {}} = columnInfo;
        const {customFilter} = feConfig;
        const {eventHub, config} = this.context;
        const {filterRenderConfig, filter, classPrefix} = config;
        const {trigger} = eventHub;
        const CustomFilter = filterRenderConfig && filterRenderConfig[customFilter];
        const renderConfig = getFilterConfig({columnInfo, editingFilterData, filter});
        const enterBtnDisabled = columnType !== 'ENUM' && editingFilterData
            && editingFilterData[columnName]
            && editingFilterData[columnName].values.length === 0
            || (editingFilterData && editingFilterData[columnName] && editingFilterData[columnName].connotSubmit);
        const enterBtnProps = {
            type: 'text-strong',
            size: 'medium',
            style: {padding: 0},
            disabled: enterBtnDisabled,
            onClick: () => {
                // 过滤掉空值，但是数值0及字符串0都不能过滤
                const validValues = editingFilterData[columnName].values.filter(v => v || String(v) === '0');
                editingFilterData[columnName].values = validValues;
                const newFilterData = {
                    ...filterData,
                    [columnName]: JSON.parse(JSON.stringify(editingFilterData[columnName]))
                };
                trigger('sdk.event.filter.filterChanged', {data: newFilterData});
                trigger('sdk.event.filter.filterLayerChange', {
                    data: {
                        columnName,
                        visible: false
                    }
                });
            }
        };
        const cancelBtnProps = {
            type: 'text-aux',
            size: 'medium',
            style: {padding: 0, marginLeft: 16},
            onClick: () => {
                trigger('sdk.event.filter.filterChangeCancel');
                trigger('sdk.event.filter.filterLayerChange', {
                    data: {
                        columnName,
                        visible: false
                    }
                });
            }
        };

        const columnTypeLayerClass = `${classPrefix}-table-filter-dropdown-layer-${columnType.toLowerCase()}`;
        const columnTypeClass = `${classPrefix}-filter-container-${columnType.toLowerCase()}`;
        const customFilterInitData = {
            [columnName]: filterData && filterData[columnName]
        };
        const targetRender = (renderConfig[columnType] && renderConfig[columnType].content) || null;
        const visible = filterFromWhere === fromWhere && filterDropdownVisibleMap[columnName];
        const dropdownProps = {
            trigger: 'click',
            visible,
            overlayClassName: `${classPrefix}-table-filter-dropdown-layer ${columnTypeLayerClass}`,
            placement: 'bottomLeft',
            onVisibleChange: v => {
                trigger('sdk.event.filter.filterLayerChange', {
                    data: {
                        columnName,
                        visible: v
                    }
                });
            },
            overlay: CustomFilter
                ? <div>{visible ? <CustomFilter initData={customFilterInitData} /> : null}</div>
                : (
                    <div className={`${classPrefix}-filter-container ${columnTypeClass}`}>
                        <div className="filter-title">
                            {columnText}
                        </div>
                        {targetRender}
                        <div className={`button-container button-container-${columnType.toLowerCase()}`}>
                            <div className="line-sperator" />
                            <Button {...enterBtnProps}>应用</Button>
                            <Button {...cancelBtnProps}>取消</Button>
                        </div>
                    </div>
                )
        };
        return (
            <Dropdown {...dropdownProps}>
                {this.props.children}
            </Dropdown>
        );
    }
}
