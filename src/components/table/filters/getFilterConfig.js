import React from 'react';
import NumberFilter from './numFilter';
import EnumFilter from './enumFilter';
import StringFilter from './stringFilter';
import RegionFilter from './regionFilter';
import CheckboxFilter from './checkboxFilter';
import {checkboxFilterMapConfig} from '../../../config/default/filter';
import {regionFilterType, checkboxFilterType} from '../../../config/default/table';

export default ({columnInfo, editingFilterData, filter = {}}) => {
    let enumList = [];
    let stringList = [];
    let numberList = [];
    const {numberFilterMap, stringFilterMap, numberTypes} = filter;
    const {columnName, columnType, enumValues, filterableOperators, feConfig} = columnInfo;
    if (columnType === 'ENUM') {
        enumList = enumValues && Object.keys(enumValues).map(enumItem => {
            return {
                label: enumValues[enumItem],
                value: enumItem
            };
        });
    }
    if (columnType === 'STRING' && filterableOperators) {
        stringList = filterableOperators.map(operator => {
            return {
                value: operator,
                label: stringFilterMap[operator]
            };
        });
    }
    if (numberTypes.indexOf(columnType) > -1 && filterableOperators) {
        numberList = filterableOperators.map(operator => {
            return {
                value: operator,
                label: numberFilterMap[operator]
            };
        });
    }
    const basicProps = {
        field: columnName,
        editingFilterData,
        feConfig
    };
    const enumFilterProps = {
        ...basicProps,
        options: enumList
    };
    const stringFilterProps = {
        ...basicProps,
        options: stringList
    };
    const numberFilterProps = {
        ...basicProps,
        options: numberList
    };
    if (regionFilterType.indexOf(columnName) > -1) {
        // 省、市 特殊逻辑
        return {
            [columnType]: {
                content: <RegionFilter {...basicProps} />
            }
        };
    }
    if (checkboxFilterType.indexOf(columnName) > -1) {
        // 多选筛选组件特殊逻辑
        const checkboxList = checkboxFilterMapConfig[columnName];
        return {
            [columnType]: {
                content: <CheckboxFilter {...basicProps} options={checkboxList} />
            }
        };
    }
    return {
        ENUM: {
            values: values => values.map(v => enumValues[v]),
            content: <EnumFilter {...enumFilterProps} />
        },
        STRING: {
            content: <StringFilter {...stringFilterProps} />
        },
        INTEGER: {
            content: <NumberFilter {...numberFilterProps} />
        },
        LONG: {
            content: <NumberFilter {...numberFilterProps} />
        },
        BIG_INTEGER: {
            content: <NumberFilter {...numberFilterProps} />
        },
        DOUBLE: {
            content: <NumberFilter {...numberFilterProps} />
        }
    };
};
