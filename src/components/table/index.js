/**
 * @file 报表 - 表格
 * <AUTHOR>
 */
import React, {PureComponent} from 'react';
import dayjs from 'dayjs';
import {IconSync, IconBarChartSquareSolid, IconTimes} from 'dls-icons-react';
import PropTypes from 'prop-types';
import {isNumber, pick, isEqual, noop, keyBy} from 'lodash';
import {Table, Pagination, Button, Toast, Alert} from '@baidu/one-ui';
import {SortableSelector, SortableSelectorV2} from '@baidu/one-ui-pro';
import ReportSdkTableService, {
    GET_TABLE_DATA_URL,
    GET_COMPARE_TABLE_DATA_URL,
    GET_SPLIT_TABLE_DATA_URL
} from '../../services/TableServices/ReportSdkTableService';
import {
    getTableHeaderParams,
    getInitFilterData,
    getCustomMarkFieldsParams,
    getTableDataParams,
    formatApiData,
    formatSplitApiData,
    getInitCustomerColumnData,
    getTableReportType,
    getTableExtraColumns,
    getIndicatorInfo,
    getRowSelectionOption,
    logDataFetchFail,
    logDataFetched,
    getMemoPageSize,
    setMemoPageSize
} from '../../utils/table';
import {getTargetDateOfMs, getTargetEndDate} from '../../utils/filterArea';
import {getApiToast, getApiError} from '../../utils/common';
import {INFINITY} from '../../config/default/cell';
import {
    defaultBottomScroll,
    defaultScroll,
    maxTick,
    tickTime,
    templateKey,
    templateCountLimit,
    templateNameLimit,
    templateValue
} from '../../config/default/table';
import SdkDatePicker from '../datePicker';
import {datePlacementConfig} from '../../config/common';
import getTableFieldsMap from './tableFieldsFactory';
import FilterList from './filters/filterList';
import DownloadEmail from './downloadEmail';
import TableSearch from './tableSearch';
import Indicator from './indicator';
import Split from './split';

const TIME_OUT_NUM = 26;
const SERVICE_PATHS = {
    getTableData: GET_TABLE_DATA_URL,
    getCompareTableData: GET_COMPARE_TABLE_DATA_URL,
    getSplitTableData: GET_SPLIT_TABLE_DATA_URL
};
const markDotColor = {color: '#00BF5C'};
const markTipLabel = '标记为最近7天内有数指标';
const newMarkFieldsTip = <span>“<IconBarChartSquareSolid style={markDotColor} />”{markTipLabel}</span>;
const markFieldsTip = <span>“<i className="mark-dot" />”{markTipLabel}</span>;
const defaultCustomColumnProps = {
    buttonProps: {type: 'basic', style: {width: 120}}
};
const defaultCustomColumnV2Props = {
    groupSidenav: true,
    templateNameInputProps: {maxLen: templateNameLimit},
    buttonProps: {size: 'small'}
};
export default class ReportTable extends PureComponent {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }
    preTableData = null
    preReportType = null
    preServiceType = null
    preFetchStatus = {}
    constructor(props, context) {
        super(props, context);
        const {pagination} = context.config;
        const {mainAreaConfig: {tableAreaConfig}} = this.props;
        const {
            defaultIndicator, tableIndicatorList, tableReportType, memoPageSize
        } = tableAreaConfig || {};
        const {
            additionFields,
            indicatorExternalFilters,
            indicatorTableSupportCommonFilter
        } = getIndicatorInfo(defaultIndicator || [], tableIndicatorList);
        const userId = context.hairuo.userId;

        let columnWidthStorageMap = {};

        const storageKey = this.getColumnWidthStorageKey();

        try {
            columnWidthStorageMap = storageKey ? JSON.parse(localStorage.getItem(storageKey)) || {} : {};
        }
        catch (error) {
            console.error('从localStorage获取数据时出错:', error);
        }


        this.state = {
            tableHeaderData: {},
            initFilterData: {},
            initCustomerColumnData: { // 自定义列相关状态
                originFields: [],
                selectedFields: [],
                fieldMap: {},
                groups: [],
                conflictFields: []
            },
            initCustomerTemplateData: { // 自定义列模版相关状态
                currentTemplate: templateKey.default, // 当前使用的模版
                templateList: [] // 模版列表
            },
            // filterData表示筛选数据，是一个对象：
            // {click: {operator: 'GT', values: [6]}, unitNameStatus: {operator: 'LIKE', values: ['测试']}}
            // 这个对象包含所有的可筛选项及智能搜索项的信息
            // 如果某一列的数据中values不为空数组，则表示对应列有筛选条件，需要带入表格数据请求参数中
            filterData: {},
            // 结构同上，这个对象表示的是用户在编辑中的筛选条件
            editingFilterData: {},
            // 晒选框可见性map对象
            filterDropdownVisibleMap: {},
            pageNo: 1,
            pageSize: memoPageSize && getMemoPageSize(userId, tableReportType)
                || pagination.pageSize,
            totalCount: 0,
            listData: [],
            lastUpdateTime: dayjs().format('HH:mm:ss'),
            sumData: {},
            sortRule: {},
            // 筛选框的来源，目前有两个：表头（tableHeader）、筛选结果（filterList）
            // 结合filterDropdownVisibleMap来显示唯一的晒选框
            filterFromWhere: '',
            // 报表公共筛选信息
            commonFilterInfo: {},
            // 表格指标数据
            tableIndicatorKey: '',
            // 细分数据
            splitColumn: '',
            chartAreaVisible: true,
            // 指标、筛选项操作附加的列
            additionFields,
            // 是否需要反馈请求参数
            isFeedbackRequest: false,
            // 表格指标选中list
            tableIndicatorKeyList: [],
            // 表格指标选中的外带参数
            indicatorExternalFilters,
            // 表格筛选指标后，是否支持外部filter
            indicatorTableSupportCommonFilter,
            // 选中的表格行keys
            selectedRowKeys: [],
            // 表格是否全选 暂时不加
            // isCheckAll: false,
            // 自定义列标记项
            markFields: [],
            // 是否正在下载
            downloading: false,
            // 下载的个人设置信息；包含是否勾选已删除物料和邮件信息等
            downloadSettings: {},
            // 是否展示表格
            tableVisible: true,
            // 表格数据总数
            smartRowCount: null,
            // 表格列宽存储map
            columnWidthStorageMap
        };
    }

    static propTypes = {
        mainAreaConfig: PropTypes.object
    }

    static defaultProps = {
        mainAreaConfig: {}
    }

    componentDidMount() {
        this.preTableData = [];
        const {eventHub: {on}} = this.context;
        // 表格forceUpdate，用于自定义列渲染有数据更新的情况
        on('sdk.event.table.forceUpdate', this.handleTableForceUpdate);
        // 点击了筛选器的确定按钮，筛选条件已经改变
        on('sdk.event.filter.filterChanged', this.handleFilterChanged);
        // 未点击了筛选器的确定按钮，还在编辑筛选指标
        on('sdk.event.filter.filterChanging', this.handleFilterChanging);
        // 取消筛选
        on('sdk.event.filter.filterChangeCancel', this.handleFilterCancel);
        // 控制筛选面板状态
        on('sdk.event.filter.filterLayerChange', this.handleFilterLayer);
        // 更改排序指标
        on('sdk.event.filter.sorterChange', this.handleSorter);
        // 清空筛选条件
        on('sdk.event.filter.clearAll', this.handleFilterClearAll);
        // 删除单个筛选条件
        on('sdk.event.filter.clear', this.handleFilterClear);
        // 设置晒选器来源
        on('sdk.event.filter.fromWhere', this.handleFilterFromWhere);
        // 搜索框搜索
        on('sdk.event.table.search', this.handleSearch);
        // 同步外部公共筛选条件给表格
        on('sdk.event.table.changeCommonFilter', this.handleChangeCommonFilter);
        // 表格上方指标改变时同步外部公共筛选条件给表格
        on('sdk.event.table.tableIndicatorChangeCommonFilter', this.handleChangeCommonFilter);
        // 监听自定义筛选器的确定按钮事件
        on('sdk.event.filter.custom.confirm', this.handleCustomFilterConfirm);
        // 监听自定义筛选器的取消按钮事件
        on('sdk.event.filter.custom.cancel', this.handleCustomFilterCancel);
        // 多账户有初始值的时候，需要多账户接口、报表配置接口、报表数据接口串行
        on('sdk.event.table.getMetaData', this.handleGetTableMetaData);
        // 监听表格左上方指标change事件
        on('sdk.event.table.indicator.change', this.handleTableIndicatorChange);
        // 监听细分事件
        on('sdk.event.table.split.change', this.handleSplitChange);
        // 监听自定义列的数据传给sdk
        on('sdk.event.main.table.custom.field.data', this.handleCustomFieldsData);
        // 表格非数据接口初始化配置
        on('sdk.event.table.setCofing', this.handleSetCofing);
        // 清空表格选中行数据
        on('sdk.event.table.clear.selected.rows', this.handleClearTableSelectedRows);
        // 监听自定义筛选器增加筛选条件事件
        on('sdk.event.filter.add.filters', this.handleCustomFilterAddFilters);
        // 表格调起下载
        on('sdk.event.table.download', this.handleDownload);
        // 根据自定义列去掉不在自定义列中的字段的筛选组件和排序
        on('sdk.event.save.custom.columns', this.handleCustomfields);
        // 重置列宽
        on('sdk.event.table.resetColumnsWidth', this.onResetColumnsWidth);
        // 清除下载
        on('sdk.event.table.syncDownloadClear', this.handleClearSyncDownload);

        this.service = new ReportSdkTableService(this.context);
        const {mainAreaConfig: {tableAreaConfig}} = this.props;
        const {isInitTable, isNeedDownloadSetting, isNeedFilterEmailDownload, defaultCloseTable} = tableAreaConfig;
        if (defaultCloseTable) {
            this.setState({
                tableVisible: false
            });
        }
        if (isInitTable !== false && !this.context.config.mccInfo?.initialSelectedCount) {
            this.handleGetTableMetaData();
        }
        // 如果下载的时候需要根据客户上一次设置过的选项下载，需要先请求设置信息
        if (isNeedDownloadSetting) {
            this.service.getPersonalDownloadSetup({
                params: {}
            }).then(res => {
                if (res.data) {
                    this.setState({
                        downloadSettings: res.data
                    });
                }
            });
        }
    }
    componentWillUnmount() {
        this.handleClearSyncDownload();
    }

    getColumnWidthStorageKey = () => {
        const {mainAreaConfig: {tableAreaConfig}} = this.props;
        const {columnWidthStorageKey, tableReportType} = tableAreaConfig || {};
        return columnWidthStorageKey || (tableReportType ? `cube-sdk-column-${tableReportType}` : '');
    }

    setColumnWidthStorageMap = columnWidthStorageMap => {
        this.setState({columnWidthStorageMap});
        const key = this.getColumnWidthStorageKey();
        if (key) {
            localStorage.setItem(key, JSON.stringify(columnWidthStorageMap));
        }
    }

    onTableDragEnd = columns => {
        const columnWidthStorageMap = keyBy(columns, 'key');
        this.setColumnWidthStorageMap(columnWidthStorageMap);
        const {eventHub: {fire}} = this.context;
        fire('sdk.event.enter.log', {
            hitType: 'table-column-width-change'
        });
    }

    onResetColumnsWidth = () => {
        this.setColumnWidthStorageMap({});
    }

    handleDidPaint = () => {
        const listData = this.state.listData;
        if (!isEqual(listData, this.preTableData)) {
            const {eventHub: {fire}} = this.context;
            this.preTableData = listData;
            fire('sdk.event.enter.log', {
                hitType: 'table-data-painted',
                extra: {
                    serviceType: this.preServiceType,
                    reportType: this.preReportType
                }
            });
            this.preServiceType && fire('sdk.event.enter.log', {
                hitType: 'table-data-painted-new',
                extra: {
                    serviceType: this.preServiceType,
                    reportType: this.preReportType,
                    'fetch_status': this.preFetchStatus.status,
                    'fetch_code': this.preFetchStatus.code || 0,
                    'fetch_columns_count': this.preFetchStatus.columnCount || 0,
                    'fetch_rows_count': this.preFetchStatus.rowCount || 0
                }
            });
        }
    }

    handleTableForceUpdate = () => {
        this.forceUpdate();
    }

    handleClearTableSelectedRows = () => {
        const {eventHub: {trigger}} = this.context;
        const {
            mainAreaConfig: {tableAreaConfig}
        } = this.props;
        this.setState({
            selectedRowKeys: []
        }, () => {
            trigger('sdk.event.table.selected.rows', {
                data: [],
                tableReportType: tableAreaConfig && tableAreaConfig.tableReportType
            });
        });
    }

    handleSetCofing = ({data}) => {
        const {
            metaData = {},
            commonFilterInfo = {},
            isFeedbackRequest = false,
            extra = {}
        } = data;
        const {sortRule = {}, initFilter = {}} = extra;
        const initFilterData = getInitFilterData(metaData, this.context, this.props);
        const mergeInitFilterData = {...initFilterData, ...initFilter};
        const {initCustomerColumnData} = getInitCustomerColumnData({metaData, context: this.context});
        this.setState({
            tableHeaderData: metaData,
            initFilterData: mergeInitFilterData,
            initCustomerColumnData,
            filterData: mergeInitFilterData,
            editingFilterData: mergeInitFilterData,
            isFeedbackRequest,
            sortRule
        }, () => {
            this.tableContainerRef && this.tableContainerRef.updateColumnWidths();
            this.handleChangeCommonFilter({data: commonFilterInfo});
        });
    }

    handleCustomFieldsData = ({data}) => {
        const fields = Object.keys(data) || [];
        const {listData} = this.state;
        const newListData = listData.map((item, index) => {
            if (index !== listData.length - 1) {
                fields.map(field => {
                    const {relatedColumn, customData = {}} = data[field];
                    item[field] = customData[item[relatedColumn]] || [];
                });
            }
            return item;
        });
        this.setState({
            listData: newListData
        });
    }

    handleChangeCommonFilter = ({data}) => {
        const {compareStartDate, filterFields} = data;
        const newState = compareStartDate
            ? {
                commonFilterInfo: {
                    ...data
                },
                splitColumn: ''
            }
            : {
                commonFilterInfo: {
                    ...data
                }
            };
        newState.pageNo = 1;
        this.setState(newState, () => {
            this.handleGetTableData();
            filterFields
                && filterFields.length
                && this.tableContainerRef
                && this.tableContainerRef.updateColumnWidths();
        });
    }

    // 去掉不在自定义列中的筛选条件和排序
    handleCustomfields = ({data}) => {
        const {fields = []} = data;
        const {filterData, sortRule} = this.state;
        const filterDataKeys = Object.keys(filterData);
        const {sortName} = sortRule;
        const validFilterDataKeys = filterDataKeys.filter(fItem => fields.indexOf(fItem) > -1);
        const newFilterData = pick(filterData, validFilterDataKeys);
        if (fields.indexOf(sortName) === -1) {
            this.setState({
                filterData: newFilterData,
                editingFilterData: newFilterData,
                sortRule: {}
            });
        }
        else {
            this.setState({filterData: newFilterData, editingFilterData: newFilterData});
        }
    }

    handleFilterChanged = ({data}) => {
        this.setState({filterData: data, pageNo: 1}, () => {
            this.handleGetTableData();
        });
    }

    handleGetTableMetaData = ({isChangeCustomFields = false} = {}) => {
        const {
            topTabKey,
            getMainTableComparable,
            onChangeTableLoading,
            mainAreaConfig: {tableAreaConfig}
        } = this.props;
        const {tableIndicatorKey} = this.state;
        const {eventHub: {fire, trigger}, token} = this.context;
        const {reportArea, smartQueryAreaConfig = {}} = this.context.config;
        const {sortRule = {}, topCount} = smartQueryAreaConfig;
        const {withNewSortableSelector, handleCustomInfo} = tableAreaConfig;
        const reportType = getTableReportType(this.context, topTabKey, tableIndicatorKey);
        // 是否展现用户设置的数据指标
        const showPartFields = reportArea.tabs
            && reportArea.tabs[topTabKey]
            && reportArea.tabs[topTabKey].tableAreaConfig
            && reportArea.tabs[topTabKey].tableAreaConfig.showPartFields || false;
        // 获取请求表头接口的入参
        const tableHeaderParams = getTableHeaderParams(this.context, reportType, showPartFields);
        const templateParams = {token, reportType};
        onChangeTableLoading(true);
        // 请求表头配置数据
        Promise.all([
            this.service.getTableMetaData({params: tableHeaderParams}),
            ...(withNewSortableSelector
                ? [this.service.getTemplates({params: templateParams})]
                : [new Promise(resolve => resolve({data: []}))]
            )
        ]).then(([metaData, templateData]) => {
            const {data: reportConfigData} = metaData;
            const {data: templateList} = templateData;
            fire('sdk.event.enter.log', {
                hitType: 'table-header-data-success'
            });
            let data = reportConfigData;
            if (handleCustomInfo) {
                data = handleCustomInfo(reportConfigData);
            }
            const initFilterData = getInitFilterData(data, this.context, this.props);
            const {initCustomerColumnData, initCustomerTemplateData} = getInitCustomerColumnData({
                metaData: data, templateList, withNewSortableSelector, context: this.context
            });
            this.setState({
                tableHeaderData: data,
                initFilterData,
                initCustomerColumnData,
                initCustomerTemplateData,
                filterData: isChangeCustomFields ? {
                    ...initFilterData,
                    ...this.state.filterData
                } : initFilterData,
                editingFilterData: isChangeCustomFields ? {
                    ...initFilterData,
                    ...this.state.editingFilterData
                } : initFilterData,
                sortRule,
                smartRowCount: topCount
            }, () => {
                trigger('sdk.event.table.inited');
                getMainTableComparable(data && data.comparable);
                this.tableContainerRef && this.tableContainerRef.updateColumnWidths();
            });
        }).catch(err => {
            getApiToast();
        }).finally(() => {
            onChangeTableLoading(false);
        });
    }
    getMarkFields = async () => {
        const {
            topTabKey,
            onChangeTableLoading,
            isMcc
        } = this.props;
        const {tableIndicatorKey} = this.state;
        const params = getCustomMarkFieldsParams(this.state, this.context, {topTabKey, tableIndicatorKey});
        let markFields = [];
        try {
            const {reportType, endDate, userIds = []} = params;
            const userId = userIds[0];
            const markFieldsInfoStr = localStorage.getItem(`custom-slector-mark-fields-${reportType}-${userId}`)
                || '{}';
            const {endDate: catcheEndDate, markFields: catcheMarkFields} = JSON.parse(markFieldsInfoStr);
            if (catcheEndDate === endDate && !isMcc) {
                markFields = catcheMarkFields;
            }
            else {
                onChangeTableLoading(true);
                const {
                    data: {summary} = {}
                } = await this.service.getTableData({params});
                markFields = Object.keys(summary).filter(item => {
                    const itemData = summary[item];
                    if (isNumber(itemData) && itemData > 0 && itemData < INFINITY) {
                        return true;
                    }
                    return false;
                });
                !isMcc && localStorage.setItem(`custom-slector-mark-fields-${reportType}-${userId}`, JSON.stringify({
                    endDate,
                    markFields
                }));
            }
        }
        catch (e) {}
        onChangeTableLoading(false);
        this.setState({markFields});
    }
    handleGetTableData = () => {
        const {eventHub: {fire, trigger}} = this.context;
        const {
            topTabKey,
            onChangeTableLoading,
            mainAreaConfig: {tableAreaConfig}
        } = this.props;
        onChangeTableLoading(true);
        const {tableIndicatorKey, splitColumn, additionFields, isFeedbackRequest} = this.state;
        this.setState({isTableLoading: true});
        const extraColumns = getTableExtraColumns(this.context, topTabKey, tableIndicatorKey) || [];
        const mergeExtraColumns = [...extraColumns, ...additionFields];
        // 获取请求表头接口的入参
        const tableDataParams = getTableDataParams(this.state, this.context, {
            topTabKey,
            tableIndicatorKey,
            extraColumns: mergeExtraColumns,
            tableAreaConfig
        });
        let requestServices = tableDataParams.compareStartDate ? 'getCompareTableData' : 'getTableData';
        if (tableDataParams && tableDataParams.splitColumn) {
            requestServices = 'getSplitTableData';
        }
        if (isFeedbackRequest === true) {
            trigger('sdk.event.table.feedbackRequest', {
                data: {
                    params: tableDataParams
                }
            });
            this.setState({isFeedbackRequest: false});
        }
        // 请求数据超时计时
        let time = 0;
        const timeInterval = setInterval(() => {
            time++;
        }, 1000);
        const reportType = tableDataParams.reportType;
        const servicePath = SERVICE_PATHS[requestServices];
        this.preServiceType = servicePath;
        this.preReportType = reportType;
        let fetchStatus = {
            status: 'success',
            columnCount: tableDataParams?.columns.length
        };
        fire('sdk.event.enter.log', {
            hitType: 'table-data-fetching',
            extra: {
                serviceType: servicePath,
                reportType
            }
        });
        // 请求表头数据
        this.service[requestServices]({
            params: tableDataParams
        }).then(({data, status, errors = []}) => {
            if (status === 0) {
                fire('sdk.event.enter.log', {
                    hitType: 'table-data-success',
                    extra: {
                        serviceType: servicePath,
                        reportType
                    }
                });
                fetchStatus.rowCount = data.rowCount;
                logDataFetched(fire, servicePath, reportType, fetchStatus);
                this.preFetchStatus = fetchStatus;
                trigger('sdk.event.main.table.changed', {
                    data: {
                        params: tableDataParams,
                        result: data
                    }
                });
                const summaryPosition = tableAreaConfig && tableAreaConfig.summaryPosition || 'after';
                const listData = splitColumn
                    ? formatSplitApiData(data, tableDataParams.columns, splitColumn, mergeExtraColumns, summaryPosition)
                    : formatApiData(data, mergeExtraColumns, summaryPosition);
                this.setState({
                    lastUpdateTime: dayjs().format('HH:mm:ss'),
                    isTableLoading: false,
                    totalCount: data.totalRowCount || 0,
                    listData,
                    selectedRowKeys: []
                });
                trigger('sdk.event.table.selected.rows', {
                    data: [],
                    tableReportType: tableAreaConfig && tableAreaConfig.tableReportType
                });
            }
            else if (status === 3) {
                getApiToast({content: '获取数据失败'});
                logDataFetchFail(fire, servicePath, reportType);
                fetchStatus.status = 'fail';
                fetchStatus.code = errors[0] && errors[0].code || status;
                logDataFetched(fire, servicePath, reportType, fetchStatus);
                this.preFetchStatus = fetchStatus;
            }
            else {
                fire('sdk.event.enter.log', {
                    hitType: 'table-data-too-big'
                });
                // 超时出发送邮件弹窗
                this.handleTimeOut();
                logDataFetchFail(fire, servicePath, reportType);
                fetchStatus.status = 'fail';
                fetchStatus.code = errors[0] && errors[0].code || status;
                logDataFetched(fire, servicePath, reportType, fetchStatus);
                this.preFetchStatus = fetchStatus;
            }
        }).catch(err => {
            // 海若超时时间是接近30s
            if (time > TIME_OUT_NUM) {
                fire('sdk.event.enter.log', {
                    hitType: 'table-data-too-big'
                });
                // 超时出发送邮件弹窗
                this.handleTimeOut();
            }
            else {
                getApiError(err);
            }
            logDataFetchFail(fire, servicePath, reportType);
            fetchStatus.status = 'fail';
            const errors = err.errors || [];
            fetchStatus.code = errors[0] && errors[0].code || err.status;
            logDataFetched(fire, servicePath, reportType, fetchStatus);
            this.preFetchStatus = fetchStatus;
        }).finally(data => {
            trigger('sdk.event.table.dataLoaded');
            this.setState({isTableLoading: false});
            clearInterval(timeInterval);
            onChangeTableLoading(false);
        });
    }

    handlePageSizeChange = e => {
        const {eventHub: {fire}} = this.context;
        const pageSize = e.target.value;
        fire('sdk.event.enter.log', {
            hitType: 'table-page-size-change',
            extra: {
                pageSize
            }
        });
        this.setState({
            pageSize,
            pageNo: 1
        }, () => {
            this.handleGetTableData();
        });
        const {
            mainAreaConfig: {tableAreaConfig}
        } = this.props;
        const {memoPageSize, tableReportType} = tableAreaConfig;
        if (memoPageSize) {
            const userId = this.context.hairuo.userId;
            setMemoPageSize(userId, tableReportType, pageSize);
        }
    }

    handlePageNoChange = e => {
        this.setState({pageNo: e.target.value}, () => {
            this.handleGetTableData();
        });
    }

    handleFilterChanging = ({data}) => {
        this.setState({editingFilterData: data});
    }

    handleFilterCancel = data => {
        // todo
        return data;
    }

    handleFilterLayer = ({data}) => {
        const {filterDropdownVisibleMap} = this.state;
        const {columnName, visible} = data;
        this.setState({
            filterDropdownVisibleMap: {
                ...filterDropdownVisibleMap,
                [columnName]: visible
            }
        });
    }

    handleSorter = ({data}) => {
        this.setState({sortRule: data}, () => {
            this.handleGetTableData();
        });
    }

    handleFilterClearAll = () => {
        const {initFilterData} = this.state;
        // 要真正清空，有默认值的默认值要清空:valuse为空数组
        const newInitialFilterData = Object.keys(initFilterData).reduce((memo, item) => {
            if (initFilterData
                && initFilterData[item]
                && initFilterData[item].values
                && initFilterData[item].values.length > 0
            ) {
                memo[item] = {
                    ...initFilterData[item],
                    values: []
                };
            }
            else {
                memo[item] = {
                    ...initFilterData[item]
                };
            }
            return memo;
        }, {});
        this.setState({
            filterData: newInitialFilterData,
            editingFilterData: newInitialFilterData,
            filterDropdownVisibleMap: {},
            pageNo: 1
        }, () => {
            this.handleGetTableData();
        });
    }

    handleFilterClear = ({data}) => {
        const {filterData, editingFilterData} = data;
        this.setState({
            filterData,
            editingFilterData,
            pageNo: 1
        }, () => {
            this.handleGetTableData();
        });
    }

    handleCustomFilterConfirm = ({data}) => {
        const {filterData, filterDropdownVisibleMap} = this.state;
        const {defaultFilterOperatorMap} = this.context.config.filter;
        const customFilterData = {};
        const customFilterDropdownVisibleMap = {};
        Object.keys(data).forEach(column => {
            customFilterData[column] = {
                operator: defaultFilterOperatorMap.CUSTOM,
                values: data[column].map(col => col.value),
                labels: data[column].map(col => col.label)
            };
            customFilterDropdownVisibleMap[column] = false;
        });
        this.setState({
            filterData: {
                ...filterData,
                ...customFilterData
            },
            filterDropdownVisibleMap: {
                ...filterDropdownVisibleMap,
                ...customFilterDropdownVisibleMap
            }
        }, () => {
            this.handleGetTableData();
        });
    }

    handleCustomFilterAddFilters = ({data}) => {
        const {filterData, filterDropdownVisibleMap} = this.state;
        const customFilterData = {};
        const customFilterDropdownVisibleMap = {};
        Object.keys(data).forEach(column => {
            customFilterData[column] = data[column] && data[column].pageFilters;
            customFilterDropdownVisibleMap[column] = false;
        });
        this.setState({
            filterData: {
                ...filterData,
                ...customFilterData
            },
            filterDropdownVisibleMap: {
                ...filterDropdownVisibleMap,
                ...customFilterDropdownVisibleMap
            }
        }, () => {
            this.handleGetTableData();
        });
    }

    handleCustomFilterCancel = ({data}) => {
        const {filterDropdownVisibleMap} = this.state;
        const customFilterDropdownVisibleMap = {};
        Object.keys(data).forEach(column => {
            customFilterDropdownVisibleMap[column] = false;
        });
        this.setState({
            filterDropdownVisibleMap: {
                ...filterDropdownVisibleMap,
                ...customFilterDropdownVisibleMap
            }
        });
    }

    handleFilterFromWhere = ({data}) => {
        this.setState({filterFromWhere: data});
    }

    handleCustomColumnSelect = (fields, {version = 'v1'} = {}) => {
        const {
            topTabKey
        } = this.props;
        const {tableIndicatorKey} = this.state;
        const reportType = getTableReportType(this.context, topTabKey, tableIndicatorKey);
        this.context.eventHub.fire('sdk.event.enter.log', {
            hitType: 'custom-column-select-click'
        });
        this.setState({isTableLoading: true});
        this.service.saveCustomColumns({
            params: {
                columns: fields,
                reportType,
                withNewCategory: this.context?.config?.withNewCategory
            }
        }).then(({status}) => {
            if (status === 0) {
                const {eventHub: {fire, trigger}} = this.context;
                fire('sdk.event.enter.log', {
                    hitType: 'save-custom-columns-success'
                });
                this.handleGetTableMetaData({isChangeCustomFields: true});
                trigger('sdk.event.save.custom.columns', {
                    data: {
                        fields
                    },
                    version
                });
            }
            else {
                this.setState({isTableLoading: false});
                getApiToast({content: '修改自定义列失败'});
            }
        }).catch(err => {
            this.setState({isTableLoading: false});
            getApiToast();
        });
    }

    // 保存为常用自定义列模版
    handleSaveTemplate = (template, newCommonTemplates, isNewTemplate) => {
        const {topTabKey} = this.props;
        const {tableIndicatorKey} = this.state;
        const {token} = this.context;
        const reportType = getTableReportType(this.context, topTabKey, tableIndicatorKey);
        const {key, label, selectedFields} = template;
        const saveTemplateParams = {
            ...(!isNewTemplate ? {templateId: +key} : {}),
            templateName: label,
            token,
            reportType,
            columns: selectedFields,
            markAsCurrentTemplate: true
        };
        return this.service.saveTemplate({params: saveTemplateParams}).then(res => {
            const {eventHub: {fire, trigger}} = this.context;
            fire('sdk.event.enter.log', {hitType: 'save-template-success'});
            this.handleGetTableMetaData({isChangeCustomFields: true});
            trigger('sdk.event.save.custom.columns', {data: {fields: selectedFields}, version: 'v2'});
        }).catch(err => {
            getApiError(err, {useToast: true});
            return false;
        });
    }

    // 新版自定义列组件弹窗点击确定回调
    handleCustomColumnV2Select = template => {
        const {key, selectedFields} = template;
        if (key === templateKey.custom) { // 打开弹窗，没有保存为模版，会变成当前自定义
            this.handleCustomColumnSelect(selectedFields, {version: 'v2'}); // 同之前保存自定义列逻辑
        }
    }

    cancelSelectCustomFields = () => {
        const {eventHub: {trigger}} = this.context;
        trigger('sdk.event.cancel.custom.columns', {version: 'v2'});
    }

    // 切换常用自定义列模版回调
    handleSelectTemplate = template => {
        const {key, selectedFields} = template;
        const {topTabKey} = this.props;
        const {tableIndicatorKey} = this.state;
        const {token} = this.context;
        const reportType = getTableReportType(this.context, topTabKey, tableIndicatorKey);
        const templateId = key === templateKey.default ? templateValue.default : +key;
        const params = {templateId, token, reportType};
        this.service.markAsCurrentTemplate({params}).then(res => {
            const {eventHub: {fire, trigger}} = this.context;
            fire('sdk.event.enter.log', {hitType: 'select-template-success'});
            this.handleGetTableMetaData({isChangeCustomFields: true});
            trigger('sdk.event.save.custom.columns', {data: {fields: selectedFields}, version: 'v2'});
        }).catch(err => {
            getApiToast();
        });
    }

    // 删除常用自定义列模版
    handleDeleteTemplate = (template, newTemplates) => {
        const {initCustomerTemplateData} = this.state;
        const {currentTemplate} = initCustomerTemplateData;
        const {key} = template;
        return this.service.deleteTemplate({
            params: {templateIds: [+key]}
        }).then(res => {
            if (currentTemplate === key) { // 删除的模版为当前正在使用的模版
                this.handleGetTableMetaData({isChangeCustomFields: true});
            }
            else {
                this.setState({
                    initCustomerTemplateData: {
                        ...this.state.initCustomerTemplateData,
                        templateList: newTemplates
                    }
                });
            }
        }).catch(err => {
            getApiToast({content: '删除模版失败'});
            return false;
        });
    }

    // 获取删除模版时提示文案
    getTemplateDeleteMsg = template => `删除指标模板【${template.label}】会使得所有数据报告中都同步删除该指标模板`;

    // 模版名称校验
    validateTemplateName = name => {
        if (!name) {
            return '自定义列名称不能为空';
        }
        if (name.length > templateNameLimit) {
            return `自定义列名称不能超过${templateNameLimit}个字符`;
        }
        return '';
    }

    handleSearch = ({data}) => {
        const {tableHeaderData, filterData, editingFilterData} = this.state;
        const {smartFilterable, smartFilterColumns} = tableHeaderData;
        const {content, type} = data;
        let newFilterData = filterData;
        let newEditingFilterData = editingFilterData;
        if (smartFilterable && smartFilterColumns && smartFilterColumns.length) {
            newFilterData = {
                ...filterData,
                [smartFilterColumns[0]]: {
                    operator: type,
                    values: content ? [content] : []
                }
            };
            newEditingFilterData = {
                ...editingFilterData,
                [smartFilterColumns[0]]: {
                    operator: type,
                    values: content ? [content] : []
                }
            };
        }
        this.setState({filterData: newFilterData, editingFilterData: newEditingFilterData, pageNo: 1}, () => {
            this.handleGetTableData();
        });
    }

    handleTimeOut = () => {
        const {eventHub: {trigger}} = this.context;
        trigger('sdk.event.table.email.open', {
            timeOut: true
        });
    }

    handleClearSyncDownload = () => {
        const {
            mainAreaConfig: {tableAreaConfig}
        } = this.props;
        const {downloading} = this.state;
        const {openSyncDownload} = tableAreaConfig;
        if (openSyncDownload && downloading) {
            clearInterval(this.downloadTaskTimer);
            this.loadingHide && this.loadingHide();
            this.setState({downloading: false});
            const {eventHub: {fire}} = this.context;
            fire('sdk.event.enter.log', {
                hitType: 'table-data-async-download-clear',
                extra: {
                    clearAt: ((this.tick || 0) + 1) * tickTime
                }
            });
        }
    }

    handleGetTaskStatus = async ({taskId, res, rej, cachePromiseStatus}) => {
        try {
            const {eventHub: {fire}} = this.context;
            const {data: {fileUrl, taskStatus}} = await this.service.getTaskStatus({
                params: {
                    taskId
                }
            });
            if (taskStatus === 'SUCCESS' && fileUrl) {
                res(fileUrl);
                clearInterval(this.downloadTaskTimer);
            }
            else if (taskStatus === 'FAIL') {
                fire('sdk.event.enter.log', {
                    hitType: 'table-data-async-download-backend-fail'
                });
                rej();
                clearInterval(this.downloadTaskTimer);
            }
            cachePromiseStatus.status = 'finished';
        }
        catch (err) {
            cachePromiseStatus.status = 'finished';
        }
    }

    handleQueryTaskStatus = taskId => new Promise(async (res, rej) => {
        const {eventHub: {fire}} = this.context;

        this.tick = 0;
        let cachePromiseStatus = {status: ''};
        this.downloadTaskTimer = setInterval(() => {
            if (this.tick <= maxTick && this.state.downloading) {
                this.tick++;
                if (cachePromiseStatus.status === 'finished') {
                    this.handleGetTaskStatus({taskId, res, rej, cachePromiseStatus});
                }
            }
            else {
                rej();
                clearInterval(this.downloadTaskTimer);
                fire('sdk.event.enter.log', {
                    hitType: 'table-data-async-download-timeout'
                });
            }
        }, tickTime);
        this.handleGetTaskStatus({taskId, res, rej, cachePromiseStatus});
    })

    handleSyncDownload = async downloadParams => {
        const {eventHub: {fire}} = this.context;
        this.loadingHide = Toast.loading({
            content: '数据加载中，请耐心等待',
            duration: maxTick * tickTime,
            onClose: () => {
                this.state.downloading && this.handleClearSyncDownload();
            }
        });
        try {
            const {data: {taskId}} = await this.service.createTask({params: downloadParams});
            const fileUrl = await this.handleQueryTaskStatus(taskId);
            const downloading = this.state.downloading;
            if (downloading) {
                window.location.href = fileUrl;
                fire('sdk.event.enter.log', {
                    hitType: 'table-data-async-download-success'
                });
                this.setState({downloading: false});
                this.loadingHide();
                Toast.success({content: '加载完成', showCloseIcon: false});
            }
            else {
                this.setState({downloading: false});
                this.loadingHide();
                Toast.info({content: '加载停止'});
            }
        }
        catch (err) {
            const downloading = this.state.downloading;
            if (downloading) {
                fire('sdk.event.enter.log', {
                    hitType: 'table-data-async-download-fail'
                });
                this.setState({downloading: false});
                this.loadingHide();
                Toast.error({content: '加载失败', showCloseIcon: false});
            }
            else {
                this.setState({downloading: false});
                this.loadingHide();
                Toast.info({content: '加载停止'});
            }
        }
    }

    handleDownload = () => {
        const {eventHub: {fire}} = this.context;
        const {
            topTabKey,
            mainAreaConfig: {tableAreaConfig}
        } = this.props;
        const {downloadInfo = {}, isNeedDownloadSetting, staticPageSize, openSyncDownload} = tableAreaConfig;
        const {addZeroRows} = downloadInfo;
        const {tableIndicatorKey, additionFields, downloadSettings} = this.state;
        const columns = getTableFieldsMap(this) || []; // 使用table处理列的方法拿到table的真实列配置
        const columnsFields = [];
        columns.forEach(config => {
            const {dataIndex, children} = config;
            if (dataIndex) {
                columnsFields.push(dataIndex);
            }
            else if (children) {
                children.forEach(config => {
                    if (config.dataIndex) {
                        columnsFields.push(config.dataIndex);
                    }
                });
            }
        });
        const tableDataParams = getTableDataParams(this.state, this.context, {
            topTabKey,
            tableIndicatorKey,
            extraColumns: additionFields,
            tableAreaConfig,
            columnsFields
        });
        fire('sdk.event.enter.log', {
            hitType: 'table-data-download-click'
        });
        // 下载超时计时
        let time = 0;
        const timeInterval = setInterval(() => {
            time++;
        }, 1000);
        this.setState({downloading: true});
        const downloadParams = {
            ...tableDataParams,
            startRow: 0,
            rowCount: 100000
        };
        if (isNumber(staticPageSize)) {
            downloadParams.rowCount = staticPageSize;
        }
        if (addZeroRows) {
            downloadParams.addZeroRows = !!addZeroRows;
        }
        if (isNeedDownloadSetting && !downloadSettings.includeDeletedMaterial) {
            downloadParams.filters.push({
                column: 'isDel',
                operator: 'IN',
                values: [0]
            });
        }
        if (openSyncDownload) {
            this.handleSyncDownload(downloadParams);
            clearInterval(timeInterval);
            return;
        }
        Toast.info({content: '数据加载中，请耐心等待'});
        this.service.downloadTableData({
            params: downloadParams
        }).then(res => {
            if (res.status === 0) {
                fire('sdk.event.enter.log', {
                    hitType: 'table-data-download-success'
                });
                window.location.href = res.data.fileUrl;
            }
            else if (res.status === 0) {
                getApiToast({content: '表格下载内容失败'});
            }
            else {
                fire('sdk.event.enter.log', {
                    hitType: 'download-data-too-big'
                });
                // 超时出发送邮件弹窗
                this.handleTimeOut();
            }
        }).catch(err => {
            // 海若超时时间是接近30s
            if (time > TIME_OUT_NUM) {
                fire('sdk.event.enter.log', {
                    hitType: 'download-data-too-big'
                });
                // 超时出发送邮件弹窗
                this.handleTimeOut();
            }
            else {
                getApiToast({content: '请求接口出错'});
            }
        }).finally(() => {
            this.setState({downloading: false});
            clearInterval(timeInterval);
        });
    }

    handleEmail = ({mails, sixMonths, emailSelectOption}) => {
        const {eventHub: {fire, trigger}} = this.context;
        const {
            topTabKey,
            mainAreaConfig: {tableAreaConfig}
        } = this.props;
        const {tableIndicatorKey, additionFields, downloadSettings} = this.state;
        const tableDataParams = getTableDataParams(this.state, this.context, {
            topTabKey,
            tableIndicatorKey,
            extraColumns: additionFields,
            tableAreaConfig
        });
        const params = {
            ...tableDataParams,
            startRow: 0,
            rowCount: 100000,
            mails,
            ...emailSelectOption?.params ?? {}
        };
        // 是否是下载最近6个月，从前一天开始计算
        if (sixMonths) {
            const now = new Date(getTargetEndDate(new Date(), -1));
            params.endDate = getTargetDateOfMs(now, '-');
            params.startDate = getTargetDateOfMs(now.setMonth(now.getMonth() - 6), '-');
        }
        if (tableAreaConfig.isNeedDownloadSetting && !downloadSettings.includeDeletedMaterial) {
            params.filters.push({
                column: 'isDel',
                operator: 'IN',
                values: [0]
            });
        }

        this.service.emailTableData({
            params
        }).then(res => {
            if (res.status === 0) {
                fire('sdk.event.enter.log', {
                    hitType: 'table-data-send-email-success'
                });
                // 传出发送成功事件
                trigger('sdk.event.table.email.send');
            }
            else {
                getApiToast({content: '发送邮箱失败'});
            }
        }).catch(err => {
            getApiToast({content: '请求接口出错'});
        });
    }

    modDownloadSettings = includeDeletedMaterial => {
        this.service.modPersonalDownloadSetup({
            params: {
                includeDeletedMaterial,
                mails: this.state.downloadSettings.mails
            }
        }).then(res => {
            this.setState({
                downloadSettings: {
                    includeDeletedMaterial
                }
            });
        });
    }

    handleToggleTable = () => {
        const {chartAreaVisible} = this.state;
        const {onToggleChartArea} = this.props;
        onToggleChartArea(!chartAreaVisible);
        this.setState({chartAreaVisible: !chartAreaVisible});
    }

    handleTableIndicatorChange = ({data}) => {
        const {
            targetKey,
            additionFields,
            tableIndicatorKeyList,
            indicatorExternalFilters,
            indicatorTableSupportCommonFilter
        } = data;
        this.setState({
            tableIndicatorKey: targetKey,
            additionFields,
            tableIndicatorKeyList,
            splitColumn: '',
            pageNo: 1,
            sortRule: {},
            indicatorExternalFilters,
            indicatorTableSupportCommonFilter
        }, () => {
            this.handleGetTableMetaData();
        });
    }

    handleSplitChange = ({data}) => {
        this.setState({
            splitColumn: data
        }, () => {
            this.tableContainerRef && this.tableContainerRef.updateColumnWidths();
            this.handleGetTableData();
        });
    }

    getTableRef = ref => {
        this.tableContainerRef = ref;
    }

    onSelectTableChange = selectedRowKeys => {
        const {eventHub: {trigger}} = this.context;
        const {mainAreaConfig: {tableAreaConfig}} = this.props;
        const {tableReportType} = tableAreaConfig || {};
        const {tableIndicatorKeyList} = this.state;
        const {rowKey} = getRowSelectionOption({
            tableAreaConfig,
            tableIndicatorKeyList
        });
        const {listData} = this.state;
        this.setState({
            selectedRowKeys
            // 总计行要除外
            // isCheckAll: (selectedRowKeys && selectedRowKeys.length)
            //     === (listData.length && listData.length > 0 && listData.length - 1)
        });
        const targetSelectedTableRows = listData.filter(row => {
            const rKey = typeof rowKey === 'function'
                ? rowKey(row)
                : (Array.isArray(row[rowKey]) ? row[rowKey] && row[rowKey][0] : row[rowKey]) || '';
            return selectedRowKeys.indexOf(String(rKey)) > -1;
        });
        trigger('sdk.event.table.selected.rows', {
            data: targetSelectedTableRows,
            tableReportType
        });
    }
    onChangeTableVisible = value => {
        this.setState({
            tableVisible: value
        });
    }

    clearTop = () => {
        this.setState({
            smartRowCount: null
        }, () => {
            this.handleGetTableData();
        });
    }

    render() {
        const {
            pagination,
            classPrefix,
            reportArea,
            datePlacement,
            isNeedCompare,
            withNewCategory
        } = this.context.config;
        const {
            lastUpdateTime,
            listData,
            totalCount,
            isTableLoading,
            initFilterData,
            filterData,
            editingFilterData,
            filterDropdownVisibleMap,
            tableHeaderData,
            filterFromWhere,
            initCustomerColumnData,
            initCustomerTemplateData,
            commonFilterInfo,
            pageSize,
            pageNo,
            tableIndicatorKeyList,
            selectedRowKeys,
            markFields,
            downloading,
            tableVisible,
            smartRowCount
        } = this.state;
        const {
            mainAreaConfig: {tableAreaConfig},
            chartAreaVisible,
            isMcc,
            topTabKey,
            mccStatus,
            initDateAndCompare,
            mainTableComparable
        } = this.props;
        const {
            tableIndicatorList,
            tableIndicatorTitle,
            defaultIndicator,
            isUseDownload,
            isUseCustomerField,
            withNewSortableSelector = false,
            indicatorUiType = 'dropDown',
            isDraggable = true,
            headerFixTop = 0,
            bottomScroll = defaultBottomScroll,
            hiddenPagination = false,
            isUseTableAction,
            memoPageSize,
            tableReportType,
            hideToggleChart = false,
            checkIsShowTableAreaTip = noop,
            tableAreaTip = '',
            $props = {},
            defaultCloseTable = false
        } = tableAreaConfig || {};
        const {Table: $tableProps = {}} = $props;
        const {currentTemplate, templateList} = initCustomerTemplateData;

        const {needRowSelection, mccRowSelection, rowKey, BatchBarRender}
            = getRowSelectionOption({
                tableAreaConfig,
                tableIndicatorKeyList
            });
        const {smartFilterable, smartFilterColumns, columnConfigs, splitColumns, splitable} = tableHeaderData;
        const isHasChartArea = reportArea.tabs
            && reportArea.tabs[topTabKey]
            && reportArea.tabs[topTabKey].chartAreaList
            && reportArea.tabs[topTabKey].chartAreaList.length > 0;
        // 是否展现用户设置的数据指标
        const showPartFields = reportArea.tabs
            && reportArea.tabs[topTabKey]
            && reportArea.tabs[topTabKey].tableAreaConfig
            && reportArea.tabs[topTabKey].tableAreaConfig.showPartFields || false;
        // 是否使用标记
        const isUseMarkFields = reportArea?.tabs[topTabKey]?.tableAreaConfig?.isUseMarkFields !== undefined
            ? reportArea.tabs[topTabKey].tableAreaConfig.isUseMarkFields
            : true;
        // 是否展示刷新数据按钮
        const showFreshData = reportArea.tabs
            && reportArea.tabs[topTabKey]
            && reportArea.tabs[topTabKey].tableAreaConfig
            && reportArea.tabs[topTabKey].tableAreaConfig.showFreshData || false;
        const columns = getTableFieldsMap(this) || [];
        const isDoubleHeader = columns.filter(column => column.children).length;
        const isShowTableAreaTip = checkIsShowTableAreaTip({tableReportType, data: listData});
        const tableProps = {
            loading: isTableLoading,
            columns,
            dataSource: listData,
            pagination: false,
            className: `${classPrefix}-main-table`,
            size: 'small',
            scroll: defaultScroll,
            ref: this.getTableRef,
            headBordered: isDoubleHeader,
            bordered: false,
            updateWidthChange: true,
            useStickyFixTop: true,
            trackBodyUpdate: this.handleDidPaint,
            onDragEnd: this.onTableDragEnd
        };
        if (headerFixTop !== false) {
            tableProps.headerFixTop = headerFixTop;
        }
        if (bottomScroll !== false) {
            tableProps.bottomScroll = bottomScroll;
        }
        if (needRowSelection) {
            if (mccRowSelection || (!mccRowSelection && !isMcc)) {
                tableProps.rowKey = typeof rowKey === 'function'
                    ? rowKey
                    : row => {
                        return `${Array.isArray(row[rowKey]) ? row[rowKey] && row[rowKey][0] : row[rowKey]}` || 'sumId';
                    };
                tableProps.rowSelection = {
                    fixed: true,
                    selectedRowKeys,
                    onChange: this.onSelectTableChange,
                    getCheckboxProps: row => {
                        return {
                            disabled: row.isSummaryRow
                        };
                    }
                };
            }
        }
        const userId = this.context.hairuo.userId;
        const paginationProps = {
            onPageSizeChange: this.handlePageSizeChange,
            onPageNoChange: this.handlePageNoChange,
            pageSize,
            pageNo,
            total: totalCount,
            defaultPageNo: memoPageSize && getMemoPageSize(userId, tableReportType) || pagination.pageSize,
            pageSizeOptions: pagination.pageSizes
        };
        const filterListProps = {
            tableHeaderData,
            initFilterData,
            filterData,
            editingFilterData,
            filterDropdownVisibleMap,
            filterFromWhere,
            className: needRowSelection && BatchBarRender && selectedRowKeys && selectedRowKeys.length > 0
                ? 'filter-list-space-cls'
                : ''
        };
        const donwloadEmailProps = {
            reportType: this.preReportType,
            disableDownload: downloading,
            handleDownload: this.handleDownload,
            handleEmail: this.handleEmail,
            modDownloadSettings: this.modDownloadSettings,
            isNeedDownloadSetting: this.props.mainAreaConfig.tableAreaConfig.isNeedDownloadSetting,
            isNeedFilterEmailDownload: this.props.mainAreaConfig.tableAreaConfig.isNeedFilterEmailDownload,
            emailFilterOptions: this.props.mainAreaConfig.tableAreaConfig.emailFilterOptions,
            downloadSettings: this.state.downloadSettings
        };
        let searchName = '';
        let searchMetrics = '';
        if (smartFilterable && smartFilterColumns && smartFilterColumns.length) {
            const targetColumn = columnConfigs[smartFilterColumns[0]] || {};
            searchName = targetColumn && targetColumn.columnText;
            searchMetrics = smartFilterColumns[0];
        }
        const tableSearchProps = {
            searchName,
            searchMetrics
        };
        const commonCutomColumnProps = {
            useGroupSelectAll: true,
            buttonTitle: '自定义列',
            isDispalyGuide: true,
            isUseSearch: true,
            markFieldsTip: withNewSortableSelector ? newMarkFieldsTip : markFieldsTip,
            markFields,
            isUseMarkFields,
            onOpenCustomPanel: () => {
                this.context.eventHub.fire('sdk.event.enter.log', {
                    hitType: 'custom-column-open-click'
                });
                this.getMarkFields();
            }
        };
        const customColumnProps = {
            ...defaultCustomColumnProps,
            ...initCustomerColumnData,
            ...commonCutomColumnProps,
            onSelect: this.handleCustomColumnSelect,
            ...(withNewCategory ? {groupSidenav: true} : {}),
            ...(showPartFields && !withNewCategory ? {
                defaultFieldTag: 'partFields',
                fieldTags: [
                    {
                        key: 'fields',
                        label: '全部指标',
                        sidenav: true
                    },
                    {
                        key: 'partFields',
                        label: '设置的转化指标',
                        sidenav: false
                    }
                ],
                partialFieldsTip: '仅显示已设置的转化指标'
            } : {})
        };
        const customColumnV2Props = {
            ...defaultCustomColumnV2Props,
            ...initCustomerColumnData,
            ...commonCutomColumnProps,
            onSelect: this.handleCustomColumnV2Select,
            commonTemplates: templateList,
            currentTemplateKey: currentTemplate,
            maxTemplateCount: templateCountLimit,
            onTemplateSave: this.handleSaveTemplate,
            onTemplateSelect: this.handleSelectTemplate,
            onTemplateDelete: this.handleDeleteTemplate,
            templateDeleteMsg: this.getTemplateDeleteMsg,
            templateNameValidator: this.validateTemplateName,
            onCancel: this.cancelSelectCustomFields
        };
        const headerPanelClass = `${classPrefix}-table-header-panel`;

        const toggleTableBtnProps = {
            type: 'basic',
            size: 'small',
            onClick: this.handleToggleTable
        };
        const draggableInitProps = {
            type: 'basic',
            size: 'small',
            onClick: this.onResetColumnsWidth
        };

        let tableInfo = tableAreaConfig && tableAreaConfig.infoList || '';
        if (tableInfo && typeof tableInfo === 'function') {
            tableInfo = tableInfo({
                indicatorKeyList: tableIndicatorKeyList
            });
        }

        // 根据指标判断是否展示自定义列
        let customColShow = tableAreaConfig && tableAreaConfig.customColShow || '';
        if (customColShow && typeof customColShow === 'function') {
            customColShow = customColShow({
                indicatorKeyList: tableIndicatorKeyList
            });
        }

        // 自定义列是否需要的条件
        const showSortableSelector = isUseCustomerField !== false || customColShow;

        const toggleTableBtnText = chartAreaVisible ? '收起' : '展开';
        const splitProps = {
            columnConfigs,
            splitColumns,
            disabled: !!(commonFilterInfo && commonFilterInfo.compareEndDate),
            className: `${classPrefix}-table-split-container`,
            popupClassName: `${classPrefix}-table-split-popup-container`
        };
        const isRenderSmartSearch = !defaultCloseTable
            && smartFilterable
            && smartFilterColumns
            && smartFilterColumns.length > 0;
        const isRenderIndicatorList = tableIndicatorList
            && tableIndicatorList.length > 0
            && indicatorUiType === 'dropDown';
        const isRenderTitle = defaultCloseTable || !(isRenderSmartSearch || isRenderIndicatorList);
        const freshDatabtnProps = {
            size: 'small',
            onClick: this.handleGetTableData,
            loading: isTableLoading,
            icon: isTableLoading ? '' : IconSync
        };
        const showTableBtnOpenProps = {
            className: 'show-detail-btn',
            onClick: () => this.onChangeTableVisible(true)
        };
        if (defaultCloseTable && !tableVisible) {
            return <span {...showTableBtnOpenProps}>展开明细数据</span>;
        }
        const showTableBtnCloseProps = {
            className: 'show-detail-btn',
            onClick: () => this.onChangeTableVisible(false)
        };
        return (
            <div className={`${classPrefix}-table`}>
                {needRowSelection && BatchBarRender && <BatchBarRender />}
                {isShowTableAreaTip && tableAreaTip
                    && <Alert
                        className={`${classPrefix}-table-info-alert`}
                        content={tableAreaTip}
                        type="info"
                        showIcon
                    />}
                {isUseTableAction !== false && <div className={headerPanelClass}>
                    <div className={`${headerPanelClass}-field ${headerPanelClass}-field-left`}>
                        {
                            isRenderTitle && (
                                defaultCloseTable ? (
                                    <div {...showTableBtnCloseProps}>收起明细数据</div>
                                ) : (<div className="table-title">详细数据</div>)
                            )
                        }
                        {isRenderSmartSearch && <TableSearch {...tableSearchProps} />}
                        {isRenderIndicatorList && <Indicator
                            defaultIndicator={defaultIndicator}
                            tableIndicatorList={tableIndicatorList}
                            indicatorUiType={indicatorUiType}
                            tableIndicatorTitle={tableIndicatorTitle}
                        />}
                    </div>
                    <div className={`${headerPanelClass}-field ${headerPanelClass}-field-right`}>
                        {datePlacement === datePlacementConfig.withTable
                            && (
                                <SdkDatePicker
                                    isNeedCompare={isNeedCompare && mainTableComparable}
                                    initDateAndCompare={initDateAndCompare}
                                />
                            )
                        }
                        {
                            splitable && splitColumns && splitColumns.length
                                ? <Split {...splitProps} />
                                : null
                        }
                        {
                            showSortableSelector && !withNewSortableSelector
                            && <SortableSelector {...customColumnProps} />
                        }
                        {
                            showSortableSelector && withNewSortableSelector
                            && <div className='sortable-selector-wrap'>
                                <SortableSelectorV2 {...customColumnV2Props} />
                            </div>
                        }
                        {
                            isUseDownload !== false && <DownloadEmail {...donwloadEmailProps} />
                        }
                        {isDraggable ? <Button {...draggableInitProps}>重置列宽</Button> : null}
                        {
                            isHasChartArea && !mccStatus && !hideToggleChart
                                ? <Button {...toggleTableBtnProps}>{toggleTableBtnText}图表</Button>
                                : null
                        }
                        {
                            showFreshData ? (
                                <span>
                                    <span className="update-time-content">更新时间：{lastUpdateTime}</span>
                                    <Button {...freshDatabtnProps}>实时数据</Button>
                                </span>
                            ) : null
                        }
                    </div>
                </div>}
                <div className={`${classPrefix}-table-top-wrap`}>
                    {
                        !!(+smartRowCount > 0) && (
                            <div className={`${classPrefix}-table-top`}>
                                TOP：{smartRowCount}
                                <IconTimes className="close-icon" onClick={this.clearTop} />
                            </div>
                        )
                    }
                    <FilterList {...filterListProps} />
                </div>
                {!!columns.length && (
                    <div>
                        <Table {...tableProps} {...$tableProps} />
                        {!hiddenPagination && <div className={`${classPrefix}-table-pagination`}>
                            <Pagination {...paginationProps} />
                        </div>}
                    </div>
                )}
                {tableInfo && <div className={`${classPrefix}-table-info-list`}>
                    {tableInfo}
                </div>}
            </div>
        );
    }
}
