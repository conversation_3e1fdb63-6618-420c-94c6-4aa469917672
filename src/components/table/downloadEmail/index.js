/**
 * @file 报表下载-发送邮箱
 * <AUTHOR>
 */
import React, {PureComponent} from 'react';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import {isEmail} from '../../../utils/common';
import {
    Form,
    Dropdown,
    Dialog,
    Button,
    Tag,
    Popover,
    Switch,
    Radio
} from '@baidu/one-ui';
import {isArray, cloneDeep} from 'lodash';
import {IconQuestionCircle} from 'dls-icons-react';

const OPTIONS = [
    {
        label: '下载',
        value: 'download'
    },
    {
        label: '下载近6个月',
        value: 'sixMonths',
        judge: true
    },
    {
        label: '发送到邮箱',
        value: 'email'
    }
];
export default class DownloadEmail extends PureComponent {

    static propTypes = {
        handleEmail: PropTypes.func.isRequired,
        handleDownload: PropTypes.func.isRequired
    };

    static contextTypes = {
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }

    constructor(props, context) {
        super(props, context);
        const {config} = context;
        const {maxEmails = 5, download = []} = config;
        const options = cloneDeep(OPTIONS).filter(v => !(v.judge && !download.includes(v.value)));
        if (props.isNeedDownloadSetting) {
            options.push({
                label: '下载设置',
                value: 'downloadSettings'
            });
        }
        this.state = {
            isNeedFilterEmailDownload: props.isNeedFilterEmailDownload,
            emailSelectOption: props.emailFilterOptions?.[0],
            visible: false,
            regular: false,
            options,
            emails: [],
            maxEmails,
            inputEmails: '',
            emailError: '',
            originEmails: [],
            emailFilterOptions: props.emailFilterOptions,
            includeDeletedMaterial: props.downloadSettings?.includeDeletedMaterial,
            settingsVisible: false
        };
    }

    componentDidMount() {
        const {eventHub: {on}} = this.context;
        on('sdk.event.table.email', this.onEmailTrigger);
        on('sdk.event.table.email.open', this.onEmailShow);
    }

    onEmailTrigger = ({data}) => {
        if (isArray(data)) {
            this.setState({
                emails: data,
                originEmails: cloneDeep(data)
            });
        } else {
            this.setState({
                emails: data ? [data] : [],
                originEmails: data ? [cloneDeep(data)] : []
            });
        }
    }

    componentWillUnmount() {
        const {eventHub: {un}} = this.context;
        un('sdk.event.table.email', this.onEmailTrigger);
        un('sdk.event.table.email.open', this.onEmailShow);

    }

    onDownload = () => {
        this.props.handleDownload();
    };

    onEmailShow = params => {
        const {
            timeOut = false,
            sixMonths = false
        } = params || {};
        this.setState({
            visible: true,
            sixMonths,
            timeOut
        });
    }

    onSend = () => {
        if (this.inputCheck()) {
            this.props.handleEmail({
                mails: this.state.emails,
                sixMonths: this.state.sixMonths,
                emailSelectOption: this.state.emailSelectOption
            });
            this.setState({
                emails: cloneDeep(this.state.originEmails),
                emailSelectOption: this.state.emailFilterOptions?.[0]
            });
            this.onCancel();
        }

    }

    onBtnClick = event => {
        this.setState({
            emailSelectOption: this.state.emailFilterOptions.find(option => option.value === event.target.value)
        });
    }

    onCancel = () => {
        this.setState({
            visible: false
        });
    }

    onChageIncludeDelMaterial= includeDeletedMaterial => {
        this.setState({
            includeDeletedMaterial
        });
    }
    onCancelSettings = () => {
        this.setState({
            settingsVisible: false
        });
    }
    onSaveSettings = async () => {
        await this.props.modDownloadSettings(this.state.includeDeletedMaterial);
        this.onCancelSettings();
    }

    onDropdownClick = event => {

        switch (event.key) {
            case 'download':
                // 下载
                this.onDownload();
                break;
            case 'email':
                // 发送邮件
                this.onEmailShow();
                break;
            case 'sixMonths':
                this.onEmailShow({
                    sixMonths: true
                });
                break;
            case 'downloadSettings':
                this.setState({
                    settingsVisible: true
                });
                break;
        }
    }

    onClose = value => {
        const temp = this.state.emails.filter(v => v !== value);
        this.setState({
            emails: temp
        });
        // 总体输入校验
        this.inputCheck(temp);
    }

    onInputChange = event => {
        this.setState({
            inputEmails: event.target.value
        });
    }

    inputCheck(inputEmails) {
        let emails;
        if (inputEmails) {
            emails = inputEmails;
        }
        else {
            emails = this.state.emails;
        }
        if (!emails.length) {
            this.setState({
                emailError: '请至少输入一个邮箱，按回车确认'
            });
            return false;
        }
        const invalid = emails.some(v => !isEmail(v));
        if (invalid) {
            this.setState({
                emailError: '存在非法邮箱'
            });
            return false;
        }
        this.setState({
            emailError: ''
        });
        return true;


    }

    keyDown = event => {
        if (event.keyCode === 13) {
            const {emails = [], inputEmails, maxEmails = 5} = this.state;
            const emailArr = inputEmails.trim().split(';');
            emailArr.forEach(v => {
                if (v && !emails.includes(v) && emails.length < maxEmails) {
                    emails.push(v);
                }
            });

            this.setState({
                emails: emails,
                inputEmails: '',
                emailError: ''
            });
        }
    }

    render() {
        const {
            emails = [],
            emailError,
            inputEmails,
            visible,
            options,
            maxEmails,
            timeOut,
            emailFilterOptions
        } = this.state;

        const inputClass = classNames({
            'error-input': emailError,
            'report-sdk-email-content': true
        });
        const footer = [
            <Button type="primary" key="1" disabled={!emails.length} onClick={this.onSend}>
                立即发送
            </Button>,
            <Button type="normal" key="3" onClick={this.onCancel}>
                取消
            </Button>
        ];
        return (
            <div className="report-sdk-email">
                <Dropdown.Button
                    disabled={this.props.disableDownload}
                    className="report-sdk-email-dropdown"
                    size="small"
                    width={120}
                    options={options}
                    title="下载"
                    onHandleMenuClick={this.onDropdownClick}
                />

                <Dialog
                    title="发送邮箱"
                    width={500}
                    visible={visible}
                    footer={footer}
                    onCancel={this.onCancel}
                >
                    <div className="report-sdk-email-setting">
                        <Form labelCol={{span: 5}} onSubmit={e => {e.preventDefault();}}>
                            <Form.Item
                                label="邮箱"
                                required
                                validateStatus={emailError ? 'error' : 'success'}
                                help={emailError}
                                extra={timeOut ? '数据量较大，减少您的等待时间，请输入邮箱，数据会发送至邮箱。' : ''}
                            >
                                <div className={inputClass}>
                                    <div className="report-sdk-email-tags">
                                        {emails.map(v => {
                                            return (
                                                <Tag
                                                    key={v}
                                                    closable
                                                    bordered={false}
                                                    size="small"
                                                    onClose={() => this.onClose(v)}
                                                >
                                                    {isEmail(v)
                                                        ? <span
                                                            className="report-sdk-email-tags-text"
                                                            title={v}
                                                        >
                                                            {v}
                                                        </span>
                                                        : <Popover
                                                            content={(
                                                                <div className="report-sdk-email-required">
                                                                    您的邮箱格式不符合规范，请检查您的设置。
                                                                </div>
                                                            )}
                                                        >
                                                            <span
                                                                className="
                                                                report-sdk-email-tags-text
                                                                report-sdk-email-required"
                                                            >
                                                                {v}
                                                            </span>
                                                        </Popover>
                                                    }
                                                </Tag>
                                            );
                                        })}
                                    </div>
                                    <div className="report-sdk-email-content-input">
                                        <input
                                            value={inputEmails}
                                            onChange={this.onInputChange}
                                            onKeyDown={this.keyDown}
                                            disabled={emails.length === maxEmails}
                                            placeholder={emails.length === maxEmails
                                                ? `您最多可添加${maxEmails}个邮箱` : '同时输入多个邮箱请以;分隔'}
                                        />
                                        <span>{emails.length} / {maxEmails}</span>
                                    </div>
                                </div>
                                {this.state.isNeedFilterEmailDownload
                                    ? (
                                        <Radio.Group
                                            className="report-sdk-email-group"
                                            direction="column"
                                            value={this.state.emailSelectOption.value}
                                            onChange={this.onBtnClick}
                                            options={emailFilterOptions}
                                        />
                                    ) : ''
                                }
                            </Form.Item>
                        </Form>
                    </div>
                </Dialog>
                <Dialog
                    title="下载设置"
                    width={500}
                    visible={this.state.settingsVisible}
                    onCancel={this.onCancelSettings}
                    onOk={this.onSaveSettings}
                >
                    <div className="report-sdk-including-del-setting">
                        <span className="report-sdk-including-del-setting-title">已删除的物料</span>
                        <Popover content="如您开启已删除物料，我们会为您生成和数据中心内数据下载功能相同的结果：结果中包含在您选择的时间范围内已经产生过展现、点击、消费但当前已经删除的数据内容。
                            从未产生过展现、点击、消费的数据不在下载结果中。
                            如您关闭已删除物料，我们会使您下载的结果和您在当前物料列表中看到的数据一致：对于已删除的物料和对应的数据不会出现在下载结果中，下载结果中的数据行和您在列表中看到的数据行完全一致。
                            特别注明：无论您是否勾选该选项，下载文件中的数据列会与您在当前列表中自定义选择的数据列保持完全一致，所有状态列（包括：状态、出价、匹配模式、指导价等）不会出现再下载结果中。"
                        >
                            <IconQuestionCircle className="report-sdk-including-del-setting-tip-question" />
                        </Popover>
                        <Switch
                            checked={this.state.includeDeletedMaterial}
                            onChange={this.onChageIncludeDelMaterial}
                            className="report-sdk-including-del-setting-switch"
                        />
                        <div>{this.state.includeDeletedMaterial ? '开启后每次下载包括已删除物料' : '关闭后每次下载不包括已删除'}</div>
                    </div>
                </Dialog>
            </div>
        );
    }
}
