/**
 * @file sdk - 指标筛选
 * <AUTHOR>
 * @date 2020/08/21
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Dropdown, Tabs, Toast} from '@baidu/one-ui';
import {getIndicatorInfo} from '../../../utils/table';

const TabPane = Tabs.TabPane;

export default class Indicator extends PureComponent {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }
    constructor(props) {
        super(props);
        const {defaultIndicator, tableIndicatorList, tableIndicatorTitle} = props;
        const {title} = getIndicatorInfo(defaultIndicator || [], tableIndicatorList, tableIndicatorTitle);
        this.state = {
            // 表格指标数据
            tableIndicatorKey: '',
            tableIndicatorVisible: false,
            tableIndicatorKeyList: [],
            title
        };
    }

    static propTypes = {
        tableIndicatorList: PropTypes.array
    }
    static defaultProps = {
        tableIndicatorList: []
    }

    onTableIndicatorVisibleChange = visible => {
        this.setState({
            tableIndicatorVisible: visible
        });
    }

    handleClickTableIndicatorMenu = e => {
        const {tableLoading} = this.props;
        if (tableLoading) {
            Toast.error({
                content: '数据加载中，暂不支持操作',
                duration: 3,
                showCloseIcon: false
            });
            return;
        }
        const {eventHub: {trigger}} = this.context;
        const indicatorUiType = this.props.indicatorUiType;
        let targetKey;
        let tableIndicatorKeyList;
        if (indicatorUiType === 'dropDown') {
            targetKey = e && e.key;
            tableIndicatorKeyList = e && e.keyPath;
        } else {
            targetKey = e;
            tableIndicatorKeyList = [e];
        }
        const {
            title,
            additionFields,
            indicatorExternalFilters,
            reportType,
            indicatorTableSupportCommonFilter
        } = getIndicatorInfo(
            e && e.keyPath || [], this.props.tableIndicatorList, this.props.tableIndicatorTitle);
        const isParentKey = ('' + targetKey).includes('parent');
        targetKey = isParentKey ? tableIndicatorKeyList[1] : targetKey;
        this.setState({
            tableIndicatorKey: reportType || targetKey,
            tableIndicatorKeyList,
            tableIndicatorVisible: false,
            title
        }, () => {
            trigger('sdk.event.table.clear.selected.rows');
            trigger('sdk.event.table.indicator.change', {
                data: {
                    targetKey: reportType || targetKey,
                    additionFields, tableIndicatorKeyList,
                    indicatorExternalFilters,
                    indicatorTableSupportCommonFilter
                }
            });
        });
    }
    render() {
        const {tableIndicatorList, indicatorUiType, className, tableReportType} = this.props;
        const {
            title,
            tableIndicatorKey
        } = this.state;
        if (indicatorUiType === 'dropDown') {
            const tableIndicatorProps = {
                title,
                options: tableIndicatorList,
                trigger: 'click',
                size: 'small',
                onHandleMenuClick: this.handleClickTableIndicatorMenu
            };
            return (<Dropdown.Button {...tableIndicatorProps} />);
        }
        const topTabsProps = {
            className,
            activeKey: '' + (tableIndicatorKey || tableReportType),
            onChange: this.handleClickTableIndicatorMenu
        };
        return (<Tabs {...topTabsProps}>
            {
                tableIndicatorList.map((tab, index) => <TabPane tab={tab.label} key={'' + tab.value} />)
            }
        </Tabs>);
    }
}
