/**
 * @file 饼图
 * <AUTHOR>
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Pie} from '@baidu/one-charts';
import {cloneDeep} from 'lodash-es';
import ReportSdkChartsService from '../../../../services/ChartsServices/ReportSdkChartsService';
import {getApiToast, getApiError} from '../../../../utils/common';
import {formatApiData, getChartsDataParams, getIndicatorList, getColumns} from '../../../../utils/charts';

export default class Pie<PERSON>hart extends PureComponent {

    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }

    constructor(props, context) {
        super(props, context);
        this.state = {
            metrics: [],
            listData: [],
            sortRule: {},
            // 报表公共筛选信息
            commonFilterInfo: {},
            isLoading: true
        };
    }

    componentDidMount() {
        const {eventHub: {on}} = this.context;
        const {
            chartConfig: {
                isRefreshOnIndicatorChange = false
            }
        } = this.props;
        this.service = new ReportSdkChartsService(this.context);
        this.handleGetChartData({data: {}});
        on('sdk.event.chart.fetch', this.handleGetChartData);
        if (isRefreshOnIndicatorChange) {
            on('sdk.event.chart.metrics.change', this.handleGetChartData);
        }
    }
    componentWillUnmount() {
        const {eventHub: {un}} = this.context;
        const {
            chartConfig: {
                isRefreshOnIndicatorChange = false
            }
        } = this.props;
        un('sdk.event.chart.fetch', this.handleGetChartData);
        if (isRefreshOnIndicatorChange) {
            un('sdk.event.chart.metrics.change', this.handleGetChartData);
        }
    }

    handleAfterSetOptionOnce = instance => {
        this.echarts = instance;
    }

    handleGetChartData = ({data: {commonFilterInfo, metrics}}) => {
        const {
            eventHub: {fire, trigger},
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {
            commonFilterInfo: commonFilterInfoLast,
            chartConfig
        } = this.props;
        const {sortType = 'DESC'} = chartConfig;
        const filtersInfo = cloneDeep(commonFilterInfo ? commonFilterInfo : commonFilterInfoLast);
        // 过滤不必要请求
        if (!Object.keys(filtersInfo || {}).length) {
            return;
        }
        this.setState({
            isLoading: true,
            listData: []
        });
        let sortRule = {};
        sortRule.sortName = (metrics && metrics.length) ? metrics[0] : this.getMetrics();
        sortRule[sortRule.sortName] = {
            isAscend: sortType === 'ASC'
        };
        const {
            rowCount,
            isResponseCompare,
            isResponseTimeWindow,
            sendId = ''
        } = chartConfig;
        if (isResponseTimeWindow && typeof isResponseTimeWindow === 'string') {
            filtersInfo.timeUnit = isResponseTimeWindow;
        }
        // 获取请求表头接口的入参
        const tableDataParams = getChartsDataParams({
            sortRule,
            commonFilterInfo: filtersInfo
        }, getColumns({
            chartConfig,
            reportAreaConfig,
            commonFilterInfo: filtersInfo
        }), chartConfig);

        if (rowCount) {
            tableDataParams.rowCount = rowCount;
        }
        if (!isResponseCompare && tableDataParams.compareStartDate) {
            delete tableDataParams.compareStartDate;
        }

        const requestServices = tableDataParams.compareStartDate ? 'getCompareTableData' : 'getTableData';
        // 请求表头数据
        this.service[requestServices]({
            params: tableDataParams
        }).then(({data, status}) => {
            if (status === 0) {
                fire('sdk.event.enter.log', {
                    hitType: 'pie-data-success'
                });
                const {datasource: listData, summary} = formatApiData(data);
                this.setState({
                    listData,
                    summary
                });
            }
            else {
                getApiToast({content: '获取数据失败'});
            }
        }).catch(err => {
            getApiError(err);
        }).finally(data => {
            const {listData = []} = this.state;
            if (sendId) {
                trigger('sdk.event.chart.fetchGroup', {data: {
                    topTabKey: this.props.topTabKey,
                    commonFilterInfo: cloneDeep(commonFilterInfo ? commonFilterInfo : commonFilterInfoLast),
                    groupIds: listData.map(v => v[sendId])
                }});
            }
            this.setState({isLoading: false});
        });
    }

    getMetrics() {
        let {
            chartConfig,
            metrics
        } = this.props;
        const {
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {defaultIndicator} = getIndicatorList({
            chartConfig,
            reportAreaConfig
        });
        metrics = (metrics && metrics.length) ? metrics : [defaultIndicator];
        metrics = metrics[0];
        return metrics;
    }

    render() {
        // todo 饼图有情况不随指标改变而变化
        let {
            chartConfig,
            metrics
        } = this.props;
        const {
            listData,
            summary
        } = this.state;
        const {
            itemStyle,
            isMatchKey,
            xAxisField,
            bottomIndicator,
            isRing = false,
            graphic,
            titleArea: {title = ''}
        } = chartConfig;
        const {
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {indicatorList, defaultIndicator} = getIndicatorList({
            chartConfig,
            reportAreaConfig
        });
        const chartData = {
            columns: getColumns({
                chartConfig,
                reportAreaConfig
            }),
            rows: listData
        };
        // 底部文字
        let bottomIndicatorText = '';
        let bottomGraphic = null;
        if (listData.length && bottomIndicator && bottomIndicator.value) {
            const {value, render} = bottomIndicator;
            const item = indicatorList.find(v => v.value === value);
            if (item) {
                bottomIndicatorText = `${item.label}对比：`;
                const dataTextList = listData.map(v => {
                    let result = `${v[xAxisField]}：`;
                    result += render ? render(v[value]) : v[value];
                    return result;
                });
                bottomIndicatorText += dataTextList.join(' ');
                const length = 70;
                if (bottomIndicatorText.length > length) {
                    bottomIndicatorText
                        = bottomIndicatorText.slice(0, length)
                        + '\n'
                        + bottomIndicatorText.slice(length);
                }

                bottomGraphic = {
                    type: 'text',
                    top: 'bottom',
                    left: 'center',
                    cursor: 'default',
                    style: {
                        text: bottomIndicatorText,
                        textAlign: 'center'
                    }
                };
            };
        }
        metrics = (metrics && metrics.length) ? metrics : [defaultIndicator];
        const indicator = indicatorList.find(v => v.value === metrics[0]);
        const settings = {
            dataUnit: indicator.unit || '',
            dataType: indicator.dataType || {precision: 2},
            tooltipLabel: indicator.label || '',
            dimension: [xAxisField],
            metrics
        };
        if (graphic && listData.length) {
            const commonGraphic = {
                type: 'text',
                top: 'middle',
                left: 'center',
                cursor: 'default'
            };
            const item = indicatorList.find(v => v.value === xAxisField);
            settings.graphic = {
                elements: [
                    {
                        ...commonGraphic,
                        style: {
                            text: graphic.titleRender({
                                title: item ? item.label : '',
                                data: listData,
                                summary,
                                metrics: metrics[0],
                                metricsLabel: indicator.label
                            }),
                            textAlign: 'center'
                        }
                    }
                ]
            };
        }
        if (bottomGraphic) {
            if (settings.graphic) {
                settings.graph.elements.push(bottomGraphic);
            }
            else {
                settings.graphic = {
                    elements: [
                        bottomGraphic
                    ]
                };
            }
        }
        const chartTitle = title.replace('{INDICATOR}', indicator ? indicator.label : '');
        const pieProps = {
            title: chartTitle,
            data: chartData,
            isTransColor: true,
            isRing,
            settings,
            afterSetOptionOnce: this.handleAfterSetOptionOnce,
            isLoading: this.state.isLoading
        };
        if (itemStyle) {
            let itemStyleResult = {};
            if (isMatchKey) {
                listData.forEach((item, index) => {
                    itemStyleResult[index + 1] = itemStyle[item[xAxisField]];
                });
            }
            else {
                itemStyleResult = itemStyle;
            }
            pieProps.itemStyle = itemStyleResult;
        }
        return (
            <div className="report-sdk-chart-pie">
                <div><Pie {...pieProps} /></div>
            </div>
        );
    }
}