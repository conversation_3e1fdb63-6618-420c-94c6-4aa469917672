import React, {PureComponent} from 'react';
import {isArray, cloneDeep} from 'lodash-es';
import PropTypes from 'prop-types';
import {Line} from '@baidu/one-charts';
import ReportSdkChartsService from '../../../../services/ChartsServices/ReportSdkChartsService';
import {getApiToast, getApiError, getLastLengthTime} from '../../../../utils/common';
import IndicatorButton from '../../indicatorButton';
import {formatText} from '../../../../utils/table';
import {formatApiData, getChartsDataParams, getIndicatorList, getColumns} from '../../../../utils/charts';

export default class LineChart extends PureComponent {

    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }

    constructor(props, context) {
        super(props, context);
        this.state = {
            listData: [],
            sortRule: {
                sortName: 'date',
                date: {
                    isAscend: true
                }
            },
            // 报表公共筛选信息
            commonFilterInfo: {},
            isTableLoading: true,
            isLoading: true
        };
    }

    componentDidMount() {
        const {eventHub: {on}} = this.context;
        this.service = new ReportSdkChartsService(this.context);
        const {topTabKey} = this.props;
        this.handleGetChartData({data: {topTabKey}});
        on('sdk.event.chart.fetch', this.handleGetChartData);
        on('sdk.event.chart.fetchGroup', this.handleGetChartData);
    }

    componentWillUnmount() {
        const {eventHub: {un}} = this.context;
        un('sdk.event.chart.fetch', this.handleGetChartData);
        un('sdk.event.chart.fetchGroup', this.handleGetChartData);
    }

    handleAfterSetOptionOnce = instance => {
        this.echarts = instance;
    }

    handleGetChartData = ({data: {topTabKey, commonFilterInfo, groupIds}}) => {
        const {
            eventHub: {fire},
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {
            topTabKey: topTabKeyLast,
            commonFilterInfo: commonFilterInfoLast,
            chartConfig,
            metrics
        } = this.props;
        const {
            isResponseIndicatorButtonChange,
            isResponseCompare = true,
            isResponseTimeWindow,
            isSingleIndicator,
            singleIndicatorRequired,
            defaultIndicator,
            xAxisField,
            groupField,
            groupFieldLabel
        } = chartConfig;
        const filtersInfo = cloneDeep(commonFilterInfo || commonFilterInfoLast);
        const isEmptyGroup = groupField && !(groupIds && groupIds.length);
        // 过滤不必要请求
        if (
            topTabKeyLast !== topTabKey
            || !Object.keys(filtersInfo || {}).length
            || isEmptyGroup
        ) {
            if (isEmptyGroup) {
                this.setState({isLoading: false, listData: []});
            }
            return;
        }
        this.setState({isLoading: true});
        let columns = getColumns({
            chartConfig,
            reportAreaConfig,
            commonFilterInfo: filtersInfo
        });
        if (groupField) {
            const filter = {
                column: groupField,
                operator: 'IN',
                values: groupIds
            };
            if (filtersInfo.filterFields) {
                filtersInfo.filterFields.push(filter);
            } else {
                filtersInfo.filterFields = [filter];
            }
            columns.push(...[groupField, groupFieldLabel]);
        }
        if (isResponseTimeWindow && typeof isResponseTimeWindow === 'string') {
            filtersInfo.timeUnit = isResponseTimeWindow;
        }
        if (filtersInfo.timeUnit === 'HOUR') {
            filtersInfo.timeUnit = 'HOUR_IN_DAY';
        }
        if (isSingleIndicator) {
            const selectedMetrics = metrics.length ? metrics : [defaultIndicator];
            columns = [xAxisField, selectedMetrics[0]].concat(singleIndicatorRequired);
        }
        // 获取请求表头接口的入参
        const tableDataParams = getChartsDataParams({
            sortRule: this.state.sortRule,
            commonFilterInfo: filtersInfo
        }, columns, chartConfig);

        if (isResponseIndicatorButtonChange && !filtersInfo.compareStartDate) {
            const {startDate, endDate} = filtersInfo;
            tableDataParams.compareStartDate = getLastLengthTime(startDate, endDate);
        }
        if (!isResponseCompare) {
            if (tableDataParams.compareStartDate) {
                delete tableDataParams.compareStartDate;
            }
        }
        const requestServices = tableDataParams.compareStartDate ? 'getCompareTableData' : 'getTableData';

        // 请求表头数据
        this.service[requestServices]({
            params: tableDataParams
        }).then(({data, status}) => {
            if (status === 0) {
                fire('sdk.event.enter.log', {
                    hitType: 'line-data-success'
                });
                const {datasource, summary} = formatApiData(data);
                this.setState({
                    totalCount: data.totalRowCount || 0,
                    listData: datasource,
                    isLoading: false,
                    summary
                });
            } else {
                getApiToast({content: '获取数据失败'});
            }
        }).catch(err => {
            getApiError(err);
        }).finally(data => {
            this.setState({isLoading: false});
        });
    }

    formatAxisSite = (unit, dataType) => value => {
        const {precision = 0} = dataType;
        return formatText(value, unit === '%', (precision === 0 ? precision : 1), true, 1);
    };

    getAxisSite = (metrics, labelMap) => {
        const leftMetrics = labelMap[metrics[0]] || {};
        const rightMetrics = labelMap[metrics[1]];
        const isOneAxisSite = metrics.length < 2;
        const baseAxisSite = {
            left: {
                ...(!isOneAxisSite ? {name: leftMetrics.unit
                    ? `${leftMetrics.label}(${leftMetrics.unit})`
                    : leftMetrics.label} : {}),
                unit: leftMetrics.unit,
                dataType: leftMetrics.dataType || {precision: 2},
                formatter: this.formatAxisSite(leftMetrics.unit, leftMetrics.dataType || {})
            }
        };
        if (metrics.length < 2) {
            return baseAxisSite;
        }
        baseAxisSite.right = {
            name: rightMetrics.unit ? `${rightMetrics.label}(${rightMetrics.unit})` : rightMetrics.label,
            unit: rightMetrics.unit,
            dataType: rightMetrics.dataType || {precision: 2},
            formatter: this.formatAxisSite(rightMetrics.unit, rightMetrics.dataType || {}),
            dimensions: [metrics[1], `${metrics[1]}Target`]
        };
        return baseAxisSite;
    }

    getRows = (metrics, isCompare) => {
        const {listData} = this.state;
        const {
            chartConfig: {
                xAxisField,
                isResponseIndicatorButtonChange,
                groupField,
                groupFieldLabel,
                showLegendLabelWithValue,
                legendLabelIdField
            }
        } = this.props;
        if (!(listData.length && listData[0][xAxisField] && isArray(listData[0][xAxisField]))) {
            if (groupField) {
                const rows = {};
                const groups = {};
                listData.forEach(row => {
                    const {date} = row;
                    if (!rows[date]) {
                        rows[date] = {};
                    }
                    const fieldValue = row[groupField];
                    const fieldLabel = row[groupFieldLabel];
                    const legendLabelIdValue = row[legendLabelIdField];
                    const selectedMetric = metrics[0];
                    rows[date][fieldValue] = row[selectedMetric];
                    if (!groups[fieldValue]) {
                        groups[fieldValue] = showLegendLabelWithValue
                            ? `${fieldLabel}(ID:${legendLabelIdValue})` // fix 重名
                            : fieldLabel;
                    }
                });
                return {groups, listData: Object.keys(rows).map(row => ({date: row, ...rows[row]}))};
            }
            return listData;
        }
        return listData.map(row => {
            const formatRow = {};
            Object.keys(row).forEach(indicator => {
                let label = '';
                if (indicator === xAxisField && (!isResponseIndicatorButtonChange || isCompare)) {
                    label = `${row[indicator][0]}/${row[indicator][1]}`;
                } else {
                    label = row[indicator][0];
                }
                formatRow[indicator] = label;
                formatRow[`${indicator}Target`] = row[indicator][1];
                formatRow[`${indicator}Ratio`] = row[indicator][3];
            });
            return formatRow;
        });
    }

    getCompareOption = (metrics, labelMap) => {
        const lineStyle = {};
        const dataGroup = [];
        metrics.forEach(metric => {
            const conpareIndicator = `${metric}Target`;
            lineStyle[conpareIndicator] = {
                type: 'dotted'
            };
            dataGroup.push({
                name: labelMap[metric] && labelMap[metric].label,
                data: [metric, `${metric}Target`],
                splitCharacter: '/'
            });
        });
        return {
            lineStyle,
            dataGroup
        };
    }

    axisLableFormatter = dataCount => (value, index) => {
        const {chartConfig} = this.props;
        const {xAxisFieldFormatter} = chartConfig;
        let label = value;
        if (typeof xAxisFieldFormatter === 'function') {
            label = xAxisFieldFormatter(value, index);
        }
        if (dataCount !== 1 && (index === 0 || index === dataCount - 1)) {
            return '';
        }
        if (label.includes(' ')) {
            label = label.split(' ').pop();
        }
        return label;
    }

    render() {
        const {
            className,
            chartConfig,
            topTabKey,
            chartKey,
            metrics,
            commonFilterInfo,
            isFilterCheckButton
        } = this.props;
        const {
            isLoading,
            summary
        } = this.state;
        const {
            startDate,
            endDate,
            compareStartDate,
            compareEndDate
        } = commonFilterInfo;
        const {
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {
            xAxisField,
            indicatorList: chartIndicatorList,
            indicatorButtonList,
            defaultIndicator: chartDefaultIndicator,
            isSingleIndicatorButton,
            isResponseIndicatorButtonChange,
            isButtonResponseCompare,
            isShowIndicator,
            indicatorUiType,
            titleArea: {
                title
            } = {},
            legendPosition,
            isResponseCompare = true,
            xAxisFieldFormatter,
            groupField,
            isLegendTitleOccupyOneLine,
            chartHeight,
            isLegendCutOff = false,
            legendItemWidth,
            operatorWidth
        } = chartConfig;
        const isCompare = !!compareStartDate && isResponseCompare;
        const {indicatorList, defaultIndicator} = getIndicatorList({
            chartConfig,
            reportAreaConfig,
            commonFilterInfo
        });
        const selectedMetrics = metrics.length ? metrics : [defaultIndicator];
        let rows = this.getRows(selectedMetrics, isCompare);
        const rawLabelMap = {};
        let labelMap = (indicatorList || []).reduce((ret, item) => {
            const {label, unit, dataType = {precision: 2}} = item;
            ret[item.value] = label;
            rawLabelMap[item.value] = {label, unit, dataType};
            if (isCompare) {
                ret[item.value] = `${startDate}~${endDate} ${label}`;
                ret[`${item.value}Target`] = `${compareStartDate}~${compareEndDate} ${label}`;
            }
            return ret;
        }, {});
        let columns = getColumns({
            chartConfig,
            reportAreaConfig,
            indicatorList,
            isCompare
        });
        if (groupField) {
            labelMap = rows.groups;
            rows = rows.listData;
            columns = Object.keys(labelMap);
        }

        const chartData = {
            columns,
            rows
        };

        const settings = {
            dimension: [xAxisField],
            metrics: selectedMetrics,
            labelMap
        };
        settings.axisSite = this.getAxisSite(settings.metrics, rawLabelMap, isCompare);
        const isOneAxisSite = settings.metrics.length < 2;
        const extend = {
            grid: {
                right: isOneAxisSite
                    ? 16
                    : 72
            }
        };
        const lineProps = {
            data: chartData,
            settings,
            afterSetOptionOnce: this.handleAfterSetOptionOnce,
            isLoading,
            legendProps: {
                horizontalAlign: legendPosition,
                isTitleOccupyOneLine: isLegendTitleOccupyOneLine,
                isCutOff: isLegendCutOff,
                ...(legendItemWidth ? {itemWidth: legendItemWidth} : {}),
                ...(operatorWidth ? {maxWidth: `calc(100% - ${operatorWidth})`} : {})
            },
            extend
            // afterConfig: option => {console.log(option);}
        };
        const dataCount = chartData.rows && chartData.rows.length;
        const isOneData = dataCount === 1;
        const seriesStyle = {
            showSymbol: true,
            symbol: 'circle',
            symbolSize: 6
        };
        const formatter = typeof xAxisFieldFormatter === 'string'
            ? xAxisFieldFormatter
            : this.axisLableFormatter(dataCount);
        if (lineProps.extend) {
            lineProps.extend['grid.left'] = isOneAxisSite ? 62 : 72;
            lineProps.extend['xAxis.0.axisLabel.formatter'] = formatter;
            lineProps.extend.yAxis = {nameGap: 60};
            if (isOneData) {
                lineProps.extend.series = seriesStyle;
            }
        }
        if (title) {
            if (React.isValidElement(title)) {
                lineProps.title = title;
            } else {
                const indicatorInfo = indicatorList.filter(item => item.value === settings.metrics[0])[0];
                const indicator = indicatorInfo && indicatorInfo.label;
                lineProps.title = `${title}${isCompare && isResponseCompare ? '对比' : ''}`;
                if (indicator) {
                    lineProps.title = lineProps.title.replace('{INDICATOR}', indicator);
                }
            }
        }

        if (isCompare) {
            const {lineStyle, dataGroup} = this.getCompareOption(settings.metrics, rawLabelMap);
            lineProps.lineStyle = lineStyle;
            settings.dataGroup = dataGroup;

            settings.metrics = settings.metrics.reduce((ret, metric) => {
                ret.push(...[metric, `${metric}Target`]);
                return ret;
            }, []);
        }
        if (groupField) {
            settings.metrics = Object.keys(labelMap);
        }

        if (chartHeight) {
            lineProps.style = {
                height: chartHeight
            };
        }
        const indicatorButtonProps = {
            topTabKey,
            chartKey,
            className,
            indicatorList: isResponseIndicatorButtonChange ? chartIndicatorList : indicatorButtonList,
            isSingleIndicatorButton,
            isResponseIndicatorButtonChange,
            isButtonResponseCompare,
            defaultIndicator: chartDefaultIndicator,
            metrics,
            isCompare,
            chartConfig,
            reportAreaConfig,
            isChartLoading: isLoading,
            summary
        };
        const displayIndicatorBtn = isShowIndicator
            && (indicatorUiType === 'button' || isFilterCheckButton);
        return (<div>
            {displayIndicatorBtn && <IndicatorButton {...indicatorButtonProps} />}
            <Line {...lineProps} />
        </div>);
    }
}