/**
 * @file 地图
 * <AUTHOR>
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import ReportSdkChartsService from '../../../../services/ChartsServices/ReportSdkChartsService';
import {OneMap} from '@baidu/one-charts';
import {getApiToast, getApiError} from '../../../../utils/common';
import {formatApiData, getChartsDataParams, getColumns, getIndicatorList} from '../../../../utils/charts';
import Table from '../smallTable';
import {provinceMap} from './map.config';
import {isEmpty, cloneDeep, isArray} from 'lodash-es';
import {getProvice} from './province';

export default class MapChart extends PureComponent {

    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }

    constructor(props, context) {
        super(props, context);
        this.state = {
            metrics: [],
            listData: [],
            sortRule: {},
            // 联动改变的配置项
            eventOption: {},
            // 报表公共筛选信息
            commonFilterInfo: {},
            isShowSmalTable: this.props.chartConfig.isShowSmalTable,
            mapType: 'china',
            mapName: '全国',
            isLoading: true,
            provinceId: 0,
            provinceMapOrigin: null
        };
    }

    componentDidMount() {
        const {eventHub: {on}} = this.context;
        const {
            chartConfig: {
                isRefreshOnIndicatorChange = false
            }
        } = this.props;
        this.service = new ReportSdkChartsService(this.context);
        this.handleGetChartData({data: {}});
        on('sdk.event.chart.fetch', this.handleGetChartData);
        if (isRefreshOnIndicatorChange) {
            on('sdk.event.chart.metrics.change', this.handleGetChartData);
        }
    }
    componentWillUnmount() {
        const {eventHub: {un}} = this.context;
        const {
            chartConfig: {
                isRefreshOnIndicatorChange = false
            }
        } = this.props;
        un('sdk.event.chart.fetch', this.handleGetChartData);
        if (isRefreshOnIndicatorChange) {
            un('sdk.event.chart.metrics.change', this.handleGetChartData);
        }
    }
    handleAfterSetOptionOnce = instance => {
        this.echarts = instance;
    }
    handleAfterSetOption = instance => {
        instance.off('click');
        instance.on('click', this.onEnterToProvince);
    }

    handleGetChartData = ({data: {provinceId, metrics, commonFilterInfo}}) => {
        const {
            eventHub: {fire},
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {
            commonFilterInfo: commonFilterInfoLast,
            chartConfig
        } = this.props;
        const {sortType = 'DESC'} = chartConfig;
        const filtersInfo = cloneDeep(commonFilterInfo ? commonFilterInfo : commonFilterInfoLast);
        // 过滤不必要请求
        if (!Object.keys(filtersInfo || {}).length) {
            return;
        }
        this.setState({isLoading: true});
        this.setState({
            listData: []
        });
        let sortRule = {};
        sortRule.sortName = (metrics && metrics.length) ? metrics[0] : this.getMetrics();
        sortRule[sortRule.sortName] = {
            isAscend: sortType === 'ASC'
        };
        const {isResponseCompare, isResponseTimeWindow} = chartConfig;
        if (isResponseTimeWindow && typeof isResponseTimeWindow === 'string') {
            filtersInfo.timeUnit = isResponseTimeWindow;
        }
        // 获取请求表头接口的入参
        const tableDataParams = getChartsDataParams({
            sortRule,
            commonFilterInfo: filtersInfo
        }, getColumns({
            chartConfig,
            reportAreaConfig
        }), chartConfig);
        // 是否是省市数据
        if (provinceId) {
            tableDataParams.columns.unshift('cityName');
            let set = new Set(tableDataParams.columns);
            set.delete('provinceName');
            set.delete('provinceId');
            tableDataParams.columns = Array.from(set);
            tableDataParams.filters.push(
                {
                    column: 'provinceId',
                    operator: 'IN',
                    values: [provinceId]
                }
            );
            // todo dataitem添加city字段
        } else {
            this.setState({
                mapType: 'china',
                provinceId: 0
            });
        }
        if (!isResponseCompare && tableDataParams.compareStartDate) {
            delete tableDataParams.compareStartDate;
        }
        const requestServices = tableDataParams.compareStartDate ? 'getCompareTableData' : 'getTableData';
        // 请求表头数据
        this.service[requestServices]({
            params: tableDataParams
        }).then(({data, status}) => {
            if (status === 0) {
                fire('sdk.event.enter.log', {
                    hitType: 'map-data-success'
                });
                const {datasource: listData, summary} = formatApiData(data);
                this.setState({
                    listData,
                    summary,
                    isLoading: false
                });
            } else {
                getApiToast({content: '获取数据失败'});
            }
        }).catch(err => {
            getApiError(err);
        }).finally(data => {
            this.setState({isLoading: false});
        });
    }

    getMetrics() {
        let {
            chartConfig,
            metrics
        } = this.props;
        const {
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {defaultIndicator} = getIndicatorList({
            chartConfig,
            reportAreaConfig
        });
        metrics = (metrics && metrics.length) ? metrics : [defaultIndicator];
        metrics = metrics[0];
        return metrics;
    }

    onEnterToProvince = area => {
        const {mapType} = this.state;
        // 只支持全国地图进入省份地图，并且只有有数据的省份才可以点进去
        if (mapType === 'china' && area && !isEmpty(area) && area.name !== '台湾') {
            const item  = this.state.listData.find(v => v.provinceName === area.name);
            // 如果没有数据，则无法进入
            if (!item || !area.value) {
                return;
            }
            const id = item.provinceId;
            this.onChangeMap({
                name: area.name,
                id
            });
            this.setState({
                mapName: area.name
            });
        }
    }

    onEnterToProvinceTable = area => {
        // todo 表格里的项是否一定存在数值
        const {mapType} = this.state;
        if (mapType === 'china' && area.name !== '台湾') {
            this.onChangeMap(area);
            this.setState({
                mapName: area.name
            });
        }
    }

    renderFunc = (text, record, index) => {
        if (!text) {
            return '';
        }
        else if (text === '台湾') {
            return text;
        }
        return (
            <span
                className='report-sdk-chart-map-back'
                onClick={() => {
                    this.onEnterToProvinceTable({
                        name: text,
                        id: record.provinceId
                    });
                }}
            >
                {text}
            </span>
        );
    };

    onChangeMap = async area => {
        const {name, id = 0} = area;
        this.setState({
            mapType: name === 'china' ? name : `province/${provinceMap[name]}`
        });
        let provinceMapOrigin;
        this.setState({
            isLoading: true
        });
        if (name !== 'china') {
            try {
                const {default: mapOrigin} = await getProvice(provinceMap[name]);
                provinceMapOrigin = mapOrigin;
            } catch (e) {
                getApiToast({content: '地图数据加载失败'});
            }
        }
        // 数据切换
        this.setState({
            provinceId: id,
            provinceMapOrigin
        });
        this.handleGetChartData({data: {provinceId: id}});

    }

    render() {
        const {
            chartConfig
        } = this.props;
        let {
            metrics
        } = this.props;
        const {
            listData,
            summary,
            mapType,
            mapName,
            eventOption,
            isShowSmalTable,
            provinceId,
            provinceMapOrigin
        } = this.state;
        const {
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const xAxisField = provinceId ? 'cityName' : 'provinceName';
        let {indicatorList, defaultIndicator} = getIndicatorList({
            chartConfig,
            reportAreaConfig
        });
        indicatorList = cloneDeep(indicatorList);
        const chartData = {
            columns: getColumns({
                chartConfig,
                reportAreaConfig,
                indicatorList
            }),
            rows: listData
        };
        const {itemStyle, isTransColor, titleArea: {title = ''}} = chartConfig;
        metrics = (metrics && metrics.length) ? metrics : [defaultIndicator];
        const indicator = indicatorList.find(v => v.value === metrics[0]);
        const settings = {
            dataUnit: indicator.unit || '',
            dataType: indicator.dataType || {precision: 2},
            tooltipLabel: indicator.label || '',
            mapType,
            dimension: [xAxisField],
            metrics,
            useMapOption: mapType !== 'china',
            labelMap: (indicatorList || []).reduce((ret, item) => {
                ret[item.value] = item.label;
                return ret;
            }, {}),
            ...eventOption
        };
        if (provinceId && provinceMapOrigin) {
            settings.mapOrigin = provinceMapOrigin;
        }
        const chartTitle = title.replace('{INDICATOR}', indicator ? indicator.label : '');
        const mapProps = {
            title: mapType === 'china' ? chartTitle : '',
            isTransColor: true,
            hoverColor: 'orange',
            data: chartData,
            settings,
            afterSetOptionOnce: this.handleAfterSetOptionOnce,
            afterSetOption: this.handleAfterSetOption,
            // 暂时写死，需要替换
            isLoading: this.state.isLoading
        };
        if (itemStyle) {
            mapProps.itemStyle = itemStyle;
        }

        const backToChinaProps = {
            onClick: () => {
                this.onChangeMap({
                    name: 'china',
                    id: 0
                });
            },
            className: 'report-sdk-chart-map-back'
        };

        let tableProps = {};
        if (isShowSmalTable) {
            // 全国才可以进入，台湾不进入
            if (mapType === 'china') {
                indicatorList.forEach(v => {
                    if (v.isMap) {
                        v.value = xAxisField;
                        v.render = this.renderFunc;
                    }
                });
            } else {
                const item = indicatorList.find(v => v.isMap);
                item.value = 'cityName';
                item.render = (text, record, index) => text;
            }
            // 根据情况改变小表格的显示
            const tableChartConfig = cloneDeep(chartConfig);
            tableChartConfig.indicatorList = indicatorList;
            if (tableChartConfig.indicatorList && isArray(tableChartConfig.indicatorList)) {
                tableChartConfig.indicatorList.forEach(v => {
                    if (v.judgeShow) {
                        v.isShow = metrics.includes(v.value);
                    }
                });
            }
            if (tableChartConfig.needPercent) {
                tableChartConfig.needPercent = cloneDeep(metrics);
            }
            tableProps = {
                listData: cloneDeep(listData),
                summary,
                metrics: metrics,
                sort: {
                    sortField: metrics[0],
                    sortOrder: 'desc'
                },
                isLoading: this.state.isLoading,
                chartConfig: tableChartConfig
            };
        }
        return (
            <div className="report-sdk-chart-map">
                <div className="report-sdk-chart-map-map">
                    {
                        mapType !== 'china'
                        && <div className="report-sdk-chart-map-area">
                            <span {...backToChinaProps}>全国  </span> &gt; {mapName}
                        </div>
                    }
                    <OneMap {...mapProps} />
                </div>
                {
                    isShowSmalTable
                    && <div className="report-sdk-chart-map-table">
                        <Table {...tableProps} />
                    </div>
                }
            </div>
        );
    }
}
