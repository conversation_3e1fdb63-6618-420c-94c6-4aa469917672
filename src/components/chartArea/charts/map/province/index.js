export const getProvice = proviceName => {
    let proviceData = {};
    switch (proviceName) {
        case 'anhui':
            proviceData = import(/* webpackChunkName: "anhui" */ './anhui.json');
            break;
        case 'aomen':
            proviceData = import(/* webpackChunkName: "aomen" */ './aomen.json');
            break;
        case 'beijing':
            proviceData = import(/* webpackChunkName: "beijing" */ './beijing.json');
            break;
        case 'chongqing':
            proviceData = import(/* webpackChunkName: "chongqing" */ './chongqing.json');
            break;
        case 'fujian':
            proviceData = import(/* webpackChunkName: "fujian" */ './fujian.json');
            break;
        case 'gansu':
            proviceData = import(/* webpackChunkName: "gansu" */ './gansu.json');
            break;
        case 'guangdong':
            proviceData = import(/* webpackChunkName: "guangdong" */ './guangdong.json');
            break;
        case 'guangxi':
            proviceData = import(/* webpackChunkName: "guangxi" */ './guangxi.json');
            break;
        case 'guizhou':
            proviceData = import(/* webpackChunkName: "guizhou" */ './guizhou.json');
            break;
        case 'hainan':
            proviceData = import(/* webpackChunkName: "hainan" */ './hainan.json');
            break;
        case 'hebei':
            proviceData = import(/* webpackChunkName: "hebei" */ './hebei.json');
            break;
        case 'heilongjiang':
            proviceData = import(/* webpackChunkName: "heilongjiang" */ './heilongjiang.json');
            break;
        case 'henan':
            proviceData = import(/* webpackChunkName: "henan" */ './henan.json');
            break;
        case 'hubei':
            proviceData = import(/* webpackChunkName: "hubei" */ './hubei.json');
            break;
        case 'hunan':
            proviceData = import(/* webpackChunkName: "hunan" */ './hunan.json');
            break;
        case 'jiangsu':
            proviceData = import(/* webpackChunkName: "jiangsu" */ './jiangsu.json');
            break;
        case 'jiangxi':
            proviceData = import(/* webpackChunkName: "jiangxi" */ './jiangxi.json');
            break;
        case 'jilin':
            proviceData = import(/* webpackChunkName: "jilin" */ './jilin.json');
            break;
        case 'liaoning':
            proviceData = import(/* webpackChunkName: "liaoning" */ './liaoning.json');
            break;
        case 'neimenggu':
            proviceData = import(/* webpackChunkName: "neimenggu" */ './neimenggu.json');
            break;
        case 'ningxia':
            proviceData = import(/* webpackChunkName: "ningxia" */ './ningxia.json');
            break;
        case 'qinghai':
            proviceData = import(/* webpackChunkName: "qinghai" */ './qinghai.json');
            break;
        case 'shandong':
            proviceData = import(/* webpackChunkName: "shandong" */ './shandong.json');
            break;
        case 'shanghai':
            proviceData = import(/* webpackChunkName: "shanghai" */ './shanghai.json');
            break;
        case 'shanxi':
            proviceData = import(/* webpackChunkName: "shanxi" */ './shanxi.json');
            break;
        case 'shanxi1':
            proviceData = import(/* webpackChunkName: "shanxi1" */ './shanxi1.json');
            break;
        case 'sichuan':
            proviceData = import(/* webpackChunkName: "sichuan" */ './sichuan.json');
            break;
        case 'taiwan':
            proviceData = import(/* webpackChunkName: "taiwan" */ './taiwan.json');
            break;
        case 'tianjin':
            proviceData = import(/* webpackChunkName: "tianjin" */ './tianjin.json');
            break;
        case 'xianggang':
            proviceData = import(/* webpackChunkName: "xianggang" */ './xianggang.json');
            break;
        case 'xinjiang':
            proviceData = import(/* webpackChunkName: "xinjiang" */ './xinjiang.json');
            break;
        case 'xizang':
            proviceData = import(/* webpackChunkName: "xizang" */ './xizang.json');
            break;
        case 'yunnan':
            proviceData = import(/* webpackChunkName: "yunnan" */ './yunnan.json');
            break;
        case 'zhejiang':
            proviceData = import(/* webpackChunkName: "zhejiang" */ './zhejiang.json');
            break;
    }
    return proviceData;
};