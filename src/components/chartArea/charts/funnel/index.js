/**
 * @file 漏斗图
 * <AUTHOR>
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Funnel} from '@baidu/one-charts';
import ReportSdkChartsService from '../../../../services/ChartsServices/ReportSdkChartsService';
import {getApiToast, getApiError} from '../../../../utils/common';
import {formatApiData, getChartsDataParams, getColumns, getIndicatorList} from '../../../../utils/charts';
import {cloneDeep, isNil} from 'lodash-es';

export default class FunnelChart extends PureComponent {

    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }

    constructor(props, context) {
        super(props, context);
        this.state = {
            metrics: [],
            listData: [],
            sortRule: {},
            // 报表公共筛选信息
            commonFilterInfo: {},
            isLoading: true
        };
    }

    componentDidMount() {
        const {eventHub: {on}} = this.context;
        const {
            chartConfig: {
                isRefreshOnIndicatorChange = false
            }
        } = this.props;
        this.service = new ReportSdkChartsService(this.context);
        this.handleGetChartData({data: {}});
        on('sdk.event.chart.fetch', this.handleGetChartData);
        if (isRefreshOnIndicatorChange) {
            on('sdk.event.chart.metrics.change', this.handleGetChartData);
        }
    }

    componentWillUnmount() {
        const {eventHub: {un}} = this.context;
        const {
            chartConfig: {
                isRefreshOnIndicatorChange = false
            }
        } = this.props;
        un('sdk.event.chart.fetch', this.handleGetChartData);
        if (isRefreshOnIndicatorChange) {
            un('sdk.event.chart.metrics.change', this.handleGetChartData);
        }
    }

    handleAfterSetOptionOnce = instance => {
        this.echarts = instance;
    }

    handleGetChartData = ({data: {commonFilterInfo, metrics}}) => {
        const {
            eventHub: {fire},
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {
            commonFilterInfo: commonFilterInfoLast,
            chartConfig
        } = this.props;
        const {sortType = 'DESC'} = chartConfig;
        const filtersInfo = cloneDeep(commonFilterInfo ? commonFilterInfo : commonFilterInfoLast);
        // 过滤不必要请求
        if (!Object.keys(filtersInfo || {}).length) {
            return;
        }
        this.setState({isLoading: true});
        let sortRule = {};
        sortRule.sortName = (metrics && metrics.length) ? metrics[0] : this.getMetrics();
        sortRule[sortRule.sortName] = {
            isAscend: sortType === 'ASC'
        };
        const {rowCount, isResponseCompare, isResponseTimeWindow} = chartConfig;

        if (isResponseTimeWindow && typeof isResponseTimeWindow === 'string') {
            filtersInfo.timeUnit = isResponseTimeWindow;
        }
        // 获取请求表头接口的入参
        const tableDataParams = getChartsDataParams({
            commonFilterInfo: filtersInfo
        }, getColumns({
            chartConfig,
            reportAreaConfig,
            commonFilterInfo: filtersInfo
        }), chartConfig);

        if (rowCount) {
            tableDataParams.rowCount = rowCount;
        }
        if (!isResponseCompare) {
            if (tableDataParams.compareStartDate) {
                delete tableDataParams.compareStartDate;
            }
        }
        const requestServices = tableDataParams.compareStartDate ? 'getCompareTableData' : 'getTableData';
        // 请求表头数据
        this.service[requestServices]({
            params: tableDataParams
        }).then(({data, status}) => {
            if (status === 0) {
                fire('sdk.event.enter.log', {
                    hitType: 'funnel-data-success'
                });
                const {datasource: listData, summary} = formatApiData(data);
                this.setState({
                    listData,
                    summary,
                    isLoading: false
                });
            } else {
                getApiToast({content: '获取数据失败'});
            }
        }).catch(err => {
            getApiError(err);
        }).finally(data => {
            this.setState({isLoading: false});
        });
    }

    getMetrics() {
        let {
            chartConfig,
            metrics
        } = this.props;
        const {
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {defaultIndicator} = getIndicatorList({
            chartConfig,
            reportAreaConfig
        });
        metrics = (metrics && metrics.length) ? metrics : [defaultIndicator];
        metrics = metrics[0];
        return metrics;
    }

    render() {
        let {
            chartConfig,
            metrics
        } = this.props;
        const {
            listData,
            summary
        } = this.state;
        const {
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {
            itemStyle,
            xAxisField,
            titleArea: {title = ''},
            useSummary = false,
            width = 'auto',
            left = 'middle',
            staticIndicatorCount
        } = chartConfig;
        let chartData = {
            columns: getColumns({
                chartConfig,
                reportAreaConfig
            }),
            rows: listData
        };
        const {indicatorList, defaultIndicator} = getIndicatorList({
            chartConfig,
            reportAreaConfig
        });
        let settings;
        metrics = (metrics && metrics.length) ? metrics : [defaultIndicator];
        const indicator = indicatorList.find(v => v.value === metrics[0]);
        if (useSummary) {
            let rows = [];
            if (summary) {
                let items = indicatorList.filter(item => item.isShow !== false && !isNil(summary[item.value]));
                if (staticIndicatorCount && listData.length) {
                    items = items.slice(0, staticIndicatorCount);
                    items.push({
                        label: indicator.label,
                        value: indicator.value
                    });
                }
                rows = items.map(({label, value}) => ({label, value: summary[value]}));
            }
            chartData = {
                columns: ['label', 'value'],
                rows
            };
            settings = {
                dimension: ['label'],
                metrics: 'value',
                width,
                left,
                showLabelOutSide: true
            };
        } else {
            settings = {
                dataUnit: indicator && indicator.unit || '',
                dataType: indicator && indicator.dataType || {precision: 2},
                dimension: [xAxisField],
                metrics: metrics,
                width,
                left,
                showLabelOutSide: true
            };
        }
        let chartTitle = title.replace('{INDICATOR}', indicator ? indicator.label : '');
        const funnelProps = {
            title: chartTitle,
            data: chartData,
            settings,
            afterSetOptionOnce: this.handleAfterSetOptionOnce,
            // 暂时写死，需要替换
            isLoading: this.state.isLoading
        };
        if (itemStyle) {
            funnelProps.itemStyle = itemStyle;
        }
        return <Funnel {...funnelProps} />;
    }
}