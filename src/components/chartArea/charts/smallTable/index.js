/**
 * @file 报表小表格
 * <AUTHOR>
 */
import React, {PureComponent} from 'react';
import {Table, Popover} from '@baidu/one-ui';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import {isNumber, isEqual, cloneDeep, isBoolean, isNil} from 'lodash-es';
import {getChartsDataParams, getIndicatorList} from '../../../../utils/charts';
import {getApiToast} from '../../../../utils/common';
import ReportSdkChartsService from '../../../../services/ChartsServices/ReportSdkChartsService';
import {getTargetEndDate, getDiffDay} from '../../../../utils/filterArea';
import {formatText} from '../../../../utils/table';
import IconTip from '../../../custom/components/IconTip';

export default class SmallTable extends PureComponent {

    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }

    constructor(props, context) {
        super(props, context);
        this.state = {
            indicatorList: [],
            tabs: [],
            metrics: [],
            listData: [],
            summary: {},
            displayData: [],
            sortRule: {},
            // 报表公共筛选信息
            commonFilterInfo: {},
            isLoading: true,
            isTabCompare: false
        };
    }

    componentDidMount() {
        const {eventHub: {on}} = this.context;
        this.service = new ReportSdkChartsService(this.context);
        const {listData, summary, chartConfig} = this.props;
        const {importData, isRefreshOnIndicatorChange = false} = chartConfig;
        this.initTab();
        if (importData) {
            this.setState({
                summary,
                listData
            });
        }
        else {
            this.handleGetTableData({data: {}});
            on('sdk.event.chart.fetch', this.handleGetTableData);
            if (isRefreshOnIndicatorChange) {
                on('sdk.event.chart.metrics.change', this.handleGetTableData);
            }
        }
    }

    componentWillUnmount() {
        const {listData, chartConfig: {isRefreshOnIndicatorChange = false}} = this.props;
        if (!listData) {
            const {eventHub: {un}} = this.context;
            un('sdk.event.chart.fetch', this.handleGetTableData);
            if (isRefreshOnIndicatorChange) {
                un('sdk.event.chart.metrics.change', this.handleGetTableData);
            }
        }
    }

    componentDidUpdate(prevProps) {
        const {listData, summary} = this.props;
        if (listData && !isEqual(prevProps, this.props)) {
            this.setState({
                listData,
                summary
            });
            this.initTab();
        }
    }

    isCompare = () => {
        const {
            chartConfig: {
                isResponseCompare,
                isDefaultCompare
            }
        } = this.props;
        const {
            commonFilterInfo
        } = this.state;
        return (isDefaultCompare || isResponseCompare || this.state.isTabCompare)
            && !!commonFilterInfo.compareStartDate;
    }

    handleGetTableData = ({data: {commonFilterInfo, metrics}}) => {
        const {
            eventHub: {fire, trigger}
        } = this.context;
        const {
            commonFilterInfo: commonFilterInfoLast,
            chartConfig
        } = this.props;
        const filtersInfo = cloneDeep(commonFilterInfo ? commonFilterInfo : commonFilterInfoLast);
        // 过滤不必要请求
        if (!Object.keys(filtersInfo || {}).length) {
            return;
        }
        const {
            tableConfig,
            sendId = '',
            isResponseCompare,
            isResponseTimeWindow,
            rowCount,
            isDefaultCompare
        } = chartConfig;

        // 如果默认需要对比
        if ((isDefaultCompare || this.state.isTabCompare) && !filtersInfo.compareStartDate) {
            const diffDay = getDiffDay(filtersInfo.startDate, filtersInfo.endDate);
            filtersInfo.compareStartDate = getTargetEndDate(filtersInfo.startDate, -(diffDay + 1), '-');
        }
        this.setState({
            commonFilterInfo: filtersInfo,
            listData: [],
            isLoading: true
        });
        let sortRule = {};
        const {tabValue} = this.state;
        const {sortOrder = 'desc', compareField} = tableConfig[tabValue] || {};
        const isAscend = sortOrder === 'ascend';
        const {sort, filter} = compareField || {};
        const metric = (metrics && metrics.length) ? metrics[0] : this.getMetrics();
        if (!compareField) {
            sortRule.sortName = metric;
            sortRule[sortRule.sortName] = {
                isAscend
            };
        }
        if (isResponseTimeWindow && typeof isResponseTimeWindow === 'string') {
            filtersInfo.timeUnit = isResponseTimeWindow;
        }
        // 如果后端排序接口加入sortRule
        // 获取请求表头接口的入参
        const tableDataParams = getChartsDataParams({
            sortRule,
            commonFilterInfo: filtersInfo
        }, this.getRequestColumns(metric), chartConfig);
        // 如果有compareField
        if (compareField) {
            filter.forEach(v => {
                tableDataParams.filters.push({
                    column: metric,
                    ...v
                });
            });
            sort.forEach(v => {
                tableDataParams.sorts.push({
                    column: metric,
                    sortRule: isAscend ? 'ASC' : 'DESC',
                    compareField: v
                });
            });
        }
        if (!isResponseCompare && (!isDefaultCompare || !this.state.isTabCompare)) {
            if (tableDataParams.compareStartDate) {
                delete tableDataParams.compareStartDate;
            }
        }
        if (rowCount) {
            tableDataParams.rowCount = rowCount;
        }
        const requestServices = tableDataParams.compareStartDate ? 'getCompareTableData' : 'getTableData';
        // 请求表头数据
        this.service[requestServices]({
            params: tableDataParams
        }).then(({data, status}) => {
            if (status === 0) {
                fire('sdk.event.enter.log', {
                    hitType: 'small-table-data-success'
                });
                let {rows: listData, summary} = data;
                // 如果是对比
                if (this.isCompare()) {
                    listData = this.formatCompareData(listData);
                }
                this.setState({
                    listData,
                    summary
                });

                setTimeout(() => {
                    this.setData(this.state.tabValue);
                });
            }
            else {
                getApiToast({content: '获取数据失败'});
            }
        }).catch(err => {
            getApiToast();
        }).finally(data => {
            this.setState({
                isLoading: false
            });
            const {listData = []} = this.state;
            if (sendId) {
                trigger('sdk.event.chart.fetchGroup', {data: {
                    topTabKey: this.props.topTabKey,
                    commonFilterInfo: cloneDeep(commonFilterInfo ? commonFilterInfo : commonFilterInfoLast),
                    groupIds: listData.map(v => v[sendId])
                }});
            }
        });
    }

    formatCompareData(listData) {
        const {
            chartConfig
        } = this.props;
        const {indicatorList = []} = chartConfig;
        const metrics = this.getMetrics();
        return listData.map(item => {
            const result = {};
            // 将非指标列添加
            indicatorList.forEach(v => {
                result[v.value] = item.sourceInfo[v.value];
            });
            // 指标列和变化量变化率添加
            result[metrics + '1'] = item.sourceInfo[metrics];
            result[metrics + '2'] = item.targetInfo[metrics];
            result[metrics + 'ChangeValue'] = item.changeValue[metrics];
            result[metrics + 'ChangeRatio'] = item.changeRatio[metrics];

            return result;
        });
    }

    getMetrics() {
        let {
            chartConfig,
            metrics
        } = this.props;
        const {
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {defaultIndicator} = getIndicatorList({
            chartConfig,
            reportAreaConfig
        });
        metrics = (metrics && metrics.length) ? metrics : [defaultIndicator];
        metrics = metrics[0];
        return metrics;
    }

    initTab() {
        const {
            chartConfig
        } = this.props;
        const tabs = (chartConfig && chartConfig.titleArea) ? chartConfig.titleArea.tabList : [];
        const {isCompare = false} = tabs[0] || {};
        let tabValue = '';
        this.setState({
            tabs,
            isTabCompare: isCompare
        });
        setTimeout(() => {
            // 初始化选中
            if (tabs.length) {
                tabValue = this.state.tabValue ? this.state.tabValue : tabs[0].value;
                this.onTabChange(tabValue);
            }
        });
    }

    onTabChange = (value, e) => {
        const {isCompare = false} = this.state.tabs.filter(item => item.value === value)[0] || {};
        this.setState({
            tabValue: value,
            isTabCompare: isCompare
        });
        const {chartConfig: {importData = false}} =  this.props;
        if (importData) {
            this.setData(value);
        }
        else {
            setTimeout(() => {
                this.handleGetTableData({data: {}});
            });
        }
    }

    setData(value) {
        const {listData, summary} = this.state;
        const {chartConfig: {rowCount, needPercent = []}} =  this.props;
        // 计算占比
        if (!this.isCompare() && needPercent.length) {
            listData.forEach(v => {
                needPercent.forEach(key => {
                    if (summary[key]) {
                        v[key + 'percent'] = v[key] / summary[key];
                    }
                });
            });
        }
        // 截取结果集
        const displayData = rowCount ? listData.splice(0, rowCount) : listData;
        this.setState({
            displayData
        });
    }

    getRequestColumns = metric => {
        const {
            chartConfig
        } = this.props;
        // 请求的指标
        const {indicatorList = [], isSingleIndicator = false, singleIndicatorRequired = []} = chartConfig;
        const columns = isSingleIndicator
            ? [...singleIndicatorRequired, metric]
            : indicatorList.map(v => v.value);
        if (!columns.includes('date')) {
            columns.push('date');
        }
        return columns;
    }

    getColumns = () => {
        let {
            metrics,
            chartConfig
        } = this.props;
        const {indicatorList = [], needPercent = []} = chartConfig;
        metrics = this.getMetrics();
        const columns = [];
        indicatorList.forEach(v => {
            // 判断不显示，必须有isShow属性
            if (v.isShow !== false) {
                const {isPercent = false, precision = 2} = v.dataType || {};
                const row = {
                    title: v.label,
                    dataIndex: v.value,
                    align: v.align ? v.align : 'left',
                    // width: v.width ? v.width : '',
                    // minWidth: v.width ? v.width : 60,
                    draggable: true,
                    render: (text, record, index) => {
                        if (v.render) {
                            return v.render(text, record, index);
                        }
                        else if (isNumber(text)) {
                            return formatText(text, isPercent, precision, true);
                        }
                        // fix table with noData render
                        else if (isNil(text)) {
                            return '-';
                        }
                        return text;

                    }
                };
                if (v.isIndicator) {
                // 如果是指标列判断是否和当前指标相同，不同不显示
                    if (metrics === v.value) {
                        // 对比情况增加变化量和变化率
                        if (this.isCompare()) {
                            const row2 = cloneDeep(row);
                            const {
                                startDate = '',
                                endDate = '',
                                compareStartDate = ''
                            } = this.state.commonFilterInfo || {};
                            const diffDay = getDiffDay(startDate, endDate);
                            row.title = (<Popover
                                className="report-sdk-small-table-head-tip"
                                content={<div className="report-sdk-small-table-head-tip-content">
                                    {startDate.replace(/-/g, '/')}~{endDate.replace(/-/g, '/')}
                                    <div>的{row.title}</div>
                                </div>}
                                getPopupContainer={triggerNode => triggerNode.parentNode}
                            >
                                <div>{row.title + '1'}</div>
                            </Popover>);
                            row.dataIndex = row.dataIndex + '1';
                            row2.title = (<Popover
                                className="report-sdk-small-table-head-tip"
                                content={<div className="report-sdk-small-table-head-tip-content">
                                    {compareStartDate.replace(/-/g, '/')}~
                                    {getTargetEndDate(compareStartDate, diffDay, '/')}
                                    <div>的{row2.title}</div>
                                </div>}
                                getPopupContainer={triggerNode => triggerNode.parentNode}
                            >
                                <div>{row2.title + '2'}</div>
                            </Popover>);
                            row2.dataIndex = row2.dataIndex + '2';
                            row2.draggable = true;
                            columns.push(row, row2,
                                {
                                    title: v.label + '变化量',
                                    dataIndex: v.value + 'ChangeValue',
                                    draggable: true,
                                    align: 'right',
                                    render: (text, record, index) => {
                                        const result = formatText(text, isPercent, 2);
                                        return (
                                            <div
                                                className="report-sdk-small-table-unit-inline-cut"
                                                title={result}
                                            >
                                                <Popover content={result} placement="top">
                                                    {result}
                                                </Popover>
                                            </div>
                                        );
                                    }
                                },
                                {
                                    title: v.label + '变化率',
                                    dataIndex: v.value + 'ChangeRatio',
                                    draggable: true,
                                    align: 'right',
                                    render: (text, record, index) => {
                                        const result = formatText(text, true, 2);
                                        return (
                                            <div
                                                className="report-sdk-small-table-unit-inline-cut"
                                                title={result}
                                            >
                                                <Popover content={result} placement="top">
                                                    {result}
                                                </Popover>
                                            </div>
                                        );
                                    }
                                }
                            );
                        }
                        else {
                            columns.push(row);
                            // 如果需要占比
                            if (needPercent.includes(v.value)) {
                                const percentRow = {
                                    title: '占比',
                                    dataIndex: v.value + 'percent',
                                    align: 'right',
                                    width: 90,
                                    draggable: true,
                                    render: (text, record, index) => {
                                        const result = formatText(text, true, 2);
                                        return (
                                            <div
                                                className="report-sdk-small-table-unit-inline-cut"
                                                title={result}
                                            >
                                                {result}
                                            </div>
                                        );
                                    }
                                };
                                columns.push(percentRow);
                            }
                        }
                    }
                }
                else {
                    columns.push(row);
                }
            }
        });
        columns.unshift({
            title: '排名',
            dataIndex: 'rank',
            align: 'left',
            width: 60,
            draggable: true,
            render: (text, record, index) => (index + 1)
        });
        return columns;
    }

    render() {
        let {
            chartConfig
        } = this.props;
        const {
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {tabs, tabValue, displayData} = this.state;
        const columns = this.getColumns();
        const {indicatorList} = getIndicatorList({
            chartConfig,
            reportAreaConfig
        });
        const isLoading = isBoolean(this.props.isLoading) ? this.props.isLoading : this.state.isLoading;
        const indicator = indicatorList.find(v => v.value === this.getMetrics());
        const tabsRender = tabs.map(v => (
            <>
                <span
                    key={v.value}
                    onClick={e => {
                        this.onTabChange(v.value, e);
                    }}
                    className={classNames({
                        'report-sdk-small-table-tab-item': true,
                        'report-sdk-small-table-tab-item-pointer': tabs.length > 1,
                        'report-sdk-small-table-tab-item-active': tabs.length > 1 && v.value === tabValue
                    })}
                >
                    {v.label.replace('{INDICATOR}', indicator ? indicator.label : '')}
                    {
                        v.tipContent && <IconTip
                            className={'report-sdk-small-table-tab-tip'}
                            content={v.tipContent}
                        />
                    }
                </span>
            </>
        )
        );
        return (
            <div className="report-sdk-small-table">
                {
                    <div className="report-sdk-small-table-tab">
                        {tabsRender}
                    </div>
                }
                <div className="report-sdk-small-table-content">
                    <Table
                        className="report-sdk-small-table-content-table"
                        columns={columns}
                        size="small"
                        dataSource={displayData}
                        pagination={false}
                        showHeader
                        loading={isLoading}
                        type="compact"
                    />
                </div>
            </div>
        );
    }
}
