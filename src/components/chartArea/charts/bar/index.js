import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {isArray, cloneDeep} from 'lodash';
import ReportSdkChartsService from '../../../../services/ChartsServices/ReportSdkChartsService';
import {formatText} from '../../../../utils/table';
import {Bar} from '@baidu/one-charts';
import {getApiToast, getApiError} from '../../../../utils/common';
import {formatApiData, getChartsDataParams, getIndicatorList, getColumns} from '../../../../utils/charts';

export default class BarChart extends PureComponent {

    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }

    constructor(props, context) {
        super(props, context);
        const {chartConfig} = this.props;
        const {
            sortField,
            sortType
        } = chartConfig;

        this.state = {
            listData: [],
            sortRule: sortField ? {
                sortName: sortField,
                [sortField]: {
                    isAscend: sortType === 'ASC'
                }
            } : {
                sortName: 'date',
                date: {
                    isAscend: true
                }
            },
            // 报表公共筛选信息
            commonFilterInfo: {},
            isLoading: true
        };
    }

    componentDidMount() {
        const {eventHub: {on}} = this.context;
        this.service = new ReportSdkChartsService(this.context);
        const {topTabKey} = this.props;
        this.handleGetChartData({data: {topTabKey}});
        on('sdk.event.chart.fetch', this.handleGetChartData);
        on('sdk.event.chart.metricsChangeFetch', this.handleMetricsChangeGetChartData);
    }

    componentWillUnmount() {
        const {eventHub: {un}} = this.context;
        un('sdk.event.chart.fetch', this.handleGetChartData);
        un('sdk.event.chart.metricsChangeFetch', this.handleMetricsChangeGetChartData);
    }

    handleAfterSetOptionOnce = instance => {
        this.echarts = instance;
    }

    handleMetricsChangeGetChartData = arg => {
        const {
            chartConfig: {
                isRefreshOnIndicatorChange
            }
        } = this.props;
        if (!isRefreshOnIndicatorChange) {
            return;
        }
        this.handleGetChartData(arg);
    }

    handleGetChartData = ({data: {topTabKey, commonFilterInfo}}) => {
        const {
            eventHub: {fire},
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {
            topTabKey: topTabKeyLast,
            chartConfig,
            commonFilterInfo: commonFilterInfoLast,
            metrics
        } = this.props;

        const filtersInfo = cloneDeep(commonFilterInfo || commonFilterInfoLast);
        // 过滤不必要请求
        if (topTabKeyLast !== topTabKey || !Object.keys(filtersInfo || {}).length) {
            return;
        }
        const {
            isResponseCompare,
            isResponseTimeWindow,
            isRefreshOnIndicatorChange,
            sortType
        } = chartConfig;
        if (isResponseTimeWindow && typeof isResponseTimeWindow === 'string') {
            filtersInfo.timeUnit = isResponseTimeWindow;
        }
        const {defaultIndicator} = getIndicatorList({
            chartConfig,
            reportAreaConfig
        });
        const sortName = metrics.length ? metrics[0] : defaultIndicator;
        this.setState({isChartLoading: true});
        // 获取请求表头接口的入参
        const tableDataParams = getChartsDataParams({
            sortRule: isRefreshOnIndicatorChange && sortType
                ? {
                    sortName,
                    date: {
                        isAscend: sortType === 'ASC'
                    }
                }
                : this.state.sortRule,
            commonFilterInfo: filtersInfo
        }, getColumns({
            chartConfig,
            reportAreaConfig
        }), chartConfig);

        if (!isResponseCompare) {
            if (tableDataParams.compareStartDate) {
                delete tableDataParams.compareStartDate;
            }
        }
        if (isResponseTimeWindow && typeof isResponseTimeWindow === 'string') {
            tableDataParams.timeUnit = isResponseTimeWindow;
        }
        const requestServices = tableDataParams.compareStartDate ? 'getCompareTableData' : 'getTableData';
        // 请求表头数据
        this.service[requestServices]({
            params: tableDataParams
        }).then(({data, status}) => {
            if (status === 0) {
                fire('sdk.event.enter.log', {
                    hitType: 'bar-data-success'
                });
                const listData = formatApiData(data).datasource;
                this.setState({
                    totalCount: data.totalRowCount || 0,
                    listData,
                    isLoading: false
                });
            } else {
                getApiToast({content: '获取数据失败'});
            }
        }).catch(err => {
            getApiError(err);
        }).finally(data => {
            this.setState({isChartLoading: false});
        });
    }

    getRows = () => {
        const {listData} = this.state;
        const {
            chartConfig: {
                xAxisField,
                xAxisFieldFormatter
            }
        } = this.props;

        if (!(listData.length && listData[0][xAxisField] && isArray(listData[0][xAxisField]))) {
            if (typeof xAxisFieldFormatter === 'function') {
                return listData.map(row => {
                    return {
                        ...row,
                        [xAxisField]: xAxisFieldFormatter(row[xAxisField])
                    };
                });
            }
            return listData;
        }
        return listData.map(row => {
            const formatRow = {};
            Object.keys(row).forEach(indicator => {
                let label = row[indicator][0];
                if (indicator === xAxisField) {
                    label = typeof xAxisFieldFormatter === 'function'
                        ? xAxisFieldFormatter(label)
                        : label;
                }
                if (label.length > 14) {
                    label = label.slice(0, 14) + '...';
                }
                formatRow[indicator] = label;
                formatRow[`${indicator}Target`] = row[indicator][1];
                formatRow[`${indicator}Ratio`] = row[indicator][3];
            });
            return formatRow;
        });
    }

    render() {
        const {
            chartConfig,
            metrics,
            commonFilterInfo: {
                startDate,
                endDate,
                compareStartDate,
                compareEndDate
            }
        } = this.props;
        const {
            config: {
                reportArea: reportAreaConfig = {}
            }
        } = this.context;
        const {
            xAxisField,
            titleArea: {
                title
            } = {},
            legendPosition,
            isResponseCompare,
            isLegendTitleOccupyOneLine,
            dataZoomCount,
            isXAxisLabelInterval = false
        } = chartConfig;
        const isCompare = !!compareStartDate && isResponseCompare;
        const {indicatorList, defaultIndicator} = getIndicatorList({
            chartConfig,
            reportAreaConfig
        });
        const chartData = {
            columns: getColumns({
                chartConfig,
                reportAreaConfig,
                indicatorList,
                isCompare
            }),
            rows: this.getRows()
        };
        const rawLabelMap = {};
        const settings = {
            dimension: [xAxisField],
            metrics: metrics.length ? metrics : [defaultIndicator],
            labelMap: (indicatorList || []).reduce((ret, item) => {
                const {label, unit, dataType = {precision: 2}} = item;
                ret[item.value] = label;
                rawLabelMap[item.value] = {label, unit, dataType};
                if (isCompare) {
                    ret[item.value] = `${startDate}~${endDate} ${label}`;
                    ret[`${item.value}Target`] = `${compareStartDate}~${compareEndDate} ${label}`;
                }
                return ret;
            }, {})
        };
        const leftMetrics = rawLabelMap[settings.metrics[0]];
        if (leftMetrics) {
            const {dataType = {precision: 2}} = leftMetrics;
            const {isPercent, precision = 0} = dataType;
            settings.axisSite = {
                left: {
                    unit: leftMetrics.unit,
                    dataType: leftMetrics.dataType,
                    formatter: value => formatText(
                        value,
                        isPercent,
                        precision === 0 ? precision : 1,
                        true,
                        1
                    )
                }
            };
        }
        const barProps = {
            data: chartData,
            settings,
            afterSetOptionOnce: this.handleAfterSetOptionOnce,
            isLoading: this.state.isChartLoading,
            legendProps: {
                horizontalAlign: legendPosition,
                isTitleOccupyOneLine: isLegendTitleOccupyOneLine
            }
        };
        if (title) {
            if (React.isValidElement(title)) {
                barProps.title = title;
            } else {
                const indicatorInfo = indicatorList.filter(item => item.value === settings.metrics[0])[0];
                const indicator = indicatorInfo && indicatorInfo.label;
                barProps.title = `${title}${isCompare && isResponseCompare ? '对比' : ''}`;
                if (indicator) {
                    barProps.title = barProps.title.replace('{INDICATOR}', indicator);
                }
            }
        }

        if (isCompare) {
            settings.metrics = settings.metrics.reduce((ret, metric) => {
                ret.push(...[metric, `${metric}Target`]);
                return ret;
            }, []);
        }

        if (barProps.extend && barProps.extend.grid) {
            barProps.extend.grid.left = 62;
        } else {
            barProps.extend = {
                grid: {
                    left: 62
                }
            };
        }
        if (!isXAxisLabelInterval) {
            barProps.extend['xAxis.0.axisLabel.interval'] = 0;
        }
        const rows = chartData.rows;
        if (dataZoomCount && (rows && rows.length > dataZoomCount)) {
            const upperLimit = 100 * dataZoomCount / (rows.length || 1);
            barProps.extend.dataZoom = [{
                type: 'slider',
                show: true,
                brushSelect: false,
                height: 18,
                moveHandleSize: 4,
                bottom: 6,
                start: 0,
                end: upperLimit,
                maxSpan: upperLimit,
                fillerColor: 'rgba(140, 150, 178, 0.08)',
                handleStyle: {
                    color: '#C5CCDB'
                },
                dataBackground: {
                    areaStyle: {
                        color: '#EBEDF5'
                    }
                },
                borderColor: '#EBEDF5'
            }];
            barProps.extend.grid.bottom = 48;
        }
        return <Bar {...barProps} />;
    }
}