@import "../../../default.less";

@chart-map: ~"@{report-prefix-cls}-chart-map";
@chart-pie: ~"@{report-prefix-cls}-chart-pie";
@chart-table: ~"@{report-prefix-cls}-small-table";

.@{chart-map} {
    width: 100%;
    display: flex;

    &-area {
        font-size: @dls-font-size-0;
        color: @dls-color-gray-9;
    }

    &-back {
        font-size: @dls-font-size-0;
        color: @dls-color-brand;
        cursor: pointer;
    }

    &-map {
        width: 50%;
    }

    &-table {
        width: 50%;
    }
}

.@{chart-pie} {
    &-bottom-text {
        text-align: center;
        font-size: @dls-font-size-0;
    }
}

.@{chart-table} {
    .one-table-content {
        max-height: @dls-padding-unit * 75;
    }

    &-tab {
        margin-bottom: @dls-padding-unit * 4.5;
        &-tip {
            font-size: @dls-font-size-1;
            margin-left: @dls-padding-unit * 2;
        }
        &-item {
            display: inline-block;
            font-size: @dls-font-size-1;
            color: @dls-color-gray-9;
            letter-spacing: 0;
            text-align: center;
            padding: 0 @dls-padding-unit * 5;
            line-height: @dls-padding-unit * 5;
            height: @dls-font-size-1;

            &:not(:last-child) {
                border-right: 1px solid @dls-color-gray-4;
            }

            &:first-child {
                padding-left: 0;
            }

            &-active {
                color: @dls-color-brand-7;
            }

            &-pointer {
                cursor: pointer;
            }
        }
    }

    &-unit-inline-cut {
        max-width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    &-head-tip {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        padding: 8px;
        box-sizing: border-box;
        &-content {
            width: 168px;
        }
    }
}