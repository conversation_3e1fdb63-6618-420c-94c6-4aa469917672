import React from 'react';
import PropTypes from 'prop-types';
import * as charts from './charts';
import IndicatorSelector from '../indicatorSelector';

export default class ChartArea extends React.Component {

    state = {
        metrics: {},
        commonFilterInfo: {}
    }
    static contextTypes = {
        eventHub: PropTypes.object.isRequired
    }
    componentDidMount() {
        const {eventHub: {on}} = this.context;
        // 指标改变
        on('sdk.event.chart.metrics.change', this.handleMetricsChange);
        // 同步外部公共筛选条件
        on('sdk.event.table.changeCommonFilter', this.handleChangeCommonFilter);
    }

    handleMetricsChange = ({data: {metrics, eventKey}}) => {
        this.setState({metrics: {
            ...this.state.metrics,
            [eventKey]: metrics
        }}, () => {
            const {eventHub: {trigger}} = this.context;
            trigger('sdk.event.chart.metricsChangeFetch', {data: {
                topTabKey: this.props.topTabKey,
                commonFilterInfo: this.state.commonFilterInfo
            }});
        });
    }

    handleChangeCommonFilter = ({data}) => {
        const {eventHub: {trigger}} = this.context;
        const commonFilterInfo = data;
        this.setState({
            commonFilterInfo
        }, () => {
            trigger('sdk.event.chart.fetch', {data: {
                topTabKey: this.props.topTabKey,
                commonFilterInfo
            }});
        });
    }

    render() {
        const {
            className,
            mainAreaConfig,
            topTabKey,
            chartAreaVisible
        } = this.props;
        const {
            metrics,
            commonFilterInfo
        } = this.state;
        const {
            chartAreaList = []
        } = mainAreaConfig;
        const classNames = `${className}-chart-area`;
        const chartAreaVisibleCls = (!chartAreaVisible || !chartAreaList.length) ? `${classNames}-hidden` : '';
        return (<div className={`${classNames} ${chartAreaVisibleCls}`}>
            {chartAreaList.map((chartConfig, idx) => {
                const {
                    chartType,
                    isShowIndicator,
                    indicatorUiType,
                    isCompareIndicator,
                    indicatorList,
                    defaultIndicator,
                    checkIndicatorButtonShow
                } = chartConfig;
                const Component = charts[chartType.replace(/^\w/, s => s.toUpperCase())];
                const chartKey = topTabKey + chartType + idx;
                const displayMetrics = metrics[`${topTabKey}-${chartKey}`] || metrics['common-common'] || [];
                const isFilterCheckButton = (typeof checkIndicatorButtonShow === 'function'
                    ? checkIndicatorButtonShow(commonFilterInfo)
                    : false);
                const chartProps = {
                    className: classNames,
                    topTabKey,
                    chartKey,
                    mainAreaConfig,
                    chartConfig,
                    metrics: displayMetrics,
                    commonFilterInfo,
                    isFilterCheckButton
                };
                const indicatorSelectorProps = {
                    className: classNames,
                    topTabKey,
                    chartKey,
                    indicatorList,
                    isIndicatorCompare: isCompareIndicator,
                    defaultIndicator,
                    metrics: displayMetrics,
                    isFilterCheckButton,
                    commonFilterInfo
                };
                return (
                    <div
                        key={chartKey}
                        className={`${classNames}-item`}
                        style={{width: (100 / chartAreaList.length + '%')}}
                    >
                        {isShowIndicator
                            && indicatorUiType === 'dropdown'
                            && <IndicatorSelector {...indicatorSelectorProps} />
                        }
                        <Component {...chartProps} />
                    </div>
                );
            })}
        </div>);
    }
}