import React from 'react';
import classNames from 'classnames';
import PropTypes from 'prop-types';
import {partial, cloneDeep, isArray} from 'lodash';
import {Loading} from '@baidu/one-ui';
import {IconCaretDown, IconCaretUp} from 'dls-icons-react';
import {getApiToast} from '../../../utils/common';
import {formatText} from '../../../utils/table';
import ReportSdkChartsService from '../../../services/ChartsServices/ReportSdkChartsService';
import {formatApiData, getChartsDataParams, getColumns} from '../../../utils/charts';
import IconTip from '../../custom/components/IconTip';

export default class IndicatorButton extends React.Component {

    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }

    constructor(props, context) {
        super(props, context);
        const {isResponseIndicatorButtonChange, defaultIndicator} = props;
        this.state = {
            indicator: isResponseIndicatorButtonChange ? defaultIndicator : '',
            totalCount: 0,
            summary: {},
            isLoading: true
        };
    }

    componentDidMount() {
        const {eventHub: {on}} = this.context;
        this.service = new ReportSdkChartsService(this.context);
        const {topTabKey} = this.props;
        this.handleGetOverviewData({data: {topTabKey}});
        on('sdk.event.chart.fetch', this.handleGetOverviewData);
    }

    componentWillUnmount() {
        const {eventHub: {un}} = this.context;
        un('sdk.event.chart.fetch', this.handleGetOverviewData);
    }

    handleGetOverviewData = ({data: {topTabKey, commonFilterInfo}}) => {
        const {
            isResponseIndicatorButtonChange,
            isButtonResponseCompare
        } = this.props;
        if (isResponseIndicatorButtonChange) {
            return;
        }
        const {
            eventHub: {fire},
            config: {
                reportArea: reportAreaConfig = {},
                filterAreaConfig = {}
            }
        } = this.context;
        const {
            filterData: {
                timeUnit = {}
            } = {}
        } = filterAreaConfig;
        const option = timeUnit.option || [];
        const summaryItem = option.filter(item => item.label === '合计')[0] || {};
        const {
            topTabKey: topTabKeyLast,
            commonFilterInfo: commonFilterInfoLast,
            chartConfig,
            indicatorList
        } = this.props;
        const filtersInfo = cloneDeep(commonFilterInfo || commonFilterInfoLast);
        // 过滤不必要请求
        if (topTabKeyLast !== topTabKey || !Object.keys(filtersInfo || {}).length) {
            return;
        }
        filtersInfo.timeUnit = summaryItem.value;
        this.setState({isLoading: true});
        const indicatorListFilter = (typeof indicatorList === 'function') ? indicatorList() : indicatorList;
        // 获取请求表头接口的入参
        const tableDataParams = getChartsDataParams({
            sortRule: this.state.sortRule,
            commonFilterInfo: filtersInfo
        }, getColumns({
            chartConfig,
            reportAreaConfig,
            commonFilterInfo: filtersInfo,
            indicatorList: indicatorListFilter.filter(item => item.value !== 'totalRowCount')
        }), chartConfig, true);
        if (isButtonResponseCompare === false) {
            if (tableDataParams.compareStartDate) {
                delete tableDataParams.compareStartDate;
            }
        }
        const requestServices = tableDataParams.compareStartDate ? 'getCompareTableData' : 'getTableData';
        // 请求表头数据
        this.service[requestServices]({
            params: tableDataParams
        }).then(({data, status}) => {
            if (status === 0) {
                fire('sdk.event.enter.log', {
                    hitType: 'indicator-btn-data-success'
                });
                const summary = formatApiData(data).summary;
                const isCompare = this.isCompareData(summary);
                const totalRowCount = isCompare ? [data.totalRowCount] : data.totalRowCount;
                this.setState({
                    summary: {...(summary || {}), totalRowCount: totalRowCount || '加载失败'},
                    isLoading: false
                });
            }
            else {
                getApiToast({content: '获取数据失败'});
            }
        }).catch(err => {
            getApiToast();
        }).finally(data => {
            this.setState({isLoading: false});
        });
    }

    onChange = value => {
        this.setState({indicator: value});
        this.handleMetricsChange([value]);
    }

    handleMetricsChange = metrics => {
        const {topTabKey, chartKey} = this.props;
        const {eventHub: {trigger}} = this.context;
        trigger('sdk.event.chart.metrics.change', {data: {
            eventKey: `${topTabKey}-${chartKey}`,
            metrics
        }});
    }

    isCompareData = summary => {
        const indicator = Object.keys(summary)[0];
        return indicator && isArray(summary[indicator]);
    }

    render() {
        const {
            className,
            indicatorList,
            isSingleIndicatorButton,
            isResponseIndicatorButtonChange,
            metrics: metricsLast,
            isCompare = false,
            isChartLoading = true
        } = this.props;
        const classnames = `${className}-indicator-button`;
        const {indicator, isLoading} = this.state;
        const displayIndicatorList = (typeof indicatorList === 'function') ? indicatorList() : indicatorList;
        let summary = this.state.summary;
        if (isResponseIndicatorButtonChange) {
            summary = this.props.summary || {};
        }
        return (
            <div className={`${classnames}-container-loading`}>
                <Loading
                    type="normal"
                    loading={isResponseIndicatorButtonChange ? isChartLoading : isLoading}
                    tip="正在加载中..."
                >
                    <div className={`${classnames}-container`}>
                        {displayIndicatorList.map(({btnLabel, label, value, unit, display, dataType, tip}) => {
                            if (display === false) {
                                return;
                            }
                            let isActive = indicator === value;
                            if (metricsLast.length) {
                                isActive = metricsLast.includes(value);
                            }
                            const activeAble = isResponseIndicatorButtonChange && isActive;
                            const itemClassNames = classNames(`${classnames}-container-item`, {
                                [`${classnames}-container-item-active`]: activeAble,
                                [`${classnames}-container-item-actionable`]: isResponseIndicatorButtonChange
                            });
                            const labelText = classNames(`${classnames}-container-item-label-text`, {
                                [`${classnames}-container-item-label-text-active`]: activeAble
                            });
                            const valueText = classNames(`${classnames}-container-item-value-text`, {
                                [`${classnames}-container-item-value-text-active`]: activeAble
                            });
                            const itemProps = {
                                className: itemClassNames,
                                ...(isResponseIndicatorButtonChange
                                    ? {onClick: partial(this.onChange, value)}
                                    : {})
                            };
                            const summaryValue = summary[value];
                            const ratioValue = isResponseIndicatorButtonChange && summaryValue && summaryValue[3];

                            const ratioDisplay = (ratioValue || ratioValue === 0) ? formatText(ratioValue, true) : '-';
                            const fieldValue = this.isCompareData(summary)
                                ? summaryValue && summaryValue[0]
                                : summaryValue;
                            const isPercentage = unit === '%';
                            const {precision} = dataType || {};
                            const valueDisplay = (fieldValue || fieldValue === 0)
                                ? formatText(fieldValue, isPercentage, precision !== undefined ? precision : 2, true)
                                : '-';
                            const isDisplayUnit = valueDisplay !== '-'
                                && !(valueDisplay || '').includes('万');

                            let ratioStatus = 'normal';
                            if (ratioDisplay !== '-') {
                                if (ratioValue && ratioValue > 0) {
                                    ratioStatus = 'raise';
                                }
                                else if (ratioValue && ratioValue < 0) {
                                    ratioStatus = 'down';
                                }
                            }
                            const ratioClassName = classNames(
                                `${classnames}-container-item-ratio-container-value-ratio`,
                                `${classnames}-container-item-ratio-container-value-ratio-${ratioStatus}`,
                                {
                                    [`${classnames}-container-item-ratio-container-value-ratio-active`]: activeAble
                                }
                            );
                            const ratioLabelClassName = classNames(`${classnames}-container-item-ratio-container-label`,
                                {
                                    [`${classnames}-container-item-ratio-container-label-active`]: activeAble
                                }
                            );
                            const iconClassName = `${classnames}-container-item-ratio-container-icon`;
                            if (tip && typeof tip === 'string') {
                                tip = <IconTip content={tip} />;
                            }
                            return (
                                <div {...itemProps} key={value}>
                                    <span className={labelText}>
                                        {btnLabel || label}{isDisplayUnit && !isPercentage && `(${unit})`}
                                    </span>
                                    <span className={valueText}>
                                        {valueDisplay}
                                        <span className="tip">{tip}</span>
                                    </span>
                                    <div className={`${classnames}-container-item-ratio-container`}>
                                        {!isSingleIndicatorButton && (
                                            <span className={ratioLabelClassName}>
                                                {isCompare ? '对比' : '环比'}
                                            </span>
                                        )}
                                        {
                                            !isSingleIndicatorButton && (
                                                <span className={ratioClassName}>
                                                    {
                                                        ratioStatus !== 'normal'
                                                            ? ratioStatus === 'raise'
                                                                ? <IconCaretUp className={iconClassName} />
                                                                : <IconCaretDown className={iconClassName} /> : null
                                                    }{ratioDisplay}
                                                </span>
                                            )
                                        }
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </Loading>
            </div>
        );
    }
}