@import "../../../default.less";
@indicator-button: ~"@{report-sdk-container}-main-area-chart-area-indicator-button";

.@{indicator-button}-container {
    display: flex;
    justify-content: space-between;
    overflow-x: auto;
    padding-bottom: @dls-padding-unit * 3;
    & >:first-child {
        margin-left: 0;
    }
    &-item {
        flex: 1;
        min-width: @dls-padding-unit * 49;
        height: @dls-padding-unit * 27;
        border-radius: @dls-border-radius-3;
        margin-left: @dls-padding-unit * 3;
        padding: @dls-padding-unit * 4 @dls-padding-unit * 3;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        background-color: @dls-color-gray-1;

        @media screen {
            @media (min-width: 1440px) {
                padding: @dls-padding-unit * 4 @dls-padding-unit * 5;
            }
        }
        &-label-text {
            font-size: @dls-font-size-0;
            color: @dls-color-gray-7;
            margin-bottom: @dls-padding-unit;
            line-height: 16px;
        }
        &-label-text-active {
            color: @dls-color-brand-0;
        }
        &-value-text {
            font-size: @dls-font-size-3;
            color: @dls-color-gray-9;
            margin-bottom: @dls-padding-unit*3;
            font-weight: 500;
            line-height: 28px;
            display: flex;
            align-items: center;
            .tip {
                font-size: 14px;
                margin-left: @dls-padding-unit;
            }
        }
        &-value-text-active {
            color: @dls-color-brand-0;
            .tip .dls-icon {
                color: @dls-color-brand-0;
            }
            .tip .dls-icon:hover {
                color: @dls-color-brand-0;
            }
        }
        &-ratio-container {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            line-height: 16px;
            &-label {
                font-size: @dls-font-size-0;
                color: @dls-color-gray-7;
                margin-right: @dls-padding-unit*2;
            }
            &-label-active {
                color: @dls-color-brand-0;
            }
            &-value-ratio {
                font-size: @dls-font-size-0;
            }
            &-value-ratio-raise {
                color: @dls-color-error-7;
            }
            &-value-ratio-down {
                color: @dls-color-success-7;
            }
            &-value-ratio-active {
                color: @dls-color-brand-0;
            }
            &-icon {
                margin-right: @dls-padding-unit;
            }
        }
        &-active {
            background:
            linear-gradient(
                308.64deg,
                #4d67ff 4.63%,
                #4d98ff 95.71%
            );
        }
        &-actionable {
            cursor: pointer;
        }
    }
    &-loading {
        margin-bottom: @dls-padding-unit * 4;
        .one-loading-container {
            &:after {
                background: #fff;
            }
        }
        .one-loading-blur {
            &:after {
                opacity: 0.8;
            }
        }
    }
}
