/**
 * @file sdk入口 - 组件
 * <AUTHOR>
 * @date 2020/06/23
 */
import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import {isArray, isEmpty} from 'lodash';
import pick from 'lodash/pick';
import {Tabs} from '@baidu/one-ui';
import {notSupportCompareUnitDefalutValue} from '../config/common';
import ReportSdkTableService from '../services/TableServices/ReportSdkTableService';
import MainArea from './mainArea';
import FilterArea from './filterArea';
import SmartQueryArea from './smartQueryArea';
import {getDefaultFilterData, getInitPageEditingFilterData, getPageFilterData} from '../utils/filterArea';
import {getApiToast} from '../utils/common';
import IndicatorSelector from './indicatorSelector';

const TabPane = Tabs.TabPane;

const childContextTypesScheme = {
    eventHub: PropTypes.shape({
        on: PropTypes.func.isRequired,
        once: PropTypes.func.isRequired,
        un: PropTypes.func.isRequired,
        fire: PropTypes.func.isRequired,
        trigger: PropTypes.func.isRequired,
        destroyEvents: PropTypes.func.isRequired
    }).isRequired,
    config: PropTypes.object.isRequired,
    token: PropTypes.string.isRequired,
    hairuo: PropTypes.object.isRequired
};

export default class ReportSdk extends PureComponent {
    constructor(props) {
        super(props);
        const {config, eventHub, hairuo} = props;
        this.eventHub = eventHub;
        const {
            filterAreaConfig,
            reportArea: {
                defaultShowCustomDash = true
            } = {}
        } = config;
        // 页面筛选条件初始化
        const pageFilterData = getDefaultFilterData(filterAreaConfig);
        const initPageEditingFilterData = getInitPageEditingFilterData(filterAreaConfig);
        this.state = {
            topTabKey: '0',
            pageFilterData,
            pageEditingFilterData: initPageEditingFilterData,
            isMcc: false,
            userIds: [String(hairuo.userId)],
            mccData: [],
            dateData: {},
            chartAreaVisible: true,
            mainTableComparable: false,
            isTableIndicatorChange: false,
            mccStatus: false,
            customDashClosed: !defaultShowCustomDash
        };
    }
    static propTypes = {
        /** 用户可自定义class */
        className: PropTypes.string,
        ...childContextTypesScheme
    };

    static defaultProps = {
        className: 'report-sdk'
    };

    static childContextTypes = {
        ...childContextTypesScheme
    }

    componentDidMount() {
        const {on, trigger} = this.eventHub;
        // 请求多账户数据
        const {config, token, hairuo} = this.props;
        const {isNeedMcc, filterAreaConfig, mccInfo} = config;
        const {initialFilterData} = filterAreaConfig;
        const initialSelectedCount = mccInfo && mccInfo.initialSelectedCount;
        // 计划、单元、创意等初始化数据
        if (initialFilterData) {
            this.handleCustomPageFilterConfirm(initialFilterData, true);
        }
        this.service = new ReportSdkTableService({config, token, hairuo});
        if (isNeedMcc) {
            this.service.getMccInfo({
                params: {}
            }).then(({data}) => {
                const {isMcc, userList} = data;
                if (initialSelectedCount) {
                    trigger('sdk.event.table.getMetaData');
                }
                this.setState({isMcc, mccData: userList});
            }).catch(() => {
                getApiToast();
            });
            // 多账户确定事件
            on('sdk.event.mcc.confirm', this.handleMccChange);
        }

        on('sdk.event.table.inited', this.handleAllPageFilterData);
        on('sdk.event.page.filter.changed', this.handlePageFilterChanged);
        on('sdk.event.page.filter.changing', this.handlePageFilterChanging);
        on('sdk.event.page.filter.clear', this.handlePageFilterClear);
        // 监听更多筛选-自定义筛选器的确定按钮事件
        on('sdk.event.page.filter.custom.confirm', this.handleCustomPageFilterConfirm);
        // 页面右上角时间窗change事件
        on('sdk.event.date.change', this.handleDateChange);
        // 监听更多筛选的还原按钮点击事件
        on('sdk.event.page.filter.clearAll', this.handlePageFilterClearAll);
        // 监听更新数据按钮点击事件,刷新页面数据
        // on('sdk.event.filter.refresh.time', this.handleAllPageFilterData);
        // 刷新页面数据
        on('sdk.event.page.refresh', this.handleAllPageFilterData);
        // 监听表格左上方指标change事件
        on('sdk.event.table.indicator.change', this.handleTableIndicatorChange);
        // after init事件，用于告诉外部可以触发事件
        trigger('sdk.event.enter.inited');
        // 自定义数据概览区状态
        on('sdk.event.page.customDash.change', this.handleCustomDashChange);
        // 监听切换tab
        on('sdk.event.tab.change', this.handleTopTabsChange);
    }

    getChildContext() {
        const {config, token, hairuo} = this.props;
        return {
            eventHub: this.eventHub,
            config,
            token,
            hairuo
        };
    }

    handleTableIndicatorChange = () => {
        this.setState({isTableIndicatorChange: true});
    }

    handlePageFilterChanged = ({data}) => {
        const {pageFilterData} = this.state;
        const newData = {
            ...pageFilterData,
            ...data
        };
        this.setState({pageFilterData: newData}, () => {
            this.handleAllPageFilterData();
        });
    }

    handleAllPageFilterData = () => {
        const {trigger} = this.eventHub;
        const newAllData = getPageFilterData(this.state);
        const {isTableIndicatorChange} = this.state;
        if (isTableIndicatorChange) {
            trigger('sdk.event.table.tableIndicatorChangeCommonFilter', {data: newAllData});
            this.setState({isTableIndicatorChange: false});
        } else {
            trigger('sdk.event.table.changeCommonFilter', {data: newAllData});
        }
    }
    handleCustomDashChange = ({data: {customDashClosed} = {}}) => {
        this.setState({customDashClosed});
    }
    handlePageFilterChanging = ({data}) => {
        const {pageEditingFilterData} = this.state;
        const newData = {
            ...pageEditingFilterData,
            ...data
        };
        this.setState({pageEditingFilterData: newData});
    }

    handlePageFilterClear = ({data}) => {
        const {
            pageEditingFilterData,
            pageFilterData
        } = data;
        this.setState({
            pageEditingFilterData,
            pageFilterData
        }, () => {
            this.handleAllPageFilterData();
        });
    }

    handlePageFilterClearAll = () => {
        const {filterAreaConfig} = this.props.config;
        const {moreList} = filterAreaConfig;
        const {
            pageEditingFilterData,
            pageFilterData
        } = this.state;
        const remainKeys = Object.keys(pageFilterData).filter(key => {
            const targetKey = this.state[key] || key;
            return moreList.indexOf(targetKey) === -1;
        }) || [];
        const newPageFilterData = remainKeys.reduce((memo, item) => {
            memo[item] = pageFilterData[item];
            return memo;
        }, {});
        const newPageEditingFilterData = remainKeys.reduce((memo, item) => {
            memo[item] = pageEditingFilterData[item];
            return memo;
        }, {});
        this.setState({
            pageEditingFilterData: newPageEditingFilterData,
            pageFilterData: newPageFilterData
        }, () => {
            this.handleAllPageFilterData();
        });

    }

    onTopTabsChange = activeKey => {
        this.setState({topTabKey: activeKey});
    }

    handleTopTabsChange = ({data: tabKey}) => {
        this.onTopTabsChange(tabKey);
    }

    handleCustomPageFilterConfirm = ({data = {}}, isInit = false) => {
        const {pageFilterData} = this.state;
        let newData = {};
        const levelMap = {};
        const getItemNewData = dataItem => {
            const {key, level, customFilterData} = dataItem;
            const labels = [];
            const values = [];
            customFilterData && customFilterData.map(item => {
                labels.push(item.label);
                values.push(item.value);
            });
            if (level) {
                levelMap[level] = key;
            }
            return {
                [key]: {labels, values, operator: 'IN', level}
            };
        };
        if (isArray(data)) {
            for (let i = 0; i < data.length; i++) {
                const dataItem = data[i];
                const newDataItem = getItemNewData(dataItem) || {};
                newData = {
                    ...newData,
                    ...newDataItem
                };
            }
        }
        else {
            newData = getItemNewData(data) || {};
        }
        const newState = {
            pageFilterData: {
                ...pageFilterData,
                ...newData
            },
            ...levelMap
        };
        this.setState(newState, () => {
            !isInit && this.handleAllPageFilterData();
        });
    }

    handleMccChange = ({data = [], needGetTableData = true}) => {
        const {pageFilterData, pageEditingFilterData, isMcc} = this.state;
        const {isShowMultipleAccountCharts} = this.props.config;
        const newUserIds = data && data.length ? data.map(d => String(d)) : [String(this.props.hairuo.userId)];
        const newState = {
            userIds: newUserIds
        };
        // 如果是多账户，则根据配置项clearFilterListOnAccountChange 清除已经选中的查询范围数据
        // 默认的组件非受控，暂时不支持默认组件清除筛选
        if (isMcc) {
            const {mccInfo, filterAreaConfig} = this.props.config;
            // const defaultPageFilterData = getDefaultFilterData(filterAreaConfig);
            const initPageEditingFilterData = getInitPageEditingFilterData(filterAreaConfig);


            const clearFilterListOnAccountChange = mccInfo?.clearFilterListOnAccountChange || [];

            let changedPageFilterData = {};
            let changedPageEditingFilterData = {};
            // 切换mcc账号时，需要重置clearFilterListOnAccountChange列表中的filter到初始值, 首次加载不做处理
            if (needGetTableData) {
                // mcc默认清除的不支持的fields
                const mccNotSupportFields = ['queryScope'];
                [...clearFilterListOnAccountChange, ...mccNotSupportFields].map(field => {
                    // 默认的组件非受控，暂时不支持默认组件清除筛选
                    // if (field && defaultPageFilterData[field]) {
                    //     changedPageFilterData[field] = defaultPageFilterData[field];
                    // }
                    if (field && initPageEditingFilterData[field]) {
                        changedPageFilterData[field] = initPageEditingFilterData[field];
                        changedPageEditingFilterData[field] = initPageEditingFilterData[field];
                    }
                });
            }
            newState.pageFilterData = {...pageFilterData, ...changedPageFilterData};
            newState.pageEditingFilterData = {...pageEditingFilterData, ...changedPageEditingFilterData};
        }
        if (!isShowMultipleAccountCharts && data && data.length > 1) {
            newState.mccStatus = true;
        }
        else {
            newState.mccStatus = false;
        }
        this.setState(newState, () => {
            needGetTableData && this.handleAllPageFilterData();
        });
    }

    handleDateChange = ({data}) => {
        const {checked} = data;
        const {pageFilterData} = this.state;
        const {
            notSupportCompareUnits
        } = this.props.config.filterAreaConfig.filterData.timeUnit;
        const timeUnit = pageFilterData && pageFilterData.timeUnit;
        let newState = {
            dateData: data
        };
        if (checked && timeUnit && (notSupportCompareUnits.indexOf(timeUnit) > -1)) {
            newState = {
                dateData: data,
                pageFilterData: {
                    ...pageFilterData,
                    timeUnit: notSupportCompareUnitDefalutValue
                }
            };
        }
        this.setState(newState, () => {
            this.handleAllPageFilterData();
        });
    }

    initDateAndCompare = data => {
        this.setState({
            dateData: data
        });
    }

    handleToggleCharts = visible => {
        this.setState({
            chartAreaVisible: visible
        });
    }

    getMainTableComparable = mainTableComparable => {
        this.setState({
            mainTableComparable
        });
    }

    onClick = e => {
        if (this.props.config && this.props.config.needStopPropagation) {
            e.stopPropagation();
        }
    }

    render() {
        const {
            className,
            config
        } = this.props;
        const {
            topTabKey,
            pageEditingFilterData,
            pageFilterData,
            isMcc,
            mccStatus,
            mccData,
            chartAreaVisible,
            mainTableComparable,
            customDashClosed
        } = this.state;
        const smartQueryArea = config.smartQueryAreaConfig || {};
        const isShowSmartQuery = !isEmpty(smartQueryArea);
        const reportArea = config.reportArea;
        const {
            tabs,
            isCommonTable,
            titleArea: {
                title,
                tip,
                tipMccEnable = false
            } = {},
            customDashRender
        } = reportArea;
        const topTabs = tabs || [];
        const isHiddenTab = isCommonTable && (!chartAreaVisible || mccStatus);
        const classes = `${className}-container`;
        const topTabsProps = {
            className: classNames(`${classes}-top-tab`, {
                [`${classes}-top-tab-hidden`]: isHiddenTab
            }),
            activeKey: topTabKey,
            onChange: this.onTopTabsChange
        };
        const mainAreaConfig = topTabs[topTabKey] || {};
        const isUseCommonFilter = config.isUseCommonFilter;
        const mainAreaProps = {
            isMcc,
            topTabKey,
            className: classes,
            mainAreaConfig,
            mccStatus,
            handleToggleCharts: this.handleToggleCharts,
            getMainTableComparable: this.getMainTableComparable,
            initDateAndCompare: this.initDateAndCompare,
            mainTableComparable
        };
        const filterAreaProps = {
            pageEditingFilterData,
            pageFilterData,
            isMcc,
            mccData,
            initDateAndCompare: this.initDateAndCompare,
            mainTableComparable
        };
        const isCustomDash = typeof customDashRender === 'function' && !customDashClosed;
        const containerClassNames = classNames(classes, {
            [`${classes}-no-tab`]: topTabs.length < 2,
            [`${classes}-no-common-filter`]: isUseCommonFilter === false,
            [`${classes}-hidden-chart-area`]: isHiddenTab,
            [`${classes}-custom-dash`]: isCustomDash
        });
        const {
            isCommonIndicator = false,
            indicatorList = [],
            isIndicatorCompare = false,
            defaultIndicator = indicatorList[0] && indicatorList[0].value || ''
        } = reportArea;
        const isMulitTab = topTabs && topTabs.length > 1;
        const indicatorSelectorProps = {
            className: classes,
            isCommon: true,
            topTabKey,
            indicatorList,
            isIndicatorCompare,
            defaultIndicator,
            isMulitTab,
            commonFilterInfo: getPageFilterData(this.state)
        };

        const titleBoxClassNames = classNames(`${classes}-title-box`, {
            [`${classes}-title-box-common-indicator`]: isCommonIndicator
        });

        const titleAreaContainerCls = (!chartAreaVisible || (mccStatus && !tipMccEnable))
            && `${classes}-title-area-container-hidden` || '';
        return (
            <div className={className} onClick={this.onClick}>
                {isUseCommonFilter !== false && <FilterArea {...filterAreaProps} />}
                {isShowSmartQuery && <SmartQueryArea {...smartQueryArea} />}
                {isCustomDash && customDashRender({pageFilterData})}
                <div className={containerClassNames}>
                    <div className={titleAreaContainerCls}>
                        {!isMulitTab && title
                            ? <div className={`${classes}-title-area`}>
                                <div className={`${classes}-title-area-title`}>{title}</div>
                                <div className={`${classes}-title-tip`}>{tip}</div>
                            </div>
                            : <div className={titleBoxClassNames}>
                                <div className={`${classes}-title-tip`}>{tip}</div>
                            </div>
                        }
                        {isCommonIndicator && <IndicatorSelector {...indicatorSelectorProps} />}
                    </div>
                    {
                        isMulitTab && (
                            <Tabs {...topTabsProps}>
                                {
                                    topTabs.map((tab, index) => <TabPane tab={tab.tabLabel} key={index} />)
                                }
                            </Tabs>
                        )
                    }
                    {topTabs.length ? <MainArea {...mainAreaProps} /> : '请检查配置～'}
                </div>
            </div>
        );
    }
}
