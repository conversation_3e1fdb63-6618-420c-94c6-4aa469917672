import React, {PureComponent} from 'react';
import PropTypes from 'prop-types';
import {Select, Tooltip} from '@baidu/one-ui';
import {getTargetDate, getDiffDay, getTargetEndDate} from '../../utils/filterArea';

const Option = Select.Option;

export default class DateSelector extends PureComponent {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }
    constructor(props, context) {
        super(props);
        const {
            defaultDateSelectorValue
        } = context.config.filterAreaConfig;
        this.state = {
            dataRangeIndex: '' + defaultDateSelectorValue,
            startDate: '',
            endDate: '',
            compareStartDate: '',
            compareEndDate: ''
        };
    }
    componentDidMount() {
        // 初始化时设置条件
        this.dateChange();
    }
    handleChange = value => {
        this.setState({
            dataRangeIndex: value
        });
        this.dateChange(value);
    }
    dateChange = dataRangeIndex => {
        const {eventHub} = this.context;
        const index = dataRangeIndex || this.state.dataRangeIndex;
        const {
            dateSelectShortCuts,
            isDateSelectorCompare
        } = this.context.config.filterAreaConfig;
        const dataRangeInfo = dateSelectShortCuts[index] || {};
        const startDate = getTargetDate(-dataRangeInfo.from);
        const endDate = getTargetDate(-dataRangeInfo.to);

        const diffDay = getDiffDay(startDate, endDate);
        const compareStartDate = getTargetEndDate(startDate, -(diffDay + 1));
        const compareEndDate = getTargetEndDate(compareStartDate, diffDay);

        let initData = {
            date: [startDate, endDate]
        };
        if (isDateSelectorCompare === true) {
            initData = {
                ...initData,
                checked: true,
                compareDate: [compareStartDate, compareEndDate]
            };
        }
        if (dataRangeIndex === undefined) {
            this.props.initDateAndCompare(initData);
        } else {
            eventHub.trigger('sdk.event.globalDateSelector.change', {
                data: {
                    defaultDateSelectorValue: dataRangeIndex
                }
            });
        }
        eventHub.trigger('sdk.event.date.change', {
            data: initData
        });
        this.setState({
            startDate,
            endDate,
            compareStartDate,
            compareEndDate,
            isOneDay: (dataRangeInfo.from - dataRangeInfo.to) === 0
        });
    }
    render() {
        const classPrefix = this.props.classPrefix;
        const {
            dataRangeIndex,
            startDate,
            endDate,
            compareStartDate,
            compareEndDate,
            isOneDay
        } = this.state;
        const {
            dateSelectShortCuts,
            isDateSelectorCompare
        } = this.context.config.filterAreaConfig;
        const dateStrStart = isOneDay
            ? `${startDate}`
            : `${startDate}~${endDate}`;
        let dateStrEnd = '';
        if (isDateSelectorCompare === true) {
            dateStrEnd = isOneDay
                ? `${compareStartDate}`
                : `${compareStartDate}~${compareEndDate}`;
        }
        const contentCls = `${classPrefix}-date-selector-content`;
        const contentTextCls = `${classPrefix}-date-selector-content-text`;
        return (<div className={contentCls}>
            <Tooltip
                title={
                    <div className={contentTextCls}>
                        {dateStrStart}
                        {isDateSelectorCompare && ' 对比'}
                        {isDateSelectorCompare && <div>
                            {dateStrEnd}
                        </div>}
                    </div>
                }
            >
                <Select
                    width={128}
                    defaultValue={dataRangeIndex}
                    onChange={this.handleChange}
                >
                    {dateSelectShortCuts.map(({label}, index) => {
                        return <Option key={index} value={'' + index}>{label}</Option>;
                    })}
                </Select>
            </Tooltip>
        </div>);
    }
}
