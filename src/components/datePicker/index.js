/**
 * @file sdk - 多账户
 * <AUTHOR>
 * @date 2020/08/19
 */
import React, {PureComponent, Fragment} from 'react';
import PropTypes from 'prop-types';
import {DatePicker, Checkbox, Switch, Tooltip} from '@baidu/one-ui';
import {IconInfoCircle} from 'dls-icons-react';
import DateSelector from './dataSelector';
import {getTargetDate, getTargetEndDate, getDiffDay, getIsValidRange} from '../../utils/filterArea';

const {RangePicker} = DatePicker;
const defaultRange = 7;

export default class SdkDatePicker extends PureComponent {
    static contextTypes = {
        hairuo: PropTypes.object.isRequired,
        eventHub: PropTypes.object.isRequired,
        config: PropTypes.object.isRequired,
        token: PropTypes.string.isRequired
    }
    constructor(props, context) {
        super(props);
        const {
            datePickerDefaultValue,
            isShowGlobalDateSwitch,
            isGlobalDateChecked,
            datePickerMinValue,
            datePickerMaxValue: datePickMaxValue = getTargetDate(0)
        }
            = context.config.filterAreaConfig;
        const defaultStartDate = getTargetDate(defaultRange);
        const defaultEndDate = getTargetDate();
        let compareDefaultStartDate = getTargetDate(defaultRange * 2);
        let baseDateRangeValue = [defaultStartDate, defaultEndDate];

        let range = defaultRange - 1;
        if (datePickerDefaultValue) {
            if (typeof datePickerDefaultValue === 'string') {
                range = 0;
                const dateUpperString = datePickerDefaultValue.toUpperCase();
                if (dateUpperString === 'TODAY') {
                    const todayDate = getTargetDate(0);
                    compareDefaultStartDate = getTargetDate();
                    baseDateRangeValue = [todayDate, todayDate];
                }
                // 兼容下错误写法，后续大版本改动去掉后面错误的单词
                if (dateUpperString === 'YESTERDAY' || dateUpperString === 'YESTODAY') {
                    const yesterdayDate = getTargetDate();
                    compareDefaultStartDate = getTargetDate(2);
                    baseDateRangeValue = [yesterdayDate, yesterdayDate];
                }
            }
            else if (typeof datePickerDefaultValue === 'object') {
                const {
                    defaultStartDate: startDate,
                    defaultEndDate: endDate
                } = datePickerDefaultValue;
                range = getDiffDay(startDate, endDate);
                if (startDate && endDate) {
                    compareDefaultStartDate = getTargetEndDate(startDate, -(range + 1));
                    baseDateRangeValue = [startDate, endDate];
                }
            }
        }
        const isValidDate = getIsValidRange(baseDateRangeValue, datePickMaxValue, datePickerMinValue);
        if (!isValidDate) {
            console.error(
                '传入的datePickerDefaultValue不在日期可选范围内',
                baseDateRangeValue,
                datePickMaxValue,
                datePickerMinValue
            );
        }
        this.state = {
            range,
            isGlobalDateValid: isValidDate,
            // 日期不合法时，强提示tooltip文案
            globalToolTipVisible: !isValidDate,
            // 日期不合法时，使用默认日期 【待确认，是否全量上，还是只对全局时间生效】
            baseDateRangeValue: isValidDate ? baseDateRangeValue : [defaultStartDate, defaultEndDate],
            isShowGlobalDateSwitch,
            rangePickerMaxValue: datePickMaxValue,
            compareStartDate: compareDefaultStartDate,
            datePickerMaxValue: datePickMaxValue,
            checked: false,
            isGlobalDateChecked,
            errorMsg: ''
        };
    }

    static propTypes = {
        isNeedCompare: PropTypes.bool,
        initDateAndCompare: PropTypes.func
    }
    static defaultProps = {
        isNeedCompare: true,
        initDateAndCompare() {}
    }

    componentDidMount() {
        const {eventHub} = this.context;
        const {initDateAndCompare} = this.props;
        const {baseDateRangeValue, compareStartDate, range} = this.state;
        const endDate = getTargetEndDate(compareStartDate, range);
        const initData = {
            date: baseDateRangeValue,
            checked: false,
            compareDate: [compareStartDate, endDate]
        };

        const {filterAreaConfig} = this.context.config;
        const {isDateSelector} = filterAreaConfig;
        if (!isDateSelector) {
            // 初始化时设置条件
            initDateAndCompare(initData);
            eventHub.trigger('sdk.event.date.change', {
                data: initData
            });

        }
    }

    onBaseDateRangeChange = value => {
        const {checked, isGlobalDateChecked} = this.state;
        const {eventHub} = this.context;
        const diffDay = getDiffDay(value[0], value[1]);
        const compareStartDate = getTargetEndDate(value[0], -(diffDay + 1));
        // todo 对比时间窗可选事件范围逻辑需要与PM确定后才知道
        this.setState({
            baseDateRangeValue: value,
            range: diffDay,
            compareStartDate,
            isGlobalDateValid: true
        }, () => {
            const compareEndDate = getTargetEndDate(compareStartDate, diffDay);

            eventHub.trigger('sdk.event.date.change', {
                data: {
                    date: value,
                    checked,
                    compareDate: [compareStartDate, compareEndDate]
                }
            });

            // 修改时需要同步全局日期
            isGlobalDateChecked && eventHub.trigger('sdk.event.globalDate.change', {
                isGlobalDateChecked,
                globalDefaultDate: {
                    defaultStartDate: value[0],
                    defaultEndDate: value[1]
                }
            });
        });
    }

    onCompareDatePickerChange = value => {
        const {range, checked, baseDateRangeValue} = this.state;
        const {eventHub} = this.context;
        this.setState({errorMsg: ''});
        const rangePickerStartValue = baseDateRangeValue && baseDateRangeValue[0];
        if (rangePickerStartValue !== value) {
            this.setState({
                compareStartDate: value
            }, () => {
                const compareEndDate = getTargetEndDate(value, range);
                eventHub.trigger('sdk.event.date.change', {
                    data: {
                        date: baseDateRangeValue,
                        checked,
                        compareDate: [value, compareEndDate]
                    }
                });
            });
        }
        else {
            this.setState({errorMsg: '请选择不同的时间进行对比'});
        }
    }

    onCheckBoxChange = e => {
        const {eventHub} = this.context;
        const {baseDateRangeValue, compareStartDate, range} = this.state;
        const compareEndDate = getTargetEndDate(compareStartDate, range);
        this.setState({
            checked: e.target.checked
        }, () => {
            eventHub.trigger('sdk.event.date.change', {
                data: {
                    date: baseDateRangeValue,
                    checked: e.target.checked,
                    compareDate: [compareStartDate, compareEndDate]
                }
            });
        });
    }

    getCustomButtonTitle = () => {
        const {compareStartDate, range} = this.state;
        const compareEndDate = getTargetEndDate(compareStartDate, range);
        const lastText = compareStartDate === compareEndDate
            ? compareStartDate
            : `${compareStartDate} ~ ${compareEndDate}`;
        return (
            <div className="custom-date-pick">{lastText}</div>
        );
    }

    onGlobalDateSwitchChange = () => {
        const {eventHub} = this.context;
        const {isGlobalDateChecked, baseDateRangeValue} = this.state;

        const nextState = !isGlobalDateChecked;
        this.setState({
            isGlobalDateChecked: nextState,
            isGlobalDateValid: true
        });

        eventHub.trigger('sdk.event.globalDate.change', {
            isGlobalDateChecked: nextState,
            globalDefaultDate: nextState ? {
                defaultStartDate: baseDateRangeValue[0],
                defaultEndDate: baseDateRangeValue[1]
            } : undefined
        });

    }

    onToggleGlobalToolTip = v => {
        this.setState({
            globalToolTipVisible: v
        });
    }

    render() {
        const {classPrefix, filterAreaConfig, isDatePickerDisabled} = this.context.config;
        const {datePickerMinValue, datePickShortCuts, isDateSelector, hiddenGlobalDateSwitchBtn} = filterAreaConfig;
        const {isNeedCompare, initDateAndCompare} = this.props;
        if (isDateSelector) {
            return (<div className={`${classPrefix}-date-pick-container`}>
                <DateSelector initDateAndCompare={initDateAndCompare} classPrefix={classPrefix} />
            </div>);
        }
        const {
            baseDateRangeValue,
            checked,
            compareStartDate,
            rangePickerMaxValue,
            datePickerMaxValue,
            errorMsg,
            isShowGlobalDateSwitch,
            isGlobalDateChecked,
            isGlobalDateValid,
            globalToolTipVisible
        } = this.state;
        const baseDateRangePickerProps = {
            value: baseDateRangeValue,
            onChange: this.onBaseDateRangeChange,
            shortcuts: datePickShortCuts,
            validateMinDate: datePickerMinValue,
            validateMaxDate: rangePickerMaxValue,
            disabled: isDatePickerDisabled
        };
        const checkboxProps = {
            checked,
            onChange: this.onCheckBoxChange
        };
        const compareDatePickerProps = {
            value: compareStartDate,
            onChange: this.onCompareDatePickerChange,
            validateMinDate: datePickerMinValue,
            validateMaxDate: datePickerMaxValue,
            customButtonTitle: this.getCustomButtonTitle()
        };

        const globalDateSwitchProps = {
            checked: isGlobalDateChecked,
            onChange: this.onGlobalDateSwitchChange,
            style: {
                marginRight: 12
            }
        };
        return (
            <div className={`${classPrefix}-date-pick-container`}>
                {
                    isShowGlobalDateSwitch && (
                        <Fragment>
                            {
                                isGlobalDateValid
                                    ? null
                                    : (
                                        <span style={{display: 'inline-block', position: 'relative', marginRight: 12}}>
                                            <Tooltip
                                                title={
                                                    // oneui有点问题，手动设置个宽度吧
                                                    <div
                                                        style={{width: 200}}
                                                        onMouseEnter={() => this.onToggleGlobalToolTip(true)}
                                                        onMouseLeave={() => this.onToggleGlobalToolTip(false)}
                                                    >
                                                        同步时间超出当前报告可选择范围，已重置为默认时间
                                                    </div>
                                                }
                                                visible={globalToolTipVisible}
                                                getPopupContainer={trigger => trigger.parentNode}
                                                onVisibleChange={this.onToggleGlobalToolTip}
                                                // 因为错误tooltip的visible默认为true，支持点击空白处关闭体验比较好
                                                // 不然用户得手动hover再移出才能触发onVisibleChange关闭掉tooltip
                                                // trigger 改成click支持点击空白处关闭，hover效果用js实现
                                                trigger="click"
                                            >
                                                <IconInfoCircle
                                                    className="global-date-error-info-icon"
                                                    onMouseEnter={() => this.onToggleGlobalToolTip(true)}
                                                    onMouseLeave={() => this.onToggleGlobalToolTip(false)}
                                                />
                                            </Tooltip>
                                        </span>

                                    )
                            }
                            {hiddenGlobalDateSwitchBtn
                                ? null
                                : (
                                    <Tooltip
                                        title={
                                            <div style={{whiteSpace: 'nowrap'}}>
                                                {isGlobalDateChecked ? '关闭时间范围同步' : '开启后同步当前时间范围到其他报告'}
                                            </div>
                                        }
                                    >
                                        <Switch {...globalDateSwitchProps} />
                                    </Tooltip>
                                )
                            }
                        </Fragment>
                    )
                }
                <RangePicker {...baseDateRangePickerProps} />
                {isNeedCompare && <div className="check-box-container"><Checkbox {...checkboxProps}>比较</Checkbox></div>}
                {checked && <div className="compare-date-pick"><DatePicker {...compareDatePickerProps} /></div>}
                {errorMsg && <div className="error-msg">{errorMsg}</div>}
            </div>
        );
    }
}
