/*
 * @Author: sun<PERSON><PERSON><EMAIL>
 * @Date: 2022-05-05 20:16:01
 */
import {request} from '@baidu/winds-ajax';
import {MARS_PRO_STATUS_MAP} from '../config/download';

export async function getDownloadUrlSync(params) { // 同步下载
    const {fileUrl} = await request({
        path: 'marsPro/GET/ReportDataService/getReportFile',
        params
    });
    return {
        fileUrl
    };
}

export async function createAsyncDownloadTask(params, {signal}) { // 异步下载
    const res = await request({
        path: 'marsPro/GET/AsyncReportDataService/createReportTask',
        params
    });
    return res;
}

export async function checkAsyncDownloadTask(taskId, {signal}) {
    const res = await request({
        path: 'marsPro/GET/AsyncReportDataService/getTaskStatus',
        params: {taskId}
    });
    return res;
}

function delay(miliseconds) {
    return new Promise(resolve => {
        setTimeout(resolve, miliseconds);
    });
}

// signal 用于感知外部是否触发了 abort （比如 react 组件已经没了，这里就不需要再轮询了）
export async function getDownloadUrlAsync(params, {signal, timeout, interval}) {
    const {taskId} = await createAsyncDownloadTask(params, {signal}); // 创建任务
    const startTime = Date.now();
    while ((Date.now() - startTime) < timeout) { // 检查
        const {fileUrl, taskStatus} = await checkAsyncDownloadTask(taskId, {signal});
        if (taskStatus === MARS_PRO_STATUS_MAP.success && fileUrl) {
            return {fileUrl: fileUrl};
        }
        else if (taskStatus === MARS_PRO_STATUS_MAP.fail) {
            throw new Error('报告文件生成失败');
        }
        if (signal.aborted) {
            throw new Error('报告文件停止生成');
        }
        await delay(interval);
    }
    throw new Error('报告文件生成超时');
}
