import React, {Fragment} from 'react';
import {getTargetEndDate, getDiffDay} from './filterArea';
import {Toast, Dialog} from '@baidu/one-ui';
import {set, isArray, isPlainObject, mergeWith, entries} from 'lodash';
import defaultConf from '../config/default';
import {templateCountLimit} from '../config/default/table';
import {getConfig as getGlobalConfig} from './customConfig';

export const expandConfig = data => {
    if (!isPlainObject(data)) {
        return data;
    }
    return entries(data).reduce(function (ret, [key, value]) {
        if (isArray(value)) {
            value = value.map(expandConfig);
        }
        else if (isPlainObject(value)) {
            value = expandConfig(value);
        }
        set(ret, key, value);
        return ret;
    }, {});
};

export const mergeConfig = (...sources) => {
    return mergeWith({}, ...sources, function (objValue, srcValue) {
        if (isArray(srcValue)) {
            return srcValue;
        }
    });
};

export function getCombinedConfig(config = {}) {
    return mergeConfig(defaultConf, getGlobalConfig(), expandConfig(config));
}

export function isEmail(email) {
    /* eslint-disable-next-line */
    const EMAIL_REG = /^([a-zA-Z0-9_\.-]+)@([\da-zA-Z\.-]+)\.([a-zA-Z\.]{2,6})([,，]([a-zA-Z0-9_\.-]+)@([\da-zA-Z\.-]+)\.([a-zA-Z\.]{2,6}))*$/;
    return EMAIL_REG.test(email);
}

export const getApiToast = ({
    content = '接口请求错误',
    duration = 3,
    showCloseIcon = false,
    reqId = ''
} = {}) => {
    return Toast.error({
        content: (<Fragment>
            <p>{content}</p>
            {reqId && <p>rid: {reqId}</p>}
        </Fragment>),
        duration,
        showCloseIcon
    });
};

export const errorTextMap = {
    '601': '分时粒度不支持当前所选维度模式',
    '605': '超过个数限制',
    '607': '时间跨度不能超过一年',
    '700': '您没有当前操作的权限',
    '600': '模版名称已存在',
    '608': `该帐户下只能创建不超过${templateCountLimit}个自定义列模板`,
    '606': true
};

export const getApiError = (err, ext = {}) => {
    const errors = err.errors || [];
    const reqId = err.reqId || '';
    const targetCode = errors.filter(e => Object.keys(errorTextMap).indexOf(String(e.code)) > -1) || [];
    if (targetCode && targetCode[0]) {
        const content = targetCode.map(errs => {
            if (String(errs.code) === '606') {
                return errs && errs.message || '系统错误，请稍后再试';
            }
            return errorTextMap[errs && errs.code] || '系统错误，请稍后再试';
        }).join(';');
        if (ext.useToast) {
            return getApiToast({...ext, content, reqId});
        }
        return Dialog.confirm({
            title: '温馨提示',
            content: (<Fragment>
                <p>{content}</p>
                {reqId && <p>rid: {reqId}</p>}
            </Fragment>),
            onOk() {},
            okCancel: false
        });
    }
    return getApiToast({...ext, reqId});
};

export const getLastLengthTime = (startTime, endTime) => {
    const dura = getDiffDay(endTime, startTime);
    return getTargetEndDate(startTime, dura - 1, '-');
};

export const getScrollWidth = () => {
    return window.innerWidth - document.body.clientWidth;
};