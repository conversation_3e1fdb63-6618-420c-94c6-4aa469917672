
import {Toast} from '@baidu/one-ui';
import {getDownloadUrlSync, getDownloadUrlAsync} from '../apis/download';
import {duration} from '../config/download';

function isAbortError(err) {
    return err.name === 'AbortError';
}

export const download = url => {
    const divId = '__Download_fc_Container__';
    const iframeId = '__Download_fc_Iframe__';
    let div = document.getElementById(divId);
    if (!div) {
        div = document.createElement('div');
        div.id = divId;
        div.style.display = 'none';
        const iframe = document.createElement('iframe');
        iframe.id = iframeId;
        iframe.name = iframeId;
        div.appendChild(iframe);
        document.body.appendChild(div);
    }
    div.getElementsByTagName('iframe')[0].src = url;
};

/**
 * 带Toast的下载
 *
 * @param {boolean} isAsync 是否异步，默认同步
 * @param {Object} params 下载参数
 */
export async function downloadWithToast({isAsync, ...params}, {signal, timeout, interval}) {
    const getUrl = isAsync ? getDownloadUrlAsync : getDownloadUrlSync;
    let hideToast = Toast.loading({
        content: '正在为您生成报告...稍后将自动下载',
        duration: duration
    });
    const afterClick = () => {
        hideToast && hideToast();
    };
    try {
        const res = await getUrl(params, {signal, timeout, interval});
        download(res.fileUrl);
        afterClick();
        return res;
    }
    catch (err) {
        if (isAbortError(err)) { // 主动取消的忽略
            return;
        }
        afterClick();
        Toast.error({
            content: '报告文件生成失败',
            duration: duration
        });
    }
}
