import {intersection, pullAll, flatten, uniq} from 'lodash';
import {expandConfig} from './common';

// 实例配置 > 自定义全局配置 > 默认配置
let customConfig = {};

export const getConfig = () => {
    return customConfig;
};

export const setConfig = (val = {}) => {
    customConfig = expandConfig(val);
};

export const resetConfig = () => {
    customConfig = {};
};

// 获取所有有选中的互斥组中未选中的冲突指标。如[[A, B], [C, D]]，假设A被选中了，则返回[B]
export const flattenConflictFields = ({selectedFields, conflictFields = []}) => {
    return uniq(pullAll(flatten(conflictFields
        .filter(fields => intersection(selectedFields, fields).length > 0)), selectedFields));
};
