const startFlags = {
    'table-data-fetching': 'table_data_fetching'
};
const measures = {
    'table-data-success': {
        feedbackFlag: 'report_data_success',
        startFlag: startFlags['table-data-fetching'],
        endFlag: 'table-data-success'
    },
    'table-data-fail': {
        feedbackFlag: 'report_data_fail',
        startFlag: startFlags['table-data-fetching'],
        endFlag: 'table-data-fail'
    },
    'table-data-painted': {
        feedbackFlag: 'report_data_painted',
        startFlag: 'table-data-success',
        endFlag: 'table-data-painted',
        isClear: true
    },
    'table-data-fetched': {
        feedbackFlag: 'report_data_fetched',
        startFlag: startFlags['table-data-fetching'],
        endFlag: 'table-data-fetched',
        isWeirwoodWidthReportType: true
    },
    'table-data-painted-new': {
        tongjiFlag: 'report_d_painted_new',
        feedbackFlag: {
            'report_d_painted_new': 'painted_new',
            'report_d_font_to_end': 'font_to_end'
        },
        startFlag: {
            'report_d_painted_new': 'table-data-fetched',
            'report_d_font_to_end': startFlags['table-data-fetching']
        },
        endFlag: 'table-data-painted-new',
        isClear: true,
        isWeirwoodWidthReportType: true
    }
};
const perfMark = (performance, name, tongjiPerf, extra = {}) => {
    try {
        performance.mark(name);
        if (tongjiPerf && typeof tongjiPerf.send === 'function') {
            tongjiPerf.send(name, undefined, extra);
        }
    } catch (e) {}
};
const tongjiSend  = ({
    tongjiPerf,
    feedbackFlag,
    extra,
    ...sendValues
}) => {
    tongjiPerf.send(feedbackFlag, undefined, {
        ...extra,
        ...sendValues
    });
};
const measureReport = ({
    performance,
    tongjiPerf,
    prefix,
    extra,
    feedbackFlag,
    startFlag,
    endFlag,
    isMulti,
    isClear,
    isWeirwoodWidthReportType
}) => {
    const perfMesureStart = performance.getEntriesByName(startFlag);
    if ((perfMesureStart || []).length) {
        const xFeedback = `${prefix}_${feedbackFlag}`;
        performance.measure(
            xFeedback,
            startFlag,
            endFlag
        );
        if (isWeirwoodWidthReportType) {
            const reportType = extra.reportType;
            const xFbReportType = `${prefix}_${feedbackFlag}_${reportType}`;
            performance.measure(
                xFbReportType,
                startFlag,
                endFlag
            );
        }
        const perfMesure = performance.getEntriesByName(xFeedback);
        if ((perfMesure || []).length) {
            const duration = perfMesure[perfMesure.length - 1].duration.toFixed(3);
            if (isClear) {
                performance.clearMarks(startFlag);
            }
            if (!isMulti) {
                tongjiSend({
                    tongjiPerf,
                    feedbackFlag,
                    extra,
                    duration
                });
            } else {
                return duration;
            }
        }
    }
};
const measure = ({
    performance,
    tongjiPerf,
    prefix,
    extra,
    feedbackFlag,
    startFlag,
    endFlag,
    tongjiFlag,
    isClear,
    isWeirwoodWidthReportType
}) => {
    try {
        perfMark(performance, endFlag);
        if (typeof feedbackFlag === 'object') {
            const sendValues = {};
            for (let item of Object.keys(feedbackFlag)) {
                const sendValue = measureReport({
                    performance: performance,
                    tongjiPerf: tongjiPerf,
                    prefix: prefix,
                    extra: extra,
                    feedbackFlag: item,
                    startFlag: startFlag[item],
                    endFlag: endFlag,
                    isMulti: true,
                    isClear,
                    isWeirwoodWidthReportType
                });
                if (!sendValue) {
                    return;
                }
                sendValues[feedbackFlag[item]] = sendValue;
            }
            tongjiSend({
                tongjiPerf,
                feedbackFlag: tongjiFlag,
                extra,
                ...sendValues
            });
        } else {
            measureReport({
                performance,
                tongjiPerf,
                prefix,
                extra,
                feedbackFlag,
                startFlag,
                endFlag,
                isClear,
                isWeirwoodWidthReportType
            });
        }
    } catch (e) {}
};
export default ({
    performance = {
        getEntriesByName() {},
        measure() {},
        clearMarks() {},
        mark() {}
    },
    tongjiPerf = {
        send() {}
    },
    eventName,
    prefix,
    extra
}) => {
    const startFlagName = startFlags[eventName];
    const measureFlagInfo = measures[eventName];
    if (startFlagName) {
        perfMark(performance, startFlagName, tongjiPerf, extra);
    } else if (measureFlagInfo) {
        measure({
            performance,
            tongjiPerf,
            prefix,
            extra,
            ...measureFlagInfo
        });
    }
};