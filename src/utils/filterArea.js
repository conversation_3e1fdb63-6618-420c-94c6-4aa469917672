import React from 'react';
import regionTools from './regionTools';

const {language, regionIds} = regionTools;

export const getRegionDatasource = () => {
    return regionIds.map(i => {
        const item = {
            label: language.regionName[i],
            value: i
        };
        return item;
    });
};
// 获取页面初始化时的筛选条件
export const getDefaultFilterData = filterAreaConfig => {
    const {filterList, filterData} = filterAreaConfig;
    const defaultFilterData = {};
    for (let i = 0; i < filterList.length; i++) {
        const filter = filterList[i];
        const filterItemData = filterData[filter];
        const {isInFilterCondition, defaultValue, option} = filterItemData;
        const targetOption = option && option.filter(item => item.value === defaultValue) || [];
        const values = targetOption && targetOption[0] && 'realValue' in targetOption[0]
            ? targetOption && targetOption[0].realValue
            : defaultValue;
        // values为'0'和0的情况应该视为正常情况
        if (values === '0' || values === 0 || values) {
            defaultFilterData[filter] = isInFilterCondition
                ? {
                    operator: 'IN',
                    values: Array.isArray(values) ? values : [values]
                }
                : values;
        }
    }
    return defaultFilterData;
};

// 自定义选中后的render
export const customRenderTarget = (value, column, context) => {
    const {filterAreaConfig} = context.config;
    const {name, option} = filterAreaConfig.filterData[column];
    const targetOption = option.filter(item => String(item.value) === value);
    return (
        <span className="filter-item-custom-render">
            <span>{name}：</span>
            <span>{targetOption[0].label}</span>
        </span>
    );
};

// 获取页面初始化时更多筛选条件的编辑值
export const getInitPageEditingFilterData = filterAreaConfig => {
    const {moreList} = filterAreaConfig;
    const pageEditingFilterData = {};
    for (let i = 0; i < moreList.length; i++) {
        const filter = moreList[i];
        pageEditingFilterData[filter] = {
            operator: 'IN',
            values: []
        };
    }
    return pageEditingFilterData;
};

// 获取某个日期的毫秒数
export const getTargetDateTime = d => Date.parse(d);

// 根据距离1970/01/01的毫秒数获取目标日期
export const getTargetDateOfMs = (ms, split = '/') => {
    const newDate = new Date(ms);
    const day = newDate.getDate();
    const month = newDate.getMonth() + 1;
    const year = newDate.getFullYear();
    const newDay = day < 10 ? `0${day}` : day;
    const newMonth = month < 10 ? `0${month}` : month;
    return `${year}${split}${newMonth}${split}${newDay}`;
};

// 获取页面右上角的日期的默认时间
export const getTargetDate = (diffDay = 1, split = '/') => {
    const date = new Date();
    const time = date.getTime();
    const timeOneDayBefore = time - diffDay * 24 * 60 * 60 * 1000;
    return getTargetDateOfMs(timeOneDayBefore, split);
};

// 获取比目标日期晚6天的日期
export const getTargetEndDate = (d, diffDay, split = '/') => {
    const time = getTargetDateTime(d);
    const startTime = (new Date(time)).getTime();
    const endTime = startTime + diffDay * 24 * 60 * 60 * 1000;
    return getTargetDateOfMs(endTime, split);
};

// 获取两个日期相差的天数
export const getDiffDay = (startDate, endDate) => {
    const endTime = getTargetDateTime(endDate);
    const startTime = getTargetDateTime(startDate);
    return (endTime - startTime) / (24 * 60 * 60 * 1000);
};

// 判断是否在时间范围内
export const getIsValidRange = (value, max, min) => {
    const [startDate, endDate] = value;
    return (!min || getDiffDay(min, startDate) >= 0) && getDiffDay(endDate, max) >= 0;
};

// 获取页面筛选条件，包括：页面上方筛选区，多账户、时间窗、比较这几处的数据
export const getPageFilterData = state => {
    const newFilterData = {
        filterFields: []
    };
    const {pageFilterData, dateData, userIds} = state;
    Object.keys(pageFilterData).map(data => {
        const itemData = pageFilterData[data];
        if (pageFilterData && itemData && typeof itemData === 'string') {
            newFilterData[data] = itemData;
        }
        else {
            // level为自定义的筛选范围层级：计划，单元、创意等
            const {values, operator, level} = itemData;
            if (values && values.length > 0) {
                newFilterData.filterFields.push({
                    column: level || data,
                    operator: operator || 'IN',
                    values
                });
            }
        }
    });
    if (dateData) {
        const {date, compareDate, checked} = dateData;
        if (date && date.length === 2) {
            newFilterData.startDate = date[0] && date[0].replace(/\//g, '-');
            newFilterData.endDate = date[1] && date[1].replace(/\//g, '-');
        }
        if (checked && compareDate && compareDate.length === 2) {
            newFilterData.compareStartDate = compareDate[0] && compareDate[0].replace(/\//g, '-');
            newFilterData.compareEndDate = compareDate[1] && compareDate[1].replace(/\//g, '-');
        }
    }
    if (userIds) {
        newFilterData.userIds = userIds;
    }
    return newFilterData;
};

export const formatDate = (date = '', from = '-', to = '/') => {
    return date.split(' ')[0].replace(new RegExp(from, 'g'), to);
};

export const getQuarterSeasonStartMonth = function (month) {
    const spring = 0; // 春
    const summer = 3; // 夏
    const fall = 6; // 秋
    const winter = 9; // 冬

    // 月份从0-11
    if (month < 3) {
        return spring;
    }

    if (month < 6) {
        return summer;
    }

    if (month < 9) {
        return fall;
    }

    return winter;
};

export const getQLeftDay = (year, month, today) => Math.ceil(
    getDiffDay(
        new Date(year, getQuarterSeasonStartMonth(month), 1),
        today
    )
);

export const getYearLeftDay = (year, today) => Math.ceil(
    getDiffDay(
        new Date(year, 0, 1),
        today
    )
);

