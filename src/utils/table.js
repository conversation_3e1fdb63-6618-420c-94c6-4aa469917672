import {isNull, cloneDeep, isNumber, isNaN} from 'lodash';
import {INFINITY} from '../config/default/cell';
import {numberTypes} from '../config/default/filter';
import {regionFilterType, regionFilterBackendMap, templateValue, templateMap} from '../config/default/table';
import {getTargetDate} from './filterArea';
import Big from 'big.js';

export const getTableReportType = (context, topTabKey, tableIndicatorKey) => {
    const {config} = context;
    const reportType = tableIndicatorKey
        || config.reportArea.tabs && config.reportArea.tabs[topTabKey].tableAreaConfig.tableReportType;
    return reportType;
};

// 获取页面下方主表格要请求但是不显示的列
export const getTableExtraColumns = (context, topTabKey, tableIndicatorKey) => {
    const {config} = context;
    const {
        tableIndicatorList,
        extraColumns = []
    } = config.reportArea.tabs && config.reportArea.tabs[topTabKey].tableAreaConfig;
    let targetExtraColumns = extraColumns;
    if (tableIndicatorKey && tableIndicatorList && tableIndicatorList.length) {
        const targetTableIndicatorList
            = tableIndicatorList.filter(indicator => String(indicator.value) === String(tableIndicatorKey)) || [];
        targetExtraColumns
            = targetTableIndicatorList && targetTableIndicatorList[0] && targetTableIndicatorList[0].extraColumns || [];
    }
    return targetExtraColumns || [];
};

export const getTableCustomerFields = ({tableAreaConfig, context, topTabKey, tableIndicatorKey}) => {
    let customerFields = null;
    const reportType = getTableReportType(context, topTabKey, tableIndicatorKey);
    const {tableReportType: originalTableReportType, customerFields: originalCustomerFields} = tableAreaConfig;
    if (originalCustomerFields && originalTableReportType === reportType) {
        customerFields = originalCustomerFields;
    }
    else {
        const tableIndicatorCustomerFields = (tableAreaConfig.tableIndicatorList || [])
            .filter(item => item.value === Number(reportType)) || [];
        customerFields = tableIndicatorCustomerFields.length && tableIndicatorCustomerFields[0].customerFields;
    }
    return customerFields;
};

// 获取表头请求参数
export const getTableHeaderParams = (context, reportType, showPartFields) => {
    const {token, config} = context;
    const {withNewCategory} = config;
    return {
        token,
        reportType,
        dataAnalysis: showPartFields, // 请求用户有数据的列
        withNewCategory
    };
};

// 获得筛选项附加字段
export const getFilterAdditionFields = (tableAreaConfig = {}, filterFields = []) => {
    const {additionFieldCommonFilter = {}} = tableAreaConfig;
    const filterAdditionFields = [];
    filterFields.forEach(item => {
        const column = item.column;
        if (additionFieldCommonFilter[column]) {
            filterAdditionFields.push(...additionFieldCommonFilter[column]);
        }
    });
    return filterAdditionFields;
};


// 表格列根据自定义列排序
export const sortColumns = (fields, allColumns) => {
    const sortList = allColumns.reduce((ret, column, index) => {
        ret[column] = index;
        return ret;
    }, {});
    fields.sort((a, b) => {
        const aKey = a.key || a;
        const bKey = b.key || b;
        return sortList[aKey] - sortList[bKey];
    });
    return fields;
};

// 获取自定义列小红点请求参数
export const getCustomMarkFieldsParams = (state, context, props) => {
    const {
        commonFilterInfo,
        tableHeaderData
    } = state;
    const {
        topTabKey,
        tableIndicatorKey
    } = props;
    const reportType = getTableReportType(context, topTabKey, tableIndicatorKey);
    const {
        userIds = ['3203500']
    } = commonFilterInfo;

    const defaultStartDate = getTargetDate(7, '-');
    const defaultEndDate = getTargetDate(1, '-');

    const {mccInfo = {}} = context && context.config;
    const suportAllOption = mccInfo.suportAllOption;
    const suportAllOptionSelectedAll = suportAllOption && userIds.length === 1 && userIds[0] === '0';
    const {visibleColumns = []} = tableHeaderData;
    return {
        reportType,
        startDate: defaultStartDate,
        endDate: defaultEndDate,
        timeUnit: 'SUMMARY',
        userIds: suportAllOptionSelectedAll ? [] : userIds,
        columns: ['date', 'cost', ...visibleColumns],
        sorts: [],
        filters: [],
        startRow: 0,
        rowCount: 10000,
        needSum: true,
        splitColumn: ''
    };
};

// 获取当前自定义列里选择的列
export const getSelectedFields = ({
    withNewSortableSelector,
    defaultVisibleColumns,
    customColumns,
    templateId,
    templateColumns
}) => {
    const defaultColumns = (defaultVisibleColumns.length && defaultVisibleColumns) || [];
    let selectedFields = [];
    if (!withNewSortableSelector) {
        selectedFields = (customColumns.length && customColumns) || defaultColumns;
    }
    else {
        if (templateId === templateValue.default) {
            selectedFields = defaultColumns;
        }
        else if (templateId === templateValue.custom) {
            selectedFields = (customColumns.length && customColumns) || defaultColumns;
        }
        else if (templateId > templateValue.default) {
            selectedFields = (templateColumns.length && templateColumns) || defaultColumns;
        }
    }
    return selectedFields;
};

// 获取表格数据请求参数
export const getTableDataParams = (state, context, props) => {
    const {
        pageNo,
        pageSize,
        sortRule,
        filterData,
        tableHeaderData,
        commonFilterInfo,
        splitColumn,
        smartRowCount,
        indicatorExternalFilters = [],
        indicatorTableSupportCommonFilter = true
    } = state;
    const {
        topTabKey,
        tableIndicatorKey,
        extraColumns = [],
        tableAreaConfig = {},
        columnsFields = []
    } = props;
    const {sortName} = sortRule || {};
    const isAscend = sortName && sortRule[sortName] && sortRule[sortName].isAscend;
    const reportType = getTableReportType(context, topTabKey, tableIndicatorKey);
    const {filter: filterConfig} = context && context.config;
    const innerCustomFilterMap = filterConfig && filterConfig.innerCustomFilterMap || {};
    let outerFiltersMap = {};
    // 表格之外的筛选项
    const {
        startDate = '2019-01-01',
        endDate = '2019-12-12',
        compareStartDate,
        timeUnit,
        userIds = ['3203500'],
        filterFields = [],
        // 接收自定义的跟timeUnit平级的参数，可以参考http://0.0.0.0:8009/#id=normal中的testProduct
        ...rest
    } = commonFilterInfo;
    const {
        extraStaticFilters = [],
        extraStaticSorts = [],
        needSum = true,
        isResponseTimeWindow,
        staticPageSize,
        excludeFilters = [],
        withNewSortableSelector = false
    } = tableAreaConfig;
    // 表格的列
    const {
        customColumns = [], defaultVisibleColumns = [], columnConfigs,
        visibleColumns = [], templateId, templateColumns
    } = tableHeaderData;
    const extraFilters = typeof extraStaticFilters === 'function'
        ? extraStaticFilters(splitColumn)
        : extraStaticFilters;
    const {mccInfo = {}} = context && context.config;
    const suportAllOption = mccInfo.suportAllOption;
    const suportAllOptionSelectedAll = suportAllOption && userIds.length === 1 && userIds[0] === '0';
    // 筛选项
    let filters = Object.keys(filterData).reduce((ret, filterKey) => {
        const {operator, values} = filterData[filterKey] || {};
        const columnData = columnConfigs && columnConfigs[filterKey] || {};
        const {columnType} = columnData;
        if (values && values.length) {
            // 省、市级别的筛选特殊逻辑，需要将字段转义
            let filterKeyName = filterKey;
            if (regionFilterType.indexOf(filterKey) > -1) {
                filterKeyName = regionFilterBackendMap[filterKey];
            }
            ret.push({
                column: filterKeyName,
                operator,
                values: numberTypes.indexOf(columnType) > -1 ? values.map(v => +v) : values
            });
        }
        return ret;
    }, [
        ...(
            indicatorTableSupportCommonFilter
                ? [...filterFields.filter(({values}) => values && values[0] !== 'markAdditionField')]
                : []
        ),
        ...extraFilters,
        ...indicatorExternalFilters
    ]);
    if (excludeFilters.length) {
        filters = filters.filter(g => !excludeFilters.includes(g.column));
    }
    const selectedColumns = getSelectedFields({
        withNewSortableSelector,
        defaultVisibleColumns,
        customColumns,
        templateId,
        templateColumns
    });
    const formatedCustomFields = columnsFields.filter(field => {
        const tempColumns = (withNewSortableSelector
            && templateId !== templateValue.default
            && templateId !== templateValue.custom)
            ? templateColumns : customColumns;
        return tempColumns.indexOf(field) !== -1;
    });
    const targetColumns = (formatedCustomFields.length && formatedCustomFields) || selectedColumns;
    const targetColumnsWithSplitColumn = targetColumns;
    const targetColumnsWithSplitColumnAndExtraColumns = targetColumnsWithSplitColumn.concat(extraColumns);
    const filterAdditionFields = getFilterAdditionFields(tableAreaConfig, filterFields);
    const columns = Array.from(new Set([...targetColumnsWithSplitColumnAndExtraColumns, ...filterAdditionFields]));
    const isAdditionFields = (filterAdditionFields && filterAdditionFields.length)
        || (extraColumns && extraColumns.length);
    if (isAdditionFields) {
        sortColumns(columns, visibleColumns);
    }
    // 对筛选条件自定义组件特殊的筛选条件进行处理，由外部传入getFilters处理函数 start
    Object.keys(innerCustomFilterMap).map(f => {
        const getFilters = innerCustomFilterMap[f] && innerCustomFilterMap[f].getFilters;
        const targetFilter = filters.filter(g => g.column === f) || [];
        filters = filters.filter(g => g.column !== f) || [];
        const targetValues = targetFilter && targetFilter[0] && targetFilter[0].values || [];
        if (typeof getFilters === 'function' && columns.indexOf(f) > -1) {
            const {outerFilters = {}, innerFilters = []} = getFilters(targetValues);
            outerFiltersMap = {
                ...outerFiltersMap,
                ...outerFilters
            };
            if (targetValues.length > 0) {
                filters = filters.concat(innerFilters);
            }
        }
    });
    // 对筛选条件自定义组件特殊的筛选条件进行处理，由外部传入getFilters处理函数 end

    let startRow = (pageNo - 1) * pageSize;
    let rowCount = +smartRowCount || pageSize || 20;
    if (isNumber(staticPageSize)) {
        startRow = 0;
        rowCount = staticPageSize;
    }
    const params = {
        reportType,
        startDate,
        endDate,
        compareStartDate,
        timeUnit: isResponseTimeWindow || timeUnit || 'DAY',
        userIds: suportAllOptionSelectedAll ? [] : userIds,
        columns,
        sorts: sortName ? [{
            column: sortName,
            sortRule: isAscend ? 'ASC' : 'DESC'
        }, ...extraStaticSorts] : extraStaticSorts,
        filters,
        startRow,
        rowCount,
        needSum,
        splitColumn: splitColumn || '',
        ...rest
    };
    return {
        ...params,
        ...outerFiltersMap
    };
};

// 计算初始的filterData
export const getInitFilterData = (originalTableHeaderData, context, props) => {
    const {
        customColumns = [],
        defaultVisibleColumns = [],
        columnConfigs,
        smartFilterable,
        smartFilterColumns,
        splitColumns,
        splitable,
        templateId,
        templateColumns
    } = originalTableHeaderData;
    const {smartQueryAreaConfig = {}} = context.config;
    const {customColumnfilters = []} = smartQueryAreaConfig;
    const {
        defaultFilterOperatorMap = {},
        innerCustomFilterMap = {}
    } = context && context.config && context.config.filter || {};
    const {mainAreaConfig: {tableAreaConfig}} = props;
    const {withNewSortableSelector = false, initialFilters = []} = tableAreaConfig;
    const selectedColumns = getSelectedFields({
        withNewSortableSelector,
        defaultVisibleColumns,
        customColumns,
        templateId,
        templateColumns
    });
    // 初始化数据时需要加上细分列
    const targetColumnsWithSplitColumns = splitable && splitColumns && splitColumns.length > 0
        ? selectedColumns.concat(splitColumns)
        : selectedColumns;
    const filterData = {};
    for (let i = 0; i < targetColumnsWithSplitColumns.length; i++) {
        const columnItem = columnConfigs[targetColumnsWithSplitColumns[i]] || {};
        const {columnName, columnType, filterable, feConfig = {}} = columnItem;
        const {customFilter, defaultFilterableOperator} = feConfig;
        if (filterable) {
            const filterItem = {
                operator: defaultFilterableOperator || defaultFilterOperatorMap[columnType],
                values: [],
                connotSubmit: false
            };
            // 处理自定义的筛选器
            if (feConfig && customFilter) {
                filterItem.operator = defaultFilterOperatorMap.CUSTOM;
                filterItem.values
                    = innerCustomFilterMap[columnName] && innerCustomFilterMap[columnName].defaultValue || [];
            }
            filterData[columnName] = filterItem;
        }
        if (smartFilterable && smartFilterColumns && smartFilterColumns.indexOf(columnName) > -1) {
            filterData[columnName] = {
                operator: defaultFilterableOperator || defaultFilterOperatorMap.STRING,
                values: []
            };
        }
        if (regionFilterType.indexOf(columnName) > -1) {
            filterData[columnName] = {
                operator: defaultFilterableOperator,
                values: []
            };
        }
    }
    if ((customColumnfilters || []).length) {
        customColumnfilters.forEach(item => {
            const {column, operator, values} = item;
            filterData[column] = {
                operator,
                values,
                connotSubmit: false
            };
        });
    }
    if (initialFilters.length) {
        initialFilters.forEach(item => {
            const {column, operator, values} = item;
            filterData[column] = {
                operator,
                values
            };
        });
    }
    return filterData;
};

// 获取列互斥信息配置,validCostumColumns为自定义列中的有效列信息
export const getConflictFieldsConfig = (columnsInfo, validCostumColumns) => {
    // 被访问过的有互斥信息的column的List
    const visitedColumnList = [];
    const conflictFields = [];
    // 仅解析会出现在自定义列选择框中的选项
    validCostumColumns.forEach(validColumn => {
        const {conflictColumns = []} = columnsInfo[validColumn];
        if (conflictColumns.length > 0) {
            conflictColumns.forEach(flictField => {
                // 仅当互斥的两列都为自定义列组件中可展示的有效列时，才会添加互斥关系
                if (validCostumColumns.indexOf(flictField) > -1 && visitedColumnList.indexOf(flictField) === -1) {
                    conflictFields.push([validColumn, flictField]);
                }
            });
            visitedColumnList.push(validColumn);
        }
    });
    return conflictFields;
};

// 将接口返回的模版列表数据适配成组件格式
const adaptTemplateListToState = templateList => templateList.map(t => ({
    key: String(t.templateId),
    label: t.templateName,
    selectedFields: t.columns
}));

// 接口配置转为自定义列的配置
export const getInitCustomerColumnData = ({
    metaData, templateList = [], withNewSortableSelector = false, context
}) => {
    const {
        columnConfigs,
        requiredColumns,
        defaultVisibleColumns = [],
        customColumns = [],
        columnCategories = [],
        haveDataColumns = [],
        newColumnCategories = [],
        templateId,
        templateColumns = []
    } = metaData;
    const columnsInfo = cloneDeep(columnConfigs);
    // 设置不可移动和移除的自定义列
    requiredColumns.forEach(item => {
        if (columnsInfo[item]) {
            columnsInfo[item].removable = false;
        }
    });
    const fieldMap = Object.keys(columnsInfo).reduce((ret, columnKey) => {
        const column = columnsInfo[columnKey];
        const fixed = column?.feConfig?.fixed;
        const setColumnFixed = fixed ? {
            removable: false,
            draggable: false,
            fixLeft: fixed === 'left'
        } : {};
        ret[columnKey] = {
            ...column,
            ...setColumnFixed,
            label: column.columnText || '未知'
        };
        return ret;
    }, {});
    let validCostumColumns = [];
    let groups = [];

    // 报表列支持新的二级分类
    // mrd: https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/WWW6pX-lL3/gmIjZKZ6XN/UdNGXkNGCK4_6e
    if (context?.config?.withNewCategory) {
        groups = newColumnCategories.map((group, index) => {
            const {name, subCategories = []} = group;
            const children = subCategories.map(({name, columns}) => {
                const validColumns = columns.filter(column => fieldMap[column]);
                const partColumns = haveDataColumns.filter(column => validColumns.includes(column));
                validCostumColumns = [...validCostumColumns, ...validColumns];
                return {
                    id: name,
                    label: name,
                    fields: validColumns,
                    partFields: partColumns
                };
            }).filter(group => group.fields.length);
            return {
                id: index,
                label: name, // 中文描述
                children // columns配置数组
            };
        });
    }
    else {
        groups = columnCategories.map((group, idx) => {
            const {name = '未知', columns = []} = group;
            const validColumns = columns.filter(column => fieldMap[column]);
            const partColumns = haveDataColumns.filter(column => validColumns.includes(column));
            validCostumColumns = [...validCostumColumns, ...validColumns];
            return {
                id: '' + idx,
                label: name,
                fields: validColumns,
                partFields: partColumns,
                showTip: (['落地页转化', '小程序转化', 'APP转化', '落地页时间转化', '小程序时间转化', 'APP时间转化'].includes(name))
            };
        }).filter(group => group.fields.length);
    }

    const selectedFields = getSelectedFields({
        withNewSortableSelector,
        defaultVisibleColumns,
        customColumns,
        templateId,
        templateColumns
    });

    const formatedTemplateList = adaptTemplateListToState(templateList);

    const conflictFields = getConflictFieldsConfig(columnsInfo, validCostumColumns);

    return {
        initCustomerColumnData: {
            originFields: defaultVisibleColumns,
            selectedFields,
            fieldMap,
            groups,
            conflictFields
        },
        initCustomerTemplateData: {
            currentTemplate: templateMap.get(templateId) || String(templateId),
            templateList: formatedTemplateList
        }
    };
};

// 加分割符号
export const splitNum = num => {
    return (num + '').replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
};


// 十万以上返回单位
const addMagnitude = (value, d = 2, magnitudePrecision = 2) => {
    const sizes = ['', '万', '亿', '万亿'];
    const k = 10000;
    if (value < k) {
        return splitNum(value.toFixed(d));
    }
    const i = Math.floor(Math.log(value) / Math.log(k));

    return splitNum(((value / Math.pow(k, i))).toFixed(magnitudePrecision)) + sizes[i];
};

// 格式化数字
export const formatText = (itemValue, isPercentage, precision = 2, isMagnitude = false, magnitudePrecision) => {
    // 原生toFixed四舍五入存在问题，使用big.js
    if (isNumber(itemValue) && !isNaN(itemValue)) {
        // 可能会存在无限形式，做下处理
        if (+itemValue > INFINITY) {
            itemValue = '-';
        } else if (isPercentage) {
            if (itemValue !== 0) {
                itemValue = new Big(itemValue * 100);
                itemValue = splitNum(+itemValue.toFixed(precision)) + '%';
            }
        } else {
            itemValue = new Big(itemValue);
            itemValue = isMagnitude
                ? addMagnitude(+itemValue, precision, magnitudePrecision)
                : splitNum(+itemValue.toFixed(precision));
        }
    } else {
        itemValue = '-';
    }
    return itemValue;
};

export const formatApiData = ({summary, rows = [], totalRowCount}, extraColumns = [], summaryPosition = 'after') => {
    let isComparedData = false;
    let datasource = [...rows];
    const summaryDate =  '总计-' + (totalRowCount || 0);
    if (summary && summary.sourceInfo) {
        if (!summary.changeRatio.date) {
            summary.changeRatio.date = summaryDate;
        }
        if (!summary.changeValue.date) {
            summary.changeValue.date = summaryDate;
        }
        if (!~(summary.changeRatio.date || '').indexOf('全部汇总: ')) {
            summary.changeRatio.date = summaryDate;
        }
        summary.sourceInfo.isSummaryRow = true;
        if (summaryPosition === 'after') {
            datasource = datasource.concat(summary);
        } else {
            datasource.unshift(summary);
        }
    }
    else if (Object.keys(summary || {}).length) {
        if (summary && !~(summary.date || '').indexOf('全部汇总: ')) {
            summary.date = summaryDate;
            summary.isSummaryRow = true;
            if (summaryPosition === 'after') {
                datasource = datasource.concat(summary);
            } else {
                datasource.unshift(summary);
            }
        }
    }
    // 是对比
    if (datasource.length && datasource[0].sourceInfo) {
        isComparedData = true;
        datasource = datasource.map(item => {
            let {values} = Object;
            let arr = [];
            for (let value of values(item)) {
                arr.push(value);
            }
            if (arr[3] && !arr[3].date) {
                arr[3].date = (arr[2].date || '').replace('变化量', '变化率');
            }
            return arr;
        });
    }
    if (isComparedData) {
        datasource = datasource.reduce((ret, item, index) => {
            const sortedItem = {
                rowIndex: index
            };
            if (item[0]) {
                Object.keys(item[0]).forEach(key => {
                    if (extraColumns.indexOf(key) > -1) {
                        sortedItem[key] = item[0][key];
                    }
                    else {
                        sortedItem[key] = [];
                        for (let i = 0; i < item.length; i++) {
                            if (!isNull(item[i][key])) {
                                sortedItem[key].push(item[i][key]);
                            }
                        }
                    }
                });
                ret.push(sortedItem);
            }
            return ret;
        }, []);
    }
    else {
        datasource = datasource.map((item, index) => {
            const sortedItem = {
                rowIndex: index
            };
            Object.keys(item).forEach(key => {
                const value = item[key];
                if (!isNull(value)) {
                    sortedItem[key] = [value];
                }
                if (extraColumns.indexOf(key) > -1) {
                    sortedItem[key] = value;
                }
            });
            return sortedItem;
        });
    }
    return datasource;
};

export const formatSplitApiData = ({
    summary,
    rows = [],
    totalRowCount}, columns = [], splitColumn, extraColumns = [], summaryPosition = 'after') => {
    let datasource = [...rows];
    const summaryDate =  '总计-' + (totalRowCount || 0);
    if (Object.keys(summary || {}).length) {
        if (summary && !~(summary.date || '').indexOf('全部汇总: ')) {
            summary.date = summaryDate;
            summary.isSummaryRow = true;
            if (summaryPosition === 'after') {
                datasource = datasource.concat(summary);
            } else {
                datasource.unshift(summary);
            }
        }
    }

    datasource = datasource.map(item => {
        let arr = [];
        const {parentRow, subRows = []} = item;
        if (parentRow) {
            arr.push(parentRow);
            subRows.map(sub => {
                arr.push(sub);
            });
        }
        else {
            arr.push(item);
        }
        return arr;
    });

    datasource = datasource.reduce((ret, item, index) => {
        const sortedItem = {
            rowIndex: index
        };
        if (item[0]) {
            const targetItem = item[1] || item[0];
            targetItem[splitColumn] = targetItem[splitColumn] === 0
                ? targetItem[splitColumn]
                : targetItem[splitColumn] || '-';
            // 这里要从1开始取，不能从0开始，因为0是汇总行，没有细分行
            [...columns, splitColumn].forEach(key => {
                if (extraColumns.indexOf(key) > -1) {
                    sortedItem[key] = targetItem[key];
                }
                else {
                    sortedItem[key] = [];
                    for (let i = 0; i < item.length; i++) {
                        if (!isNull(item[i][key])) {
                            sortedItem[key].push(item[i][key]);
                        }
                    }
                }
            });
            ret.push(sortedItem);
        }
        return ret;
    }, []);
    return datasource;
};

export const getTableIndicatorText = ({tableIndicatorKeyList = [], tableIndicatorList = []}) => {
    const len = tableIndicatorKeyList && tableIndicatorKeyList.length;
    if (len === 0) {
        return '';
    }
    const targetIndicator
        = tableIndicatorList.filter(item => tableIndicatorKeyList[len - 1] === String(item.value))[0];
    const {label, children} = targetIndicator;
    if (len === 2) {
        const secondData = children && children.filter(item => String(item.value) === tableIndicatorKeyList[0]);
        const secondLabel = secondData && secondData[0] && secondData[0].label || '';
        const lastSecondLable = secondLabel ? `-${secondLabel}` : '';
        return `定向维度：${label}${lastSecondLable}`;
    }
    return label;
};

// 获得表格指标下拉信息
export const getIndicatorInfo = (list = [], tableIndicatorList = [], defaultTitle = '定向维度') => {
    const selectedList = [...list].reverse();
    const firstLevel = selectedList[0];
    const secondLevel = selectedList[1];
    let title = defaultTitle;
    let additionFields = [];
    let indicatorExternalFilters = [];
    let reportType = '';
    let indicatorTableSupportCommonFilter = true;
    if (firstLevel) {
        const indicatorItem = tableIndicatorList.filter(item => ('' + item.value) === ('' + firstLevel))[0];
        if (indicatorItem) {
            title += '：' + indicatorItem.label;
            additionFields = indicatorItem.additionFields;
            indicatorExternalFilters = indicatorItem.indicatorExternalFilters;
            reportType = indicatorItem.reportType;
            indicatorTableSupportCommonFilter = indicatorItem.indicatorTableSupportCommonFilter;
            if (indicatorItem.children && indicatorItem.children.length && secondLevel) {
                const indicatorSecondItem = indicatorItem.children.filter(
                    item => ('' + item.value) === ('' + secondLevel)
                )[0];
                if (indicatorSecondItem) {
                    title += '-' + indicatorSecondItem.label;
                    additionFields = indicatorSecondItem.additionFields;
                    indicatorExternalFilters = indicatorSecondItem.indicatorExternalFilters;
                    reportType = indicatorItem.reportType;
                    indicatorTableSupportCommonFilter = indicatorItem.indicatorTableSupportCommonFilter;
                }
            }
        }

    }
    return {
        title,
        additionFields: additionFields || [],
        indicatorExternalFilters,
        indicatorTableSupportCommonFilter,
        reportType
    };
};

export const getWithSplitColumns = ({columnCategories, targetColumns, splitColumn}) => {
    const newTargetColumns = [...targetColumns];
    // 找出细分列合适的位置 start
    // 思路：
    // 1、找出属性category的列
    const propertyCategoriesList = columnCategories.filter(category => category.name === '属性') || [];
    const propertyColumns
        = (propertyCategoriesList && propertyCategoriesList[0] && propertyCategoriesList[0].columns) || [];
    // 2、找出表格可见列中在属性category的列集合
    const inPropertyColumns = propertyColumns.filter(c => newTargetColumns.indexOf(c) > -1) || [];
    // 3、找出最后一个在属性category的列
    const inPropertyColumnsLen = (inPropertyColumns && inPropertyColumns.length) || 0;
    const lastPropertyColumn = inPropertyColumns && inPropertyColumnsLen > 0
        ? inPropertyColumns[inPropertyColumnsLen - 1]
        : '';
    // 4、找出细分列在表格可见列中的位置，在最后一个在属性category的列后面
    let splitColumnIndex = 0;
    if (lastPropertyColumn) {
        splitColumnIndex = newTargetColumns.indexOf(lastPropertyColumn) || 0;
    }
    // 5、把细分列加进去
    newTargetColumns.splice(splitColumnIndex + 1, 0, splitColumn);
    // 找出细分列合适的位置 end
    return newTargetColumns;
};

// 获得多选的配置
export const getRowSelectionOption = ({
    tableAreaConfig = {},
    tableIndicatorKeyList = []
}) => {
    const {
        tableReportType,
        tableIndicatorList,
        defaultIndicator = [],
        rowSelectionOption = {}
    } = tableAreaConfig;
    const lastestRowSelectionOption = tableIndicatorList && tableIndicatorList.length > 0
        ? rowSelectionOption[tableIndicatorKeyList[0] || defaultIndicator[0] || tableReportType]
        : rowSelectionOption;
    return lastestRowSelectionOption || {};
};

export const logDataFetchFail = (fire, serviceType, reportType) => {
    fire('sdk.event.enter.log', {
        hitType: 'table-data-fail',
        extra: {
            serviceType,
            reportType
        }
    });
};

export const logDataFetched = (fire, serviceType, reportType, fetchStatus) => {
    fire('sdk.event.enter.log', {
        hitType: 'table-data-fetched',
        extra: {
            serviceType,
            reportType,
            'fetch_status': fetchStatus.status,
            'fetch_code': fetchStatus.code || 0
        }
    });
};

export const getMemoPageSize = (userId, reportType) => {
    return localStorage.getItem(`report-${reportType}-page-size-${userId}`);
};
export const setMemoPageSize = (userId, reportType, pageSize) => {
    try {
        localStorage.setItem(`report-${reportType}-page-size-${userId}`, pageSize);
    } catch (e) {}
};