import {isNull} from 'lodash';

// 获取表格数据请求参数
export const getChartsDataParams = (paramRaw, columns, chartConfig, isIndicatorBtn = false) => {
    const {
        pageNo = 1,
        pageSize = 10000,
        sortRule,
        commonFilterInfo
    } = paramRaw;
    const {sortName} = sortRule || {};
    const isAscend = sortRule && sortRule[sortName] && sortRule[sortName].isAscend;
    // 表格之外的筛选项
    const {
        startDate = '2020-08-10',
        endDate = '2020-08-30',
        compareStartDate,
        timeUnit = 'DAY',
        userIds = ['27892978'],
        filterFields = []
    } = commonFilterInfo;
    const {extraStaticFilters = [], chartReportType: reportType, isAddZeroRows} = chartConfig;
    // 筛选项
    const filters = [
        ...filterFields.filter(({values}) => values && values[0] !== 'markAdditionField'),
        ...extraStaticFilters
    ];
    const sort = {
        column: sortName,
        sortRule: isAscend ? 'ASC' : 'DESC'
    };
    const sorts = sortName ? [sort] : [];
    if (['HOUR_IN_DAY', 'HOUR'].includes(timeUnit)) {
        sorts.push({
            column: 'hour',
            sortRule: 'ASC'
        });
    }

    return {
        reportType,
        startDate,
        endDate,
        compareStartDate,
        timeUnit,
        userIds,
        columns,
        sorts,
        filters,
        startRow: (pageNo - 1) * pageSize,
        rowCount: pageSize || 20,
        needSum: true,
        ...(isAddZeroRows === true && !isIndicatorBtn ? {addZeroRows: true} : {})
    };
};

export const formatApiData = ({summary, rows}) => {
    let datasource = [...rows];

    if (summary && summary.sourceInfo) {
        if (!summary.changeRatio.date) {
            summary.changeRatio.date = '变化率';
        }
        if (!summary.changeValue.date) {
            summary.changeValue.date = '变化量';
        }
        if (!~(summary.changeRatio.date || '').indexOf('全部汇总: ')) {
            summary.changeRatio.date = '全部汇总: ' + summary.changeRatio.date;
        }
        summary.sourceInfo.isSummeryRow = true;
        datasource = datasource.concat(summary);
    } else if (Object.keys(summary || {}).length) {
        summary.isSummeryRow = true;
        if (summary && !~(summary.date || '').indexOf('全部汇总: ')) {
            summary.date = '全部汇总: ' + summary.date;
            datasource = datasource.concat(summary);
        }
    }

    // 是对比
    if (datasource.length && datasource[0].sourceInfo) {
        datasource = datasource.map(item => {
            let {values} = Object;
            let arr = [];
            for (let value of values(item)) {
                arr.push(value);
            }
            if (arr[3] && !arr[3].date) {
                arr[3].date = (arr[2].date || '').replace('变化量', '变化率');
            }
            return arr;
        });
    }

    const isComparedData = Array.isArray(datasource[0]);
    if (isComparedData) {
        datasource = datasource.reduce((ret, item) => {
            const sortedItem = {};

            if (item[0]) {
                Object.keys(item[0]).forEach(key => {
                    sortedItem[key] = [];
                    for (let i = 0; i < item.length; i++) {
                        if (!isNull(item[i][key])) {
                            sortedItem[key].push(item[i][key]);
                        }
                    }
                });

                ret.push(sortedItem);
            }

            return ret;

        }, []);
    } else {
        datasource = datasource.map(item => {
            const sortedItem = {};
            Object.keys(item).forEach(key => {
                const value = item[key];
                if (!isNull(value)) {
                    sortedItem[key] = value;
                }
            });
            return sortedItem;
        });
    }
    const summaryData = datasource.pop() || {};
    return {datasource, summary: summaryData};
};

export const getIndicatorList = ({chartConfig, reportAreaConfig, commonFilterInfo}) => {
    // 请求的指标
    const {indicatorList: indicatorListChart} = chartConfig;
    const {indicatorList: indicatorListArea, isCommonIndicator} = reportAreaConfig;
    let indicatorList = [];
    let defaultIndicator = chartConfig.defaultIndicator;
    // 默认取图表里面的，如果展示公共的则取公共的
    if (indicatorListChart && indicatorListChart.length || typeof indicatorListChart === 'function') {
        indicatorList = indicatorListChart;
    } else if (isCommonIndicator) {
        indicatorList = indicatorListArea;
        defaultIndicator = reportAreaConfig.defaultIndicator;
    }
    if (typeof indicatorList === 'function') {
        indicatorList = indicatorList(commonFilterInfo);
    }
    return {indicatorList, defaultIndicator};
};

export const getColumns = ({
    chartConfig,
    reportAreaConfig,
    commonFilterInfo,
    indicatorList,
    isCompare = false
}) => {
    const {xAxisField, sortField} = chartConfig;
    let columns = [];
    if (!indicatorList) {
        const indicatorListInfo = getIndicatorList({chartConfig, reportAreaConfig, commonFilterInfo});
        indicatorList = indicatorListInfo.indicatorList;
    }
    if (indicatorList.length) {
        columns = indicatorList.reduce((ret, indicator) => {
            const value = indicator.value;
            ret.push(value);
            if (isCompare) {
                ret.push(`${value}Target`);
            }
            return ret;
        }, []);
    } else if (chartConfig.defaultIndicator) {
        columns.push(chartConfig.defaultIndicator);
    } else if (reportAreaConfig.defaultIndicator) {
        columns.push(reportAreaConfig.defaultIndicator);
    }
    if (!columns.includes(xAxisField)) {
        columns.push(xAxisField);
    }
    if (!columns.includes('date')) {
        columns.push('date');
    }
    if (sortField && !columns.includes(sortField)) {
        columns.push(sortField);
    }
    return columns;
};

export const sortFunc = (order, orderBy) => {

    return function (x, y) {
        const symbol = order === 'ascend' ? 1 : -1;
        const a = x[orderBy];
        const b = y[orderBy];

        // 相等，返回0
        if (a === b) {
            return 0;
        }

        if (a === null && b === null) {
            return 0;
        }

        // b是null，desc时排在最后
        if (b === null) {
            return symbol * 1;
        }
        else if (a === null) {
            return symbol * (-1);
        }

        const aIsNumber = !isNaN(a);
        const bIsNumber = !isNaN(b);

        // a, b 都是数字
        if (aIsNumber && bIsNumber) {
            return symbol * (parseFloat(a) - parseFloat(b));
        }

        // a, b 如果有一个能转成数字
        // 能转成数字的永远大。
        if (aIsNumber || bIsNumber) {
            return aIsNumber ? (symbol * 1) : (symbol * -1);
        }

        // 否则就是文字对比
        return symbol * (a + '').localeCompare(b);
    };
};
