import React from 'react';
import ReactDOM from 'react-dom';
import {includes, isString} from 'lodash';
import EventTarget from 'mini-event/EventTarget';
import '@babel/polyfill';
import 'abortcontroller-polyfill/dist/polyfill-patch-fetch';
import {getCombinedConfig} from '../utils/common';
// eslint-disable-next-line
import packageJson from '../../package.json';
import {allowedModes} from '../config/common';
import {setConfig, resetConfig} from '../utils/customConfig';

const {version} = packageJson;

export {default as BaiduTongjiLogger} from '../Logger/BaiduTongji';
export {filterAreaConfig as FiltersConfig} from '../config/default/filterAreaConfig';

export default class Entry extends EventTarget {
    constructor({
        mode,
        hairuo,
        config = {},
        token,
        loggers = []
    }) {
        super();
        if (!includes(allowedModes, mode)) {
            throw new Error('Not allowed mode');
        }
        this.mode = mode;
        this.config = getCombinedConfig(config);
        this.token = token;
        this.loggers = loggers;
        this.hairuo = hairuo;
        this.on('sdk.event.enter.log', ({hitType, fields, extra}) => this.log(hitType, fields, extra));
    }

    async init(target) {
        this.target = target;
        this.targetElement = isString(target) ? document.getElementById(target) : target;
        const sdkProps = {
            eventHub: {
                on: this.on.bind(this),
                once: this.once.bind(this),
                un: this.un.bind(this),
                fire: this.fire.bind(this),
                destroyEvents: this.destroyEvents.bind(this),
                trigger: this.trigger.bind(this) // 同fire方法，自动打log
            },
            config: this.config,
            token: this.token,
            hairuo: this.hairuo
        };
        const {default: ReportSdk} = await import(/* webpackChunkName: "ReportSdk" */ '../components');
        if (this.targetElement) {
            ReactDOM.unmountComponentAtNode(this.targetElement);
        }
        ReactDOM.render(
            <ReportSdk {...sdkProps} />,
            this.targetElement
        );
    }

    trigger(eventName, data, isLog = true) {
        this.fire(eventName, data);
        if (isLog) {
            this.log(eventName, data);
        }
    }
    // 统一日志记录器
    log(hitType, fields, extra = {}) {
        extra = {
            version,
            userId: this.hairuo?.userId,
            origin: location.origin,
            ...extra
        };
        if (process.env.NODE_ENV === 'development') {
            console.warn(hitType, fields, extra);
        }
        this.loggers.forEach(logger => {
            try {
                // 某个 logger 出错不能阻止其他 logger 调用
                logger.send(hitType, fields, extra);
            }
            catch (err) {
                if (process.env.NODE_ENV === 'development') {
                    console.log('log of entry', err);
                }
            }
        });
    }

    destroy() {
        this.fire('sdk.event.entry.willDestroy');
        // todo 销毁事件
        this.fire('sdk.event.entry.destroyed');
        this.destroyEvents();
        ReactDOM.unmountComponentAtNode(this.targetElement);
    }
}
// @deprecated
Entry.injectDependencies = function () {};
Entry.setConfig = setConfig;
Entry.resetConfig = resetConfig;
