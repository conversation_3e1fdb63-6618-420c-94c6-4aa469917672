import React from 'react';
import ReactDOM from 'react-dom';
import EventTarget from 'mini-event/EventTarget';
import '@babel/polyfill';
// eslint-disable-next-line
import pkgInfo from '../../package.json';

export default class Entry extends EventTarget {
    constructor({
        hairuo,
        config = {},
        loggers = [],
        token
    }) {
        super();
        this.config = config;
        this.loggers = loggers;
        this.hairuo = hairuo;
        this.token = token;
        this.on('sdk.event.enter.log', ({hitType, fields, extra}) => this.log(hitType, fields, extra));
    }

    async init(target) {
        this.target = target;
        const sdkProps = {
            eventHub: {
                on: this.on.bind(this),
                once: this.once.bind(this),
                un: this.un.bind(this),
                fire: this.fire.bind(this),
                destroyEvents: this.destroyEvents.bind(this),
                trigger: this.trigger.bind(this) // 同fire方法，自动打log
            },
            config: this.config,
            hairuo: this.hairuo,
            token: this.token
        };
        const {default: ReportSdk} = await import(/* webpackChunkName: "ReportSdkCustom" */ '../components/custom');
        ReactDOM.render(
            <ReportSdk {...sdkProps} />,
            document.getElementById(target)
        );
    }

    trigger(eventName, data, isLog = true) {
        this.fire(eventName, data);
        if (isLog) {
            this.log(eventName, data);
        }
    }
    // 统一日志记录器
    log(hitType, fields, extra = {}) {
        extra = {
            version: pkgInfo.version,
            userId: this.hairuo?.userId,
            origin: location.origin,
            ...extra
        };
        if (process.env.NODE_ENV === 'development') {
            console.warn(hitType, fields, extra);
        }
        this.loggers.forEach(logger => {
            try {
                // 某个 logger 出错不能阻止其他 logger 调用
                logger.send(hitType, fields, extra);
            }
            catch (err) {
                if (process.env.NODE_ENV === 'development') {
                    console.log('log of entry', err);
                }
            }
        });
    }

    destroy() {
        this.fire('sdk.event.entry.willDestroy');
        // todo 销毁事件
        this.fire('sdk.event.entry.destroyed');
        this.destroyEvents();
        const targetNode = document.getElementById(this.target);
        if (targetNode) {
            ReactDOM.unmountComponentAtNode(targetNode);
        }
    }
}
// @deprecated
Entry.injectDependencies = function () {};
