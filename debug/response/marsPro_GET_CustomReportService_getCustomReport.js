/**
 * @file 获取定制报告表格
 */
/* eslint-disable */
module.exports = () => {
  const tpl = require('../tpl');
  const rel = tpl.success();
  rel.data = {
        "reportId":"b9df1c9d-ce2d-4da8-b40b-1a9d033aee5c",
        "reportName":"a_mock_name",
        "customReportType":"CUSTOM",
        "reportType":2154871,
        "appId":0,
        "scheduleTimeUnit":"DAY_IN_WEEK",
        "scheduleTimes":[
            1,
            2
        ],
        "startDate":"2020-12-03",
        "endDate":"2020-12-04",
        "expireTime":"2021-07-30 10:40:49",
        "mails":[
            "<EMAIL>",
            "<EMAIL>"
        ],
        "sendChannels":[
            "MAIL"
        ],
        "selectedUsers":[
            {
                "userId":1,
                "userName":"a_mock_name"
            }
        ],
        "dataRequest":{
            "columns":[
                "date",
                "userId",
                "planId",
                "planNameStatus",
                "click",
                "cost",
                "cpc",
                "impression",
                "conversion"
            ],
            "sorts":[
                {
                    "column":"date",
                    "sortRule":"ASC"
                }
            ],
            "filters":[
                {
                    "column":"feedSubjectEnum",
                    "operator":"IN",
                    "values":[
                        "3"
                    ]
                },
                {
                    "column":"ocpcPayMode",
                    "operator":"IN",
                    "values":['0', '1']
                }
            ],
            "startRow":0,
            "rowCount":100,
            "needSum":true
        }
    };
  return rel;
};
