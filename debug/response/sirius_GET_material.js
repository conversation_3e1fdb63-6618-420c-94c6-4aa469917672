/**
 * @file feed物料列表测试接口
 * <AUTHOR>
 * @date 2020-07-15
 */
/* eslint-disable */
module.exports = () => {
    const tpl = require('../tpl');
    const rel = tpl.success();
    rel.data = [
        {"planid":90243476,"planName":"网站链接_计划n1J_7_13_20:44","isdel":0},
        {"planid":90245388,"planName":"网站链接_计划qmx_7_14_14:23","isdel":0},
        {"planid":90191976,"planName":"网站链接_计划QOg_6_22_18:3","isdel":0},
        {"planid":90191977,"planName":"应用推广_计划Eu4_6_22_18:5","isdel":0},
        {"planid":90245384,"planName":"网站链接_计划WfY_7_14_14:20","isdel":0},
        {"planid":90245385,"planName":"网站链接_计划q8Q_7_14_14:20","isdel":0},
        {"planid":90245386,"planName":"网站链接_计划h9N_7_14_14:21","isdel":0},
        {"planid":90245387,"planName":"网站链接_计划BP9_7_14_14:23","isdel":0},
        {"planid":90245383,"planName":"网站链接_计划rR7_7_14_14:17","isdel":0},
        {"planid":90191975,"planName":"商品_商品目录_计划1AZ_6_22_17:56","isdel":0},
        {"planid":90230511,"planName":"plan7231781","isdel":0},
        {"planid":90245406,"planName":"网站链接_计划6HK_7_14_14:39","isdel":0},
        {"planid":90245401,"planName":"网站链接_计划vpZ_7_14_14:32","isdel":0}
    ];
    return rel;
};