/**
 * @file 获取定制报告结果
 */
/* eslint-disable */
module.exports = () => {
    const tpl = require('../tpl');
    const rel = tpl.success();
    rel.data = {
        "executionStatus" : "SUCCESS",
        "isCleaned" : false,
        "fileUrl" : "https://bj.bcebos.com/v1/custom-report-files-offline/2020.08/02/691f9640-4b16-4756-aedf-96a50562ee20.xlsx?authorization=bce-auth-v1%2F68df6c8658a5479da2794b2bdfc837f1%2F2020-08-03T03%3A25%3A01Z%2F300%2F%2Ff9d53aee77196a65986f15de7aa705e6c340fb07b8ce404be3c9eca9995bb578",
        "previewUrl" : "https://bj.bcebos.com/v1/custom-report-files-offline/2020.08/02/691f9640-4b16-4756-aedf-96a50562ee20.png?authorization=bce-auth-v1%2F68df6c8658a5479da2794b2bdfc837f1%2F2020-08-03T03%3A25%3A01Z%2F300%2F%2Ff08da22b83b38c73f144a6b4e38cdd77899b875089d0633cc2352c115953fb2d"
    };
    return rel;
};