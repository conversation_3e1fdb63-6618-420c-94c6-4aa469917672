/**
 * @file doc本地启动文件配置
 * <AUTHOR>
 * @email <EMAIL>
 */
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const HardSourceWebpackPlugin = require('hard-source-webpack-plugin');
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const paths = require('./paths');
const mockServiceMiddleware = require('./config/mockServiceMiddleware')({debugFilePath: paths.appDebug});
const proxy = require('./config/proxy');
module.exports = {
    mode: 'development',
    devtool: 'source-map',
    entry: {
        main: [
            '@babel/polyfill',
            paths.docIndex
        ]
    },
    output: {
        path: paths.build,
        pathinfo: true,
        filename: 'static/js/[name].bundle.js',
        publicPath: '/'
    },
    module: {
        rules: [
            {
                test: [/\.bmp$/, /\.gif$/, /\.jpe?g$/, /\.png$/],
                loader: 'url-loader',
                options: {
                    limit: 10000,
                    name: 'static/media/[name].[hash:8].[ext]'
                }
            },
            {
                test: /\.m?jsx?$/,
                include: [paths.doc, paths.src],
                loader: 'babel-loader',
                options: {
                    cacheDirectory: true
                }
            },
            {
                test: /\.less$/,
                use: [
                    'css-hot-loader',
                    MiniCssExtractPlugin.loader,
                    'css-loader',
                    {
                        loader: 'less-loader',
                        options: {
                            javascriptEnabled: true
                        }
                    }
                ]
            },
            {
                test: [/\.woff$/, /\.ttf$/, /\.svg$/, /\.eot$/],
                loader: 'file-loader',
                options: {
                    name: 'static/media/[name].[hash:8].[ext]'
                }
            },
            {
                exclude: [
                    /\.html$/,
                    /\.m?jsx?$/,
                    /\.less$/,
                    /\.json$/,
                    /\.bmp$/,
                    /\.gif$/,
                    /\.jpe?g$/,
                    /\.png$/,
                    /\.woff$/,
                    /\.ttf$/,
                    /\.svg$/,
                    /\.eot$/
                ],
                loader: 'file-loader',
                options: {
                    name: 'static/media/[name].[hash:8].[ext]'
                }
            }
        ]
    },
    resolve: {
        extensions: ['.js', '.mjs', '.jsx', '.json'],
        alias: {
            src: paths.src,
            doc: paths.doc,
            common: path.resolve(paths.src, 'common'),
            '@baidu/cube-sdk': path.resolve(paths.classEntry, 'Entry')
        }
    },
    optimization: {
        splitChunks: {
            minChunks: 2,
            cacheGroups: {
                vendor: {
                    test: /@babel|react|react-dom|react-highlight/,
                    name: 'vendor',
                    chunks: 'all'
                }
            }
        }
    },
    plugins: [
        new webpack.DefinePlugin({
            'process.env': {
                STAGE: JSON.stringify(process.env.STAGE || 'development')
            },
            'baseDir': JSON.stringify(__dirname)
        }),
        new webpack.HotModuleReplacementPlugin(),
        new MiniCssExtractPlugin({
            filename: 'static/css/styles.css',
            allChunks: true
        }),
        new HtmlWebpackPlugin({
            filename: 'index.html',
            template: path.resolve(paths.doc, 'index.html'),
            chunks: ['vendor', 'main'],
            minify: {
                removeComments: true,
                collapseWhitespace: false
            },
            chunksSortMode: 'manual'
        }),
        new HardSourceWebpackPlugin()
    ],
    devServer: {
        contentBase: paths.build,
        publicPath: '/',
        watchOptions: {
            ignored: /node_modules\/(?!@baidu\/one-charts)/,
            aggregateTimeout: 300,
            poll: 1000
        },
        host: '0.0.0.0',
        port: 8009,
        disableHostCheck: true,
        hot: true,
        historyApiFallback: {
            index: '/index.html'
        },
        stats: {
            colors: true,
            hash: true,
            timings: true,
            chunks: false
        },
        before(app) {
            app.use(mockServiceMiddleware);
        }
    }
};
