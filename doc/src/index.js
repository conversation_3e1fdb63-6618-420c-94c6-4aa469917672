import {createElement} from 'react';
import {render} from 'react-dom';
import App from './app.jsx';
import './index.less';
renderPage(window.location.hash);
window.onhashchange = function () {
    document.body.scrollTop = document.documentElement.scrollTop = 0;
    renderPage(window.location.hash);
};
// 主渲染
function renderPage(hash) {
    hash = hash.length === 0 ? '' : hash.substr(1, hash.length);
    var props = parseQuery(hash);
    props.changeHash = e => {
        appDispatchHandler('changeHash', {id: e.target.id});
    }
    render(createElement(App, props), document.getElementById('container'));
}
function appDispatchHandler(type, param) {
    switch (type) {
        case 'changeHash':
            var hash = window.location.hash;
            hash = hash.length === 0 ? '' : hash.substr(1, hash.length);
            var obj = parseQuery(hash);
            for (var key in param) {
                if (!param.hasOwnProperty(key)) continue;
                obj[key] = param[key];
            }
            window.location.hash = stringifyQuery(obj);
            break;
        default:
            break;
    }
}
function parseQuery(query) {
    var reg = /([^=&\s]+)[=\s]*([^=&\s]*)/g;
    var obj = {};
    while(reg.exec(query)){
        obj[RegExp.$1] = RegExp.$2;
    }
    return obj;
}
function stringifyQuery(obj) {
    var str = '';
    obj = JSON.parse(JSON.stringify(obj));
    for (var key in obj) {
        if (!obj.hasOwnProperty(key)) continue;
        str += (str.length > 0 ? '&' : '') + key + '=' + obj[key];
    }
    return str;
}
