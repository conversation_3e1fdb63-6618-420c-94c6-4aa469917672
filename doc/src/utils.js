import React from 'react';

export const transToStyledText = (str, flag = true) => {
    if (flag) {
        return str.replace(/{/g, `<span class="gray-block">`)
        .replace(/}/g, `</span>`);
    }
    return str;
};

export const getSpecialColumnsRender = field => {
    return <div>{field && typeof field.map === 'function' && field.map(f => <div style={{lineHeight: '19px'}}>{f}</div>)}</div>;
};

export const nameFormat = ({column, columnInfo, columnName}) => {
    const field = column[columnName];
    if (columnName === 'ideaInfo') {
        column[columnName] = Array.isArray(field) ? field[0] : field;
    } else {
        return Array.isArray(field) ? field[0] : field;
    }
};