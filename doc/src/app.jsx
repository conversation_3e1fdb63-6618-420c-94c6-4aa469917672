import React, {PureComponent, Fragment} from 'react';
import {
    menu, basicMenu, introMenu, feedMenu, fcMenu, shopMenu
} from './config';
import 'src/combined.less';
import './app.less';
const renderLabel = (menu, selectedId, props) => {
    return Object.keys(menu).map(menuId => {
        const menuItem = menu[menuId];
        return (
            <div className="left-menu-item-contrainer" key={menuId}>
                <span
                    id={menuId}
                    onClick={props.changeHash}
                    className={selectedId === menuId ? "left-menu-item-selected" : 'left-menu-item'}
                >
                    {menuItem.label}
                </span>
            </div>
        );
    });
};
export default class App extends PureComponent {

    state = {
        // 全局的时间设置 写业务时应该放在redux里，这里为了简便写在state
        globalDateValue: undefined,
        // 是否打开全局时间
        isGlobalDateChecked: false
    }

    handleGlobalDateChange = e => {
        const {
            globalDefaultDate,
            isGlobalDateChecked
        } = e;

        this.setState({
            globalDefaultDate,
            isGlobalDateChecked
        });
    }

    render() {
        const selectedId = this.props.id || 'index';
        const Main = menu[selectedId] && menu[selectedId].value;
        const isFcReport = fcMenu[selectedId] && fcMenu[selectedId].value;
        const {
            globalDefaultDate,
            isGlobalDateChecked
        } = this.state;
        // 全局时间设置额外的props， 目前只有fc有，如果feed也要加就放开限制
        const globalDateExtraProps = isFcReport ? {
            globalDefaultDate,
            isGlobalDateChecked,
            handleGlobalDateChange: this.handleGlobalDateChange
        } : {};
        return (
            <Fragment>
                <div className="main">
                    <div className="left-menu">
                        <div className="left-menu-title">报表 SDK</div>
                        <div className="left-menu-main">
                            <div className="left-menu-main-sub-title">
                                <span>凤巢报告</span>
                                {renderLabel(fcMenu, selectedId, this.props)}
                            </div>
                        </div>
                    </div>
                    <div className="right-main">
                        {
                            Main
                                ? <Main {...this.props} {...globalDateExtraProps}/>
                                : null
                        }
                    </div>
                </div>
            </Fragment>
        );
    }
}
