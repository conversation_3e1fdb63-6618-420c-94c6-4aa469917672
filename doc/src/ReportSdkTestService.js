/**
 * @file 报表SDK apiService
 * <AUTHOR>
 */

import post from '../../src/services/common/reportSdk/api';

const GET_TABLE_META_DATA_URL = 'marsPro/GET/ReportConfigService/getReportConfig';
const GET_MATERIAL_DATA_URL = 'sirius/GET/material';
const GET_REFRESH_DATA_URL = 'pluto/GET/updatetime';

const defaultConf = {
    paramsBuilder(params) {
        return params;
    },
    responseTransfer(res) {
        return res;
    },
    errorHandler(path, error) {
        // const {eventHub: {fire}} = this.context;
        // fire('sdk.event.enter.log', {
        //     hitType: 'request-error',
        //     fields: {
        //         path,
        //         ...error
        //     }
        // });
        throw error;
    }
};
const uploadConf = {
    ...defaultConf,
    paramsBuilder(params) {
        return params;
    }
};

export default class TestService {
    constructor(context) {
        const {token, hairuo: {userId, optId}} = context;
        const postParams = {
            token,
            userId,
            optId
        };
        // super(postParams);
        this.post = post(postParams);
        this.context = context;
    }

    /**
     * 公共请求发送方法
     *
     * @param {Object} conf 请求conf
     * @param {Object} rawParams 原始请求参数
     * @param {string} url 请求路径
     * @return {Promise} 请求 promise
     */
    postService(conf, rawParams, url) {
        const {paramsBuilder, responseTransfer, errorHandler} = conf;
        const params = paramsBuilder(rawParams);
        return this
            .post(url, params)
            .then(
                res => responseTransfer(res)
            )
            .catch(
                err => errorHandler.call(this, url, err)
            );
    }

    // 以下为接口方法
    // 获取feed物料
    getMaterialData({params, url = GET_MATERIAL_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // feed更新数据时间
    getRefreshDataTime({params, url = GET_REFRESH_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }

    // 模拟获得表格配置
    getTableMetaData({params, url = GET_TABLE_META_DATA_URL}) {
        return this.postService(uploadConf, params, url);
    }
}
