@import "~@baidu/one-ui/lib/index.css";
@import "~@baidu/one-ui-pro/lib/index.css";
* {
    margin: 0;
    padding: 0;
}
body,
html {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}
.main {
    display: flex;
    display: -webkit-flex;
    padding: 30px;
}
.left-menu {
    min-width: 140px;
    border-right: 1px solid #e5e5e5;
    &-title {
        position: relative;
        > span {
            position: relative;
            top: -8px;
            display: inline-block;
            margin-left: 5px;
            font-size: 8px;
            color: #9d9d9d;
        }
    }
    &-main {
        margin-top: 40px;
        &-sub-title {
            > span {
                font-size: 14px;
            }
        }
    }
    &-item-contrainer {
        margin: 15px 0 15px 24px;
        cursor: pointer;
        .left-menu-item {
            font-size: 12px;
            color: #333;
            cursor: pointer;
            &:hover {
                border-bottom: 2px solid #9d9d9d;
            }
            &-selected {
                border-bottom: 2px solid #000;
                font-size: 12px;
            }
        }
    }
}
.right-main {
    padding: 0 20px;

    flex: 1;
    #report-sdk-normal-demo {
        width: 1400px;
        padding: 24px;
        background-color: rgb(246, 247, 250);
    }
}
