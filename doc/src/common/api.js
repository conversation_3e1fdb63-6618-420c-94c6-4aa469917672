import React, {PureComponent} from 'react';
import Highlight from 'react-highlight';
import {transToStyledText} from '../utils';
import apis from '../api';
import './api.less';
export default class Api extends PureComponent {
    constructor(props) {
        super(props);
    }
    render() {
        const {title, apiKey, ApiCustom} = this.props;
        const apiList = apis[apiKey];
        return (
            <div className="api" id="api">
                <div className="api-title">{title}</div>
                {
                    ApiCustom ? <ApiCustom /> : null
                }
                <div className="api-main">
                    {
                        apiList && apiList.length ? (
                            <div className="api-header">
                                <span className="api-header-item">参数</span>
                                <span className="api-header-item">类型</span>
                                <span className="api-header-item">说明</span>
                                <span className="api-header-item">可选值</span>
                                <span className="api-header-item">默认值</span>
                            </div>
                        ) : null
                    }
                    <div className="api-line-main">
                        {
                            apiList.map((api, index) => {
                                return (
                                    <div className="api-line" key={index}>
                                        <span className="api-line-item api-line-item-param">{api.param}</span>
                                        <span className="api-line-item api-line-item-type">{api.type}</span>
                                        <span className="api-line-item api-line-item-desc">
                                            {
                                                typeof api.desc === 'string' ? (
                                                    <Highlight innerHTML={true}>
                                                        {transToStyledText(api.desc, api.descFlag)}
                                                    </Highlight>
                                                ) : api.desc
                                            }
                                        </span>
                                        <span className="api-line-item api-line-item-option">{api.option}</span>
                                        <span className="api-line-item api-line-item-default">{api.default}</span>
                                    </div>
                                );
                            })
                        }
                    </div>
                </div>
            </div>
        ); 
    }
}
