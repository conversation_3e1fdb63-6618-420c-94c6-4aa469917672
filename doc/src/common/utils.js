const parseQuery = (url = location.href) => {
    const queryObj = {};
    const reg = /[?&]([^=&#]+)=([^&#]*)/g;
    let querys = url.match(reg);
    if (querys) {
        for (let i in querys) {
            const query = querys[i].split('=');
            const key = query[0].substr(1);
            const value = query[1];
            queryObj[key] ? queryObj[key] = [].concat(queryObj[key], value) : queryObj[key] = value;
        }
    }
    return queryObj;
};
export const getUseridFromUrl = () => parseQuery()['userid'];