.hljs {
    background: #f9f9f9 !important;
    padding: 20px !important;
    border-top: 1px solid #f1f1f1;
}
.demo {
    margin-top: 30px;
    &-code-operator {
        .new-fc-one-icon-angle-down,
        .new-fc-one-icon-angle-up {
            margin-left: 10px;
        }
    }
    &-instances {
        border: 1px solid #F1F1F1;
        margin-top: 20px;
        padding: 30px;
        >div {
            padding: 20px;
            position: relative;
        }
        &-code-copy {
            position: absolute;
            right: 30px;
            top: 30px;
            cursor: pointer;
            color: #999;
        }
    }
    &-title {
        font-size: 16px;
        font-weight: bold;
    }
    &-desc {
        font-size: 12px;
        color: #666666;
        margin-top: 20px;
    }
    &-code-operator {
        background: #fafafa;
        border: 1px solid #eee;
        border-top: 0;
        cursor: pointer;
        text-align: center;
        line-height: 35px;
        .fenice-icon {
            margin: 0 5px;
        }
        span {
            color: #666;
        }
    }
}
.gray-block {
    background: #f1f1f1;
    color: #666;
    font-size: 12px;
    padding: 5px;
    margin: 0 5px;
}
