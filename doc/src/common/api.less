.api {
    &-header-title {
        font-size: 30px;
        color: #333333;
        margin-top: 30px;
    }
    &-title {
        font-size: 16px;
        color: #333333;
        margin-top: 20px;
    }
    &-main {
        border: 1px solid #eee;
        margin-top: 20px;
        border-bottom: 0;
    }
    &-header {
        display: flex;
        display: -webkit-flex;
        font-weight: bold;
        font-size: 12px;
        color: #797979;
        border-bottom: 1px solid #eee;
        &-item {
            flex: 1;
            padding: 20px;
            display: flex;
            display: -webkit-flex;
            align-items: center;
        }
    }
    &-line {
        display: flex;
        display: -webkit-flex;
        border-bottom: 1px solid #eee;
        &-main {
            color: #797979;
        }
        &-item {
            flex: 1;
            padding: 20px;
            display: flex;
            display: -webkit-flex;
            align-items: center;
            &-desc {
                line-height: 28px;
            }
        }
    }
}
