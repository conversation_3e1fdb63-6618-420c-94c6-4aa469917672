import React, {PureComponent} from 'react';
import Highlight from 'react-highlight';
import {transToStyledText} from '../utils';
import './demo.less';
const compReq = require.context('../components', true, /\.jsx?$/);
const codeReq = require.context('../../rawLoader!../components', true, /\.jsx?$/);
export default class Demo extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            isExpendCode: false
        };
    }
    onCodeExpendChange = () => {
        const isExpendCode = this.state.isExpendCode;
        this.setState({
            isExpendCode: !isExpendCode
        });
    }
    render() {
        const {title, desc, source, id, descFlag} = this.props;
        const isExpendCode = this.state.isExpendCode;
        const operatorText = isExpendCode ? '隐藏代码' : '展开代码';
        const path = `./${id}/demo/${source}.js`;
        const Demo = compReq(path);
        console.log({path, Demo}, 'DemoDemo')
        const code = codeReq(path);
        return (
            <div className="demo" data-demo={source}>
                <div className="demo-title">{title}</div>
                <div className="demo-desc">
                    <Highlight innerHTML>
                        {transToStyledText(desc, descFlag)}
                    </Highlight>
                </div>
                <div className="demo-instances" id="demo-instances" style={{maxWidth: '1500px'}}>
                    <Demo {...this.props} />
                    {
                        this.state.isExpendCode
                            ? (
                                <div className="demo-instances-code-contrainer">
                                    <Highlight language="javascript">
                                        {code}
                                    </Highlight>
                                </div>
                            )
                            : null
                    }
                </div>
                <div className="demo-code-operator" id="demo-code-operator" onClick={this.onCodeExpendChange}>
                    <span>
                        {operatorText}
                    </span>
                </div>
            </div>
        );
    }
}
