import React, {PureComponent} from 'react';
import {But<PERSON>} from '@baidu/one-ui';
import {IconQuestionCircle} from 'dls-icons-react';
import TestService from '../../../ReportSdkTestService';
import ReportSdkEntry from '@baidu/cube-sdk';
import {getSpecialColumnsRender} from '../../../utils';

const hourMap = {
    '0': '0:00 ~ 1:00',
    '1': '1:00 ~ 2:00',
    '2': '2:00 ~ 3:00',
    '3': '3:00 ~ 4:00',
    '4': '4:00 ~ 5:00',
    '5': '5:00 ~ 6:00',
    '6': '6:00 ~ 7:00',
    '7': '7:00 ~ 8:00',
    '8': '8:00 ~ 9:00',
    '9': '9:00 ~ 10:00',
    '10': '10:00 ~ 11:00',
    '11': '11:00 ~ 12:00',
    '12': '12:00 ~ 13:00',
    '13': '13:00 ~ 14:00',
    '14': '14:00 ~ 15:00',
    '15': '15:00 ~ 16:00',
    '16': '16:00 ~ 17:00',
    '17': '17:00 ~ 18:00',
    '18': '18:00 ~ 19:00',
    '19': '19:00 ~ 20:00',
    '20': '20:00 ~ 21:00',
    '21': '21:00 ~ 22:00',
    '22': '22:00 ~ 23:00',
    '23': '23:00 ~ 24:00'
};
const getCookie = name => {
    let arr = null;
    const reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)');

    if (arr = document.cookie.match(reg)) {
        return unescape(arr[2]);
    } else {
        return null;
    }
};
const Operate = (text, column) => {
    if (column.isSummeryRow) {
        return '-';
    }
    return (<div>
        <Button type="text-strong" size="small">
            编辑
        </Button>
        <Button type="text-strong" size="small">
            删除
        </Button>
    </div>);
};
export default class Normal extends PureComponent {
    constructor(props) {
        super(props);
    }

    componentDidMount() {
        this.service = new TestService({
            token: '9a21f7c3-b9e1-11ea-947a-6c92bf28d381',
            hairuo: {
                userId: getCookie('CPID_3'),
                optId: getCookie('CPID_3'),
            }
        });

        const dropdownIndicatorList = [
            {label: '消费', value: 'cost', unit: '元', dataType: {precision: 2}},
            {label: '展现', value: 'impression', unit: '次', dataType: {precision: 0}},
            {label: '点击', value: 'click', unit: '次', dataType: {precision: 0}},
            {label: '平均点击价格', value: 'cpc', unit: '元', dataType: {precision: 2}},
            {label: '平均点击率', value: 'ctr', unit: '%', dataType: {isPercent: true, precision: 2}}
        ];

        const config = {
            mode: 'embed',
            tableReportType: 2208157,
            token: '9a21f7c3-b9e1-11ea-947a-6c92bf28d381',
            hairuo: {
                userId: getCookie('CPID_3'),
                optId: getCookie('CPID_3'),
                token: getCookie('CPTK_3')
            },
            config: {
                withNewCategory: true,
                pageTitle: '账户报告',
                mccInfo: {
                    suportAllOption: true
                },
                filterAreaConfig: {
                    filterList: ['timeUnit', 'device'],
                    moreList: ['targetingType'],
                    datePickerMinValue: '2018/11/01',
                    // 是否展示全局时间的switch开关
                    isShowGlobalDateSwitch: true,
                    hiddenGlobalDateSwitchBtn: true,
                    // 是否开启全局时间，开启时需使用方自行传入 datePickerDefaultValue的开始时间
                    // 业务方使用时需自行监听 sdk.event.globalDate.change 事件， 修改外部的是否开启全局时间isisGlobalDateChecked & 全局时间的globalDateValue
                    // sdkEntry.on('sdk.event.globalDate.change', handler)
                    isGlobalDateChecked: true,
                    datePickerDefaultValue: this.props.globalDefaultDate
                },
                isDatePickerDisabled: true,
                // 是否需要比较
                isNeedCompare: true,
                // 表头筛选配置
                filter: {
                    stringFilterMaxLength: 30
                },
                cellRenderConfig: {
                    contentFormats: {
                        regionFormat: ({column, columnName}) => {
                            const field = column[columnName];
                            return getSpecialColumnsRender(field);
                        }
                    }
                },
                // 图表区配置，数组项对应一个tab
                reportArea: {
                    titleArea: {
                        title: '重点关键词分析',
                        tip: '此处地域分布、时段分布图表不支持时间细分'
                    },
                    // 是否指标对比
                    isIndicatorCompare: true,
                    isCommonTable: true,
                    tabs: [{
                        tabLabel: '整体走势',
                        chartAreaList: [{
                            chartType: 'line',
                            chartReportType: 2208157,
                            // 是否响应时间窗
                            isResponseTimeWindow: 'DAY',
                            // 是否响应时间对比
                            isResponseCompare: true,
                            // 是否显示下拉指标相关
                            isShowIndicator: true,
                            indicatorUiType: 'button',
                            isCompareIndicator: true,
                            // indicatorList: dropdownIndicatorList,
                            indicatorList: [
                                {label: '消费', btnLabel: '总消费', value: 'cost', unit: '元', dataType: {precision: 2}, tip: 'xxx'},
                                {label: '展现', btnLabel: '总展现', value: 'impression', unit: '次', dataType: {precision: 0}, tip: <IconQuestionCircle />},
                                {label: '点击', btnLabel: '总点击', value: 'click', unit: '次', dataType: {precision: 0}},
                                {label: '平均点击率', btnLabel: '总点击率', value: 'ctr', unit: '%', dataType: {isPercent: true, precision: 2}},
                                {label: '平均点击价格', btnLabel: '平均点击价格', value: 'cpc', unit: '元', dataType: {precision: 2}}
                            ],
                            // 是否响指标button改变
                            isResponseIndicatorButtonChange: true,
                            // 由函数决定是否展示指标button
                            checkIndicatorButtonShow: () => true,
                            // x轴的字段
                            xAxisField: 'date',
                            // 默认数据的字段，如果有选择指标，也会当作默认选择的指标项
                            defaultIndicator: 'cost'
                        }],
                        // 一期的表格配置,和tableTabList的value对应
                        tableAreaConfig: {
                            // 页面初始化时的表格reportType
                            tableReportType: 2208157,
                            customerFields: [{
                                columnText: '操作',
                                cellRender: Operate
                            }],
                            showFreshData: true,
                            $props: {
                                Table: {size: 'medium'}
                            }
                        }
                    },
                    {
                        tabLabel: '地域分布',
                        chartAreaList: [{
                            chartType: 'bar',
                            titleArea: {
                                title: 'TOP5地域{INDICATOR}'
                            },
                            chartReportType: 2208157,
                            // 是否响应时间窗
                            isResponseTimeWindow: 'SUMMARY',
                            sortType: 'DESC',
                            // 是否响应时间对比
                            isResponseCompare: true,
                            // 是否显示下拉指标相关
                            isShowIndicator: true,
                            indicatorUiType: 'dropdown',
                            // isCompareIndicator: true,
                            indicatorList: dropdownIndicatorList,
                            // 改变指标时是否重新请求
                            isRefreshOnIndicatorChange: true,
                            // x轴的字段
                            xAxisField: 'provinceName',
                            // 默认数据的字段，如果有选择指标，也会当作默认选择的指标项
                            defaultIndicator: 'cost',
                            legendPosition: 'center'
                        }],
                        // 一期的表格配置,和tableTabList的value对应
                        tableAreaConfig: {
                            // 页面初始化时的表格reportType
                            tableReportType: 2208157
                        }
                    },
                    {
                        tabLabel: '时段分布',
                        chartAreaList: [{
                            chartType: 'bar',
                            title: '整体时段分布',
                            // legendPosition: 'center',
                            chartReportType: 2208157,
                            // 是否响应时间对比
                            isResponseCompare: true,
                            // 是否显示下拉指标相关
                            isShowIndicator: true,
                            sortType: 'DESC',
                            // 是否响应时间窗
                            isResponseTimeWindow: 'SUMMARY',
                            indicatorUiType: 'dropdown',
                            indicatorList: dropdownIndicatorList,
                            // 改变指标时是否重新请求
                            isRefreshOnIndicatorChange: true,
                            // 是否响指标button改变
                            isResponseIndicatorButtonChange: true,
                            // x轴的字段
                            xAxisField: 'hour',
                            xAxisFieldFormatter: (value, index) => {
                                return hourMap[value];
                            },
                            // 默认数据的字段，如果有选择指标，也会当作默认选择的指标项
                            defaultIndicator: 'cost',
                            legendPosition: 'center'
                        }],
                        // 一期的表格配置,和tableTabList的value对应
                        tableAreaConfig: {
                            // 页面初始化时的表格reportType
                            tableReportType: 2208157
                        }
                    }]
                }
            }
        };
        this.sdkEntry = new ReportSdkEntry(config);
        this.sdkEntry.init('report-sdk-normal-demo');
        this.sdkEntry.on('sdk.event.filter.filterChanged', this.handleFilterChanged);
        this.sdkEntry.on('sdk.event.filter.sorterChange', this.handleSorterChange);
        this.sdkEntry.on('sdk.event.table.email.send', this.handleEmailSend);
        // 调用外部方法或者redux方法修改全局时间
        this.props.handleGlobalDateChange
            && this.sdkEntry.on('sdk.event.globalDate.change', this.props.handleGlobalDateChange);

        setTimeout(() => {
            this.sdkEntry.fire('sdk.event.table.email', {data: '<EMAIL>'});
        }, 1000);
    }

    handleFilterChanged = filterData => {
        console.log('filterChange pop out');
        console.log(filterData);
    }

    handleEmailSend = () => {
        console.log('email send successful');
    }

    handleSorterChange = sorterData => {
        console.log('sorterChange pop out');
        console.log(sorterData);
    }

    render() {
        return (
            <div style={{backgroundColor: '#f6f7fa'}}>
                <div id="report-sdk-normal-demo"></div>
            </div>
        );
    }
}
