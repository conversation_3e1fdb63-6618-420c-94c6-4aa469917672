import React from 'react';

import Title from '../../common/title';
// eslint-disable-next-line import/no-cycle
import {menu} from '../../config';
import './style.less';

export default props => {
    // eslint-disable-next-line react/prop-types
    const id = props.id || 'index';
    const {label: title} = menu[id] || {};
    return (
        <div>
            <Title title={title} />
            <div className="demo-home-page-title">报表 SDK是基于商业平台打造的一套报表页面解决方案</div>
            <div className="demo-home-page-title">
                当前版本号
            </div>
            <div className="demo-home-page-title">
                0.0.1
            </div>
            <div className="demo-home-page-title">如何安装</div>
            <div className="demo-home-page-title">
                npm install @baidu/cube-sdk --registry=http://registry.npm.baidu-int.com
            </div>
        </div>
    );
};
