import React from 'react';
import {menu} from '../config';
import Title from '../common/title';
import Demo from '../common/demo';
import Api from '../common/api';
export default props => {
    const id = props.id;
    const {label: title, desc, desginLink, demos, apis} = menu[id] || {};
    console.log(demos, Demo, 'demos')
    return (<div>
        <Title title={title} desc={desc} desginLink={desginLink} />
        {
            demos.map((demo, index) => {
                const {title, desc, descFlag} = demo;
                return (<Demo
                    {...props} key={index} title={title}
                    desc={desc} source={demo.source} descFlag={descFlag}
                />);
            })
        }
        <div className="api-header-title" id="api-header-title">API</div>
        {
            apis.map((api, index) => {
                return (<Api key={index} title={api.title} apiKey={api.apiKey} ApiCustom={api.CustomContent || null}/>);
            })
        }
    </div>);
};
