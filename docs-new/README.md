# 报表 SDK 文档站点

> 使用新的 Doc SDK 构建，从原有 doc 目录迁移而来

## 🚀 快速开始

### 安装依赖

```bash
npm install @baidu/doc-sdk
```

### 本地开发

```bash
# 启动开发服务器
npm run dev

# 或者直接打开 index.html
open index.html
```

### 构建部署

```bash
npm run build
```

## 📁 项目结构

```
docs-new/
├── index.html          # 入口HTML文件
├── index.js            # 主要配置和初始化
├── components/         # 组件目录
│   └── fcAccountReport/
│       └── demo/
│           └── normal.js
├── package.json        # 项目配置
└── README.md          # 说明文档
```

## 🔄 迁移说明

### 主要变化

1. **架构升级**: 从原有的自定义文档系统迁移到标准化的 Doc SDK
2. **配置统一**: 所有配置集中在 `index.js` 中管理
3. **组件注册**: 通过全局变量注册组件、代码和API文档
4. **样式优化**: 使用新的主题系统，支持更好的定制化

### 迁移对比

#### 原有结构 (doc/)
```
doc/
├── src/
│   ├── config.js       # 配置文件
│   ├── app.jsx         # 主应用
│   ├── components/     # 组件目录
│   └── common/         # 公共组件
├── index.html
└── rawLoader.js
```

#### 新结构 (docs-new/)
```
docs-new/
├── index.js            # 统一配置和初始化
├── index.html          # 简化的HTML入口
└── components/         # 迁移的组件
```

### 配置迁移

#### 原有配置方式
```javascript
// doc/src/config.js
export const menu = {
    ...fcAccountReportConfig,
};

export const fcMenu = {
    ...fcAccountReportConfig,
};
```

#### 新配置方式
```javascript
// docs-new/index.js
const docSdk = new DocSDK({
  title: '报表 SDK',
  components: {
    'fcAccountReport': {
      label: 'fc账户报告',
      demos: [...],
      apis: [...]
    }
  }
});
```

### 组件注册

#### 原有方式
```javascript
// 通过 require.context 动态加载
const compReq = require.context('../components', true, /\.jsx?$/);
const codeReq = require.context('../../rawLoader!../components', true, /\.jsx?$/);
```

#### 新方式
```javascript
// 全局变量注册
window.__DOC_SDK_DEMOS__ = {
  'fcAccountReport': {
    'normal': FcAccountReportDemo
  }
};

window.__DOC_SDK_DEMO_CODES__ = {
  'fcAccountReport': {
    'normal': `// 源代码字符串`
  }
};
```

## 🎨 主题定制

新的 Doc SDK 支持更灵活的主题定制：

```javascript
const docSdk = new DocSDK({
  theme: {
    name: 'default',
    colors: {
      primary: '#1890ff',
      success: '#52c41a',
      warning: '#faad14',
      error: '#ff4d4f'
    },
    typography: {
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }
  }
});
```

## 📖 功能特性

### ✅ 已迁移功能

- [x] 组件展示和文档
- [x] Demo 代码展示和折叠
- [x] API 文档表格
- [x] 导航和路由
- [x] 搜索功能
- [x] 响应式布局

### 🚧 待完善功能

- [ ] 完整的组件依赖导入
- [ ] 样式文件迁移
- [ ] 构建配置优化
- [ ] 单元测试迁移

## 🛠️ 开发指南

### 添加新组件

1. 在 `components/` 目录下创建组件文件夹
2. 添加 demo 文件
3. 在 `index.js` 中注册组件

```javascript
// 1. 导入组件
import NewComponentDemo from './components/newComponent/demo/basic.js';

// 2. 注册到全局变量
window.__DOC_SDK_DEMOS__.newComponent = {
  'basic': NewComponentDemo
};

// 3. 添加到配置
const docSdk = new DocSDK({
  components: {
    'newComponent': {
      label: '新组件',
      demos: [{ title: '基础用法', source: 'basic' }]
    }
  }
});
```

### 自定义样式

可以通过主题系统或直接添加 CSS 来自定义样式：

```javascript
// 主题方式
theme: {
  components: {
    demo: `
      .doc-demo {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      }
    `
  }
}
```

## 🔧 故障排除

### 常见问题

1. **组件无法显示**: 检查是否正确注册到 `window.__DOC_SDK_DEMOS__`
2. **代码高亮失效**: 确保引入了 highlight.js
3. **样式异常**: 检查主题配置和 CSS 变量

### 调试技巧

```javascript
// 开启调试模式
const docSdk = new DocSDK({
  debug: true,
  // ...其他配置
});

// 查看注册的组件
console.log(window.__DOC_SDK_DEMOS__);
console.log(window.__DOC_SDK_DEMO_CODES__);
console.log(window.__DOC_SDK_APIS__);
```

## 📚 相关文档

- [Doc SDK 官方文档](../doc-sdk/README.md)
- [原有 doc 目录说明](../doc/README.md)
- [迁移工具使用](../doc-sdk/tools/migrate.js)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

MIT License
