/**
 * fc账户报告 - 普通报表示例
 * 从原有doc目录迁移并适配新的Doc SDK
 */

import React, { PureComponent } from 'react';

// 模拟原有的依赖（在实际项目中需要正确导入）
const Button = ({ children, type, size, onClick }) => (
  <button 
    onClick={onClick}
    style={{
      padding: size === 'small' ? '4px 8px' : '8px 16px',
      backgroundColor: type === 'text-strong' ? 'transparent' : '#1890ff',
      color: type === 'text-strong' ? '#1890ff' : 'white',
      border: type === 'text-strong' ? 'none' : '1px solid #1890ff',
      borderRadius: '4px',
      cursor: 'pointer',
      marginRight: '8px'
    }}
  >
    {children}
  </button>
);

const IconQuestionCircle = () => <span>❓</span>;

// 模拟测试服务
class TestService {
  constructor(config) {
    this.config = config;
  }
}

// 模拟报表SDK入口
class ReportSdkEntry {
  constructor(config) {
    this.config = config;
    this.events = {};
  }
  
  init(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
      container.innerHTML = `
        <div style="padding: 20px; border: 1px solid #d9d9d9; border-radius: 6px; background: #fff;">
          <h3 style="margin-bottom: 16px; color: #262626;">账户报告演示</h3>
          <div style="margin-bottom: 16px;">
            <div style="display: inline-block; margin-right: 16px; padding: 8px 12px; background: #f0f0f0; border-radius: 4px;">
              时间范围: 最近7天
            </div>
            <div style="display: inline-block; margin-right: 16px; padding: 8px 12px; background: #f0f0f0; border-radius: 4px;">
              设备: 全部
            </div>
          </div>
          <div style="height: 200px; background: #fafafa; border: 1px dashed #d9d9d9; display: flex; align-items: center; justify-content: center; margin-bottom: 16px;">
            <span style="color: #8c8c8c;">图表区域 (模拟)</span>
          </div>
          <div style="background: #fafafa; border: 1px dashed #d9d9d9; padding: 16px;">
            <div style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 8px; margin-bottom: 8px; font-weight: bold;">
              <div>日期</div>
              <div>消费(元)</div>
              <div>展现(次)</div>
              <div>点击(次)</div>
              <div>操作</div>
            </div>
            <div style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 8px; padding: 8px 0; border-top: 1px solid #f0f0f0;">
              <div>2023-10-01</div>
              <div>1,234.56</div>
              <div>12,345</div>
              <div>567</div>
              <div>
                <button style="margin-right: 4px; padding: 2px 8px; border: none; background: #1890ff; color: white; border-radius: 2px; cursor: pointer;">编辑</button>
                <button style="padding: 2px 8px; border: none; background: #ff4d4f; color: white; border-radius: 2px; cursor: pointer;">删除</button>
              </div>
            </div>
            <div style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 8px; padding: 8px 0; border-top: 1px solid #f0f0f0;">
              <div>2023-10-02</div>
              <div>2,345.67</div>
              <div>23,456</div>
              <div>678</div>
              <div>
                <button style="margin-right: 4px; padding: 2px 8px; border: none; background: #1890ff; color: white; border-radius: 2px; cursor: pointer;">编辑</button>
                <button style="padding: 2px 8px; border: none; background: #ff4d4f; color: white; border-radius: 2px; cursor: pointer;">删除</button>
              </div>
            </div>
          </div>
        </div>
      `;
    }
  }
  
  on(event, handler) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(handler);
  }
  
  fire(event, data) {
    if (this.events[event]) {
      this.events[event].forEach(handler => handler(data));
    }
  }
}

const hourMap = {
  '0': '0:00 ~ 1:00',
  '1': '1:00 ~ 2:00',
  '2': '2:00 ~ 3:00',
  '3': '3:00 ~ 4:00',
  '4': '4:00 ~ 5:00',
  '5': '5:00 ~ 6:00',
  '6': '6:00 ~ 7:00',
  '7': '7:00 ~ 8:00',
  '8': '8:00 ~ 9:00',
  '9': '9:00 ~ 10:00',
  '10': '10:00 ~ 11:00',
  '11': '11:00 ~ 12:00',
  '12': '12:00 ~ 13:00',
  '13': '13:00 ~ 14:00',
  '14': '14:00 ~ 15:00',
  '15': '15:00 ~ 16:00',
  '16': '16:00 ~ 17:00',
  '17': '17:00 ~ 18:00',
  '18': '18:00 ~ 19:00',
  '19': '19:00 ~ 20:00',
  '20': '20:00 ~ 21:00',
  '21': '21:00 ~ 22:00',
  '22': '22:00 ~ 23:00',
  '23': '23:00 ~ 24:00'
};

const getCookie = name => {
  let arr = null;
  const reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)');
  if (arr = document.cookie.match(reg)) {
    return unescape(arr[2]);
  } else {
    return null;
  }
};

const Operate = (text, column) => {
  if (column.isSummeryRow) {
    return '-';
  }
  return (
    <div>
      <Button type="text-strong" size="small">
        编辑
      </Button>
      <Button type="text-strong" size="small">
        删除
      </Button>
    </div>
  );
};

export default class Normal extends PureComponent {
  constructor(props) {
    super(props);
  }

  componentDidMount() {
    // 模拟原有的初始化逻辑
    this.service = new TestService({
      token: '9a21f7c3-b9e1-11ea-947a-6c92bf28d381',
      hairuo: {
        userId: getCookie('CPID_3') || 'demo-user',
        optId: getCookie('CPID_3') || 'demo-opt',
      }
    });

    const dropdownIndicatorList = [
      { label: '消费', value: 'cost', unit: '元', dataType: { precision: 2 } },
      { label: '展现', value: 'impression', unit: '次', dataType: { precision: 0 } },
      { label: '点击', value: 'click', unit: '次', dataType: { precision: 0 } },
      { label: '平均点击价格', value: 'cpc', unit: '元', dataType: { precision: 2 } },
      { label: '平均点击率', value: 'ctr', unit: '%', dataType: { isPercent: true, precision: 2 } }
    ];

    const config = {
      mode: 'embed',
      tableReportType: 2208157,
      token: '9a21f7c3-b9e1-11ea-947a-6c92bf28d381',
      hairuo: {
        userId: getCookie('CPID_3') || 'demo-user',
        optId: getCookie('CPID_3') || 'demo-opt',
        token: getCookie('CPTK_3') || 'demo-token'
      },
      config: {
        withNewCategory: true,
        pageTitle: '账户报告',
        mccInfo: {
          suportAllOption: true
        },
        filterAreaConfig: {
          filterList: ['timeUnit', 'device'],
          moreList: ['targetingType'],
          datePickerMinValue: '2018/11/01',
          isShowGlobalDateSwitch: true,
          hiddenGlobalDateSwitchBtn: true,
          isGlobalDateChecked: true,
          datePickerDefaultValue: this.props.globalDefaultDate
        },
        isDatePickerDisabled: true,
        isNeedCompare: true,
        filter: {
          stringFilterMaxLength: 30
        },
        reportArea: {
          titleArea: {
            title: '重点关键词分析',
            tip: '此处地域分布、时段分布图表不支持时间细分'
          },
          isIndicatorCompare: true,
          isCommonTable: true,
          tabs: [{
            tabLabel: '整体走势',
            chartAreaList: [{
              chartType: 'line',
              chartReportType: 2208157,
              isResponseTimeWindow: 'DAY',
              isResponseCompare: true,
              isShowIndicator: true,
              indicatorUiType: 'button',
              isCompareIndicator: true,
              indicatorList: [
                { label: '消费', btnLabel: '总消费', value: 'cost', unit: '元', dataType: { precision: 2 }, tip: 'xxx' },
                { label: '展现', btnLabel: '总展现', value: 'impression', unit: '次', dataType: { precision: 0 }, tip: <IconQuestionCircle /> },
                { label: '点击', btnLabel: '总点击', value: 'click', unit: '次', dataType: { precision: 0 } },
                { label: '平均点击率', btnLabel: '总点击率', value: 'ctr', unit: '%', dataType: { isPercent: true, precision: 2 } },
                { label: '平均点击价格', btnLabel: '平均点击价格', value: 'cpc', unit: '元', dataType: { precision: 2 } }
              ],
              isResponseIndicatorButtonChange: true,
              checkIndicatorButtonShow: () => true,
              xAxisField: 'date',
              defaultIndicator: 'cost'
            }],
            tableAreaConfig: {
              tableReportType: 2208157,
              customerFields: [{
                columnText: '操作',
                cellRender: Operate
              }],
              showFreshData: true,
              $props: {
                Table: { size: 'medium' }
              }
            }
          }]
        }
      }
    };

    this.sdkEntry = new ReportSdkEntry(config);
    this.sdkEntry.init('report-sdk-normal-demo');
    this.sdkEntry.on('sdk.event.filter.filterChanged', this.handleFilterChanged);
    this.sdkEntry.on('sdk.event.filter.sorterChange', this.handleSorterChange);
    this.sdkEntry.on('sdk.event.table.email.send', this.handleEmailSend);

    if (this.props.handleGlobalDateChange) {
      this.sdkEntry.on('sdk.event.globalDate.change', this.props.handleGlobalDateChange);
    }

    setTimeout(() => {
      this.sdkEntry.fire('sdk.event.table.email', { data: '<EMAIL>' });
    }, 1000);
  }

  handleFilterChanged = filterData => {
    console.log('filterChange pop out');
    console.log(filterData);
  }

  handleEmailSend = () => {
    console.log('email send successful');
  }

  handleSorterChange = sorterData => {
    console.log('sorterChange pop out');
    console.log(sorterData);
  }

  render() {
    return (
      <div style={{ backgroundColor: '#f6f7fa', padding: '20px', borderRadius: '6px' }}>
        <div style={{ marginBottom: '16px', padding: '12px', background: '#e6f7ff', borderRadius: '4px', border: '1px solid #91d5ff' }}>
          <strong>📝 迁移说明:</strong> 此示例已从原有doc目录迁移到新的Doc SDK架构。
          在实际使用中，请确保正确导入所需的依赖包。
        </div>
        <div id="report-sdk-normal-demo"></div>
      </div>
    );
  }
}
