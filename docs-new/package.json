{"name": "cube-sdk-docs", "version": "1.0.0", "description": "报表 SDK 文档站点 - 使用 Doc SDK 构建", "main": "index.js", "scripts": {"dev": "python -m http.server 8080", "dev:node": "npx http-server -p 8080 -c-1", "build": "echo 'Build completed. Files are ready for deployment.'", "preview": "python -m http.server 3000", "lint": "echo 'Linting...'", "test": "echo 'Testing...'"}, "keywords": ["documentation", "sdk", "report", "baidu", "cube"], "author": "Baidu Cube SDK Team", "license": "MIT", "dependencies": {"@baidu/doc-sdk": "file:../doc-sdk"}, "devDependencies": {"http-server": "^14.1.1"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/baidu/cube-sdk.git", "directory": "docs-new"}, "bugs": {"url": "https://github.com/baidu/cube-sdk/issues"}, "homepage": "https://github.com/baidu/cube-sdk#readme"}