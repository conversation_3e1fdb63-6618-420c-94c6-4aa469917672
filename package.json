{"name": "@baidu/cube-sdk", "version": "0.1.51", "description": "报表 SDK", "main": "lib/entry/Entry.js", "module": "es/entry/Entry.js", "sideEffects": ["*.less", "*.css"], "files": ["lib", "es", "src"], "scripts": {"start": "cross-env STAGE=development NODE_ENV=development BABEL_ENV=development webpack-dev-server --debug --config  webpack.dev.js --progress --open", "start:test": "cross-env STAGE=development NODE_ENV=development BABEL_ENV=development webpack-dev-server --debug --config  webpack.dev.js --progress --open --proxy test --proxyMode whole", "start:preonline": "cross-env STAGE=development NODE_ENV=development BABEL_ENV=development webpack-dev-server --debug --config  webpack.dev.js --progress --open --proxy preonline --proxyMode whole", "start:online": "cross-env STAGE=development NODE_ENV=development BABEL_ENV=development webpack-dev-server --debug --config  webpack.dev.js --progress --open --proxy online --proxyMode whole", "clean": "<PERSON><PERSON>f lib dist es", "prepare": "npm run build", "build": "npm run clean && npm run build:cjs && npm run build:es && npm run build:less&& npm run build:cppyjson", "build:cjs": "cross-env BABEL_ENV=production NODE_ENV=production BUILD_TARGET=cjs babel src --out-dir lib", "build:es": "cross-env BABEL_ENV=production NODE_ENV=production BUILD_TARGET=es babel src --out-dir es", "build:less": "node bin/css", "build:cppyjson": "node bin/copyJson", "lint": "npm run lint:es && npm run lint:style", "lint:es": "eslint src --ext '.js,.jsx'", "lint:style": "stylelint \"src/**/*.less\" --syntax less", "lint-staged": "lint-staged", "lint-staged:es": "eslint"}, "lint-staged": {"linters": {"{src,lib}/**/*.{js,jsx}": ["eslint"], "src/**/*.less": ["stylelint --syntax less"]}}, "pre-commit": ["lint"], "repository": {"type": "git", "url": ""}, "keywords": ["dls", "react"], "license": "ISC", "dependencies": {"@babel/polyfill": "^7.4.4", "@baidu/guid": "^1.0.1", "@baidu/one-charts": ">=0.0.20", "@baidu/one-ui-icon": "^4.6.0", "abortcontroller-polyfill": "^1.7.3", "big.js": "^6.1.1", "chroma-js": "^2.1.0", "classnames": "^2.2.5", "dayjs": "^1.10.7", "dls-icons-react": "^3.3.0", "dls-illustrations-react": "^1.3.2", "enum": "^3.0.4", "immer": "^9.0.7", "less-plugin-dls": "1.0.0-alpha.27", "lodash": "^4.17.15", "mini-event": "^2.3.0", "prop-types": "^15.6.0", "user": "^0.0.0"}, "peerDependencies": {"@baidu/light-ai-react": ">=1.15.1", "@baidu/one-charts": ">=0.0.20", "@baidu/one-ui": ">=4.0.0", "dls-graphics": ">=1.0.0-alpha.1", "react": ">16.0.0", "react-dom": ">16.0.0"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/preset-env": "^7.4.4", "@babel/preset-react": "^7.0.0", "@baidu/light-ai-react": "1.15.2-alpha.7", "@baidu/one-ui": "npm:@baidu/one-ui@4.41.6", "@baidu/one-ui-pro": "0.2.0", "@baidu/winds-ajax": "1.0.20", "@ecomfe/eslint-config": "^7.4.0", "@huse/request": "^1.2.1", "babel-eslint": "^10.1.0", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-import": "^1.13.0", "chalk": "^2.4.1", "cross-env": "^5.2.0", "css-hot-loader": "^1.4.4", "css-loader": "^2.1.1", "dls-graphics": "^1.0.0-alpha.2", "eslint": "^7.5.0", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-hooks": "^4.2.0", "file-loader": "^3.0.1", "hard-source-webpack-plugin": "^0.13.1", "html-webpack-plugin": "^4.0.1", "http-proxy": "^1.16.2", "http-proxy-middleware": "^0.17.4", "less": "^3.9.0", "less-loader": "^5.0.0", "less-plugin-npm-import": "^2.1.0", "lint-staged": "^8.1.7", "mini-css-extract-plugin": "^0.6.0", "mockservice": "^0.1.14", "pre-commit": "^1.2.2", "react": "^17.0.2", "react-dom": "^17.0.1", "react-highlight": "^0.12.0", "rimraf": "^2.6.3", "speed-measure-webpack-plugin": "^1.5.0", "style-loader": "^0.23.1", "stylelint": "^10.0.1", "stylelint-config-standard": "^18.3.0", "superagent": "^3.8.0", "url-loader": "^1.1.2", "url-parse": "1.5.1", "webpack": "^4.31.0", "webpack-cli": "^3.3.2", "webpack-dev-server": "^3.3.1"}}