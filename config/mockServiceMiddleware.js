/**
 * @file    mock 接口中间件
 * <AUTHOR>
 */

/* eslint-disable import/unambiguous */
const url = require('url');
const chalk = require('chalk');
const mockService = require('mockservice');
const {isProxyRemote, proxyServer} = require('./proxy');

module.exports = ({debugFilePath} = {}) => {
    mockService.config({
        name: 'report-sdk',
        dir: debugFilePath // paths.appDebug
    });

    const proxyRequest = (request, response) => {
        const parsedUrl = url.parse(request.url, true);
        const path = parsedUrl.query.path;

        if (isProxyRemote(path)) {
            // eslint-disable-next-line no-console
            console.log(chalk.bgGreen(`[REMOTE DEBUG] PATH: ${path}`));
            proxyServer.web(request, response);
            return;
        }
        const serve = mockService.serve;
        request.query = parsedUrl.query;
        const data = [];
        request.on('data', trunk => {
            data.push(trunk && trunk.toString());
        });
        request.on('end', trunk => {
            if (trunk) {
                data.push(trunk.toString());
            }
            request.body = data.join('');
            serve(request, response);
        });
    };

    const auth = (request, response) => {
        const parsedUrl = url.parse(request.url, true);
        if (!isProxyRemote(parsedUrl.pathname)) {
            const exp = 'Wed, 23 Jul 2015 02:52:19 GMT';
            response.cookie('__cas__rn__', `kslf_cas_rn_lld;path=/;expires=${exp}`);
            response.cookie('__cas__st__3', 'kslf_cas_st_3_fcfe;path=/');
            response.cookie('__cas__id__3', '630152');
            response.cookie('CPTK_3', 'CPTK_3_fcfe;path=/');
            response.cookie('CPID_3', '630152');
            // response.redirect(path);
        } else {
            proxyServer.web(request, response);
        }
    };

    return (req, res, next) => {
        const parsedUrl = url.parse(req.url, true);
        if (/^\/hairuo\/request.ajax$/.test(parsedUrl.pathname) && req.method === 'POST') {
            proxyRequest(req, res);
        }
        else if (/^\/nirvana\/request.ajax$/.test(parsedUrl.pathname) && req.method === 'POST') {
            proxyRequest(req, res);
        } else if (/^\/hairuo\/main.do$/.test(parsedUrl.pathname) && req.method === 'GET') {
            auth(req, res);
        } else {
            next();
        }
    };
};
