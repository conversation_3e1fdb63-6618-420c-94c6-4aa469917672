/**
 * @file    代理
 * <AUTHOR>
 */
/* eslint-disable import/unambiguous */
const http = require('http');
const https = require('https');
const httpProxy = require('http-proxy');
const chalk = require('chalk');
const {
    // proxyToEnum,
    proxyModeEnum,
    proxyTo,
    proxyMode,
    pathList,
    proxyToTestPort
} = require('./proxy.config.js');

const proxyToConfig = {
    /**
     * feed测试环境
     */
    // TEST: {
    //     proxyUrl: `http://fcfeed.baidu.com:${proxyToTestPort}`,
    //     proxyHost: 'fcfeed.baidu.com',
    //     devHost: 'dev.fcfeed.baidu.com',
    //     agent: http.globalAgent
    // },
    /**
     * fc测试环境
     */
    TEST: {
        proxyUrl: `http://fctest.baidu.com:${proxyToTestPort}`,
        proxyHost: 'fctest.baidu.com',
        devHost: 'dev.fctest.baidu.com',
        agent: http.globalAgent
    },
    /**
     * feed预上线环境
     */
    // PREONLINE: {
    //     proxyUrl: 'http://feedads.baidu.com',
    //     proxyHost: 'feedads.baidu.com',
    //     devHost: 'dev.feedads.baidu.com',
    //     agent: http.globalAgent
    // },
    /**
     * fc预上线环境
     */
    PREONLINE: {
        proxyUrl: 'http://fengchao.baidu.com',
        proxyHost: 'fengchao.baidu.com',
        devHost: 'dev.fengchao.baidu.com',
        agent: http.globalAgent
    },
    /**
     * 线上环境
     */
    ONLINE: {
        proxyUrl: 'https://fengchao.baidu.com',
        proxyHost: 'fengchao.baidu.com',
        devHost: 'dev.fengchao.baidu.com',
        agent: https.globalAgent
    }
};

const isInPathList = path => pathList.some(rule => {
    return rule instanceof RegExp ? rule.test(path) : rule === path;
});

const isProxyRemote = path => (proxyMode === proxyModeEnum.WHOLE)
    || (proxyMode === proxyModeEnum.WHITELIST && isInPathList(path))
    || (proxyMode === proxyModeEnum.BLACKLIST && !isInPathList(path));


const {proxyUrl, devHost, agent, proxyHost} = proxyToConfig[proxyTo.key];
const proxyServer = httpProxy.createProxyServer({
    target: proxyUrl,
    changeOrigin: true,
    agent,
    headers: {
        host: proxyHost,
        origin: proxyUrl,
        // referer暂时写成固定的，需要调整
        referer: `${proxyUrl}/fc`
    }
});

proxyServer.on('error', e => {
    // eslint-disable-next-line no-console
    console.log(chalk.red('[proxy error, error]'), e);
});

module.exports = {
    isProxyRemote,
    proxyServer,
    host: devHost
};
