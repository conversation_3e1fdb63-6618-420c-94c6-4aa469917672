/**
 * @file 代理配置
 * <AUTHOR>
 * @date 2018-02-07
 */

const Enum = require('enum');

const proxyToEnum = new Enum({
    /*
        测试环境
     */
    TEST: 0,
    /*
        预上线环境
     */
    PREONLINE: 1,
    /*
        线上环境
    */
    ONLINE: 2
});

const proxyModeEnum = new Enum({
    /*
        全量代理
     */
    WHOLE: 0,
    /*
        白名单模式
     */
    WHITELIST: 1,
    /*
        黑名单模式
     */
    BLACKLIST: 2
});


/*
 * proxyTo: TEST or PREONLINE or ONLINE
 * proxyMode: WHOLE, WHITELIST or BLACKLIST
 * proxyToTestPort: 代理至fctest环境的端口号
*/
const PROXY = process.env.PROXY || 'PREONLINE';
const proxyTo = proxyToEnum[PROXY];
const proxyMode = proxyModeEnum.WHITELIST;
// const proxyTo = proxyToEnum.TEST;
// const proxyMode = proxyModeEnum.WHOLE;
const pathList = ['/request.ajax'];
const proxyToTestPort = 8175;

const config = {
    proxyToEnum,
    proxyModeEnum,
    proxyTo,
    proxyMode,
    pathList,
    proxyToTestPort
};

const formatProxyConfig = config => {
    const proxyIndex = process.argv.lastIndexOf('--proxy');
    const modeIndex = process.argv.lastIndexOf('--proxyMode');
    const portIndex = process.argv.lastIndexOf('--port');
    if (proxyIndex >= 0) {
        config.proxyTo = proxyToEnum[process.argv[proxyIndex + 1].toUpperCase()];
    }
    if (modeIndex >= 0) {
        config.proxyMode = proxyModeEnum[process.argv[modeIndex + 1].toUpperCase()];
    }
    if (portIndex >= 0) {
        config.proxyToTestPort = process.argv[portIndex + 1];
    }
    return config;
};

const proxyConfig = formatProxyConfig(config);

module.exports = proxyConfig;

