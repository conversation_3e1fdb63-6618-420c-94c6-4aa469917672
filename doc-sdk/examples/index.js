/**
 * Doc SDK 示例
 */

import React from 'react';
import DocSD<PERSON> from '../src';

// 示例组件
const ButtonDemo = () => {
  return (
    <div style={{ padding: '20px' }}>
      <button 
        style={{
          padding: '8px 16px',
          backgroundColor: '#1890ff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
        onClick={() => alert('Button clicked!')}
      >
        点击我
      </button>
    </div>
  );
};

const InputDemo = () => {
  const [value, setValue] = React.useState('');
  
  return (
    <div style={{ padding: '20px' }}>
      <input
        type="text"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        placeholder="请输入内容"
        style={{
          padding: '8px 12px',
          border: '1px solid #d9d9d9',
          borderRadius: '4px',
          fontSize: '14px'
        }}
      />
      <p style={{ marginTop: '10px', color: '#666' }}>
        输入的内容: {value}
      </p>
    </div>
  );
};

// 注册全局组件和代码
window.__DOC_SDK_DEMOS__ = {
  'button': {
    'basic': ButtonDemo,
    'advanced': ButtonDemo
  },
  'input': {
    'basic': InputDemo
  }
};

window.__DOC_SDK_DEMO_CODES__ = {
  'button': {
    'basic': `import React from 'react';

const ButtonDemo = () => {
  return (
    <div style={{ padding: '20px' }}>
      <button 
        style={{
          padding: '8px 16px',
          backgroundColor: '#1890ff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
        onClick={() => alert('Button clicked!')}
      >
        点击我
      </button>
    </div>
  );
};

export default ButtonDemo;`,
    'advanced': `// 高级按钮示例
import React from 'react';

const AdvancedButton = () => {
  return (
    <div>
      <button>高级按钮</button>
    </div>
  );
};

export default AdvancedButton;`
  },
  'input': {
    'basic': `import React, { useState } from 'react';

const InputDemo = () => {
  const [value, setValue] = useState('');
  
  return (
    <div style={{ padding: '20px' }}>
      <input
        type="text"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        placeholder="请输入内容"
        style={{
          padding: '8px 12px',
          border: '1px solid #d9d9d9',
          borderRadius: '4px',
          fontSize: '14px'
        }}
      />
      <p style={{ marginTop: '10px', color: '#666' }}>
        输入的内容: {value}
      </p>
    </div>
  );
};

export default InputDemo;`
  }
};

window.__DOC_SDK_APIS__ = {
  'button': {
    'Button': [
      {
        param: 'type',
        type: 'string',
        desc: '按钮类型',
        option: 'primary | secondary | danger',
        default: 'primary',
        required: false
      },
      {
        param: 'size',
        type: 'string',
        desc: '按钮大小',
        option: 'small | medium | large',
        default: 'medium',
        required: false
      },
      {
        param: 'disabled',
        type: 'boolean',
        desc: '是否禁用',
        option: 'true | false',
        default: 'false',
        required: false
      },
      {
        param: 'onClick',
        type: '(event: MouseEvent) => void',
        desc: '点击事件处理函数',
        option: '',
        default: '',
        required: false
      }
    ]
  },
  'input': {
    'Input': [
      {
        param: 'value',
        type: 'string',
        desc: '输入框的值',
        option: '',
        default: '',
        required: false
      },
      {
        param: 'placeholder',
        type: 'string',
        desc: '占位符文本',
        option: '',
        default: '',
        required: false
      },
      {
        param: 'disabled',
        type: 'boolean',
        desc: '是否禁用',
        option: 'true | false',
        default: 'false',
        required: false
      },
      {
        param: 'onChange',
        type: '(event: ChangeEvent) => void',
        desc: '值变化时的回调',
        option: '',
        default: '',
        required: false
      }
    ]
  }
};

// 创建文档SDK实例
const docSdk = new DocSDK({
  title: 'Component Library',
  description: '一个基于React的组件库文档',
  version: '1.0.0',
  
  // 主题配置
  theme: {
    name: 'default'
  },
  
  // 布局配置
  layout: {
    type: 'sidebar',
    sidebar: {
      width: 280,
      collapsible: true
    }
  },
  
  // 组件配置
  components: {
    'button': {
      label: 'Button 按钮',
      description: '按钮用于开始一个即时操作。',
      demos: [
        {
          title: '基础用法',
          desc: '最简单的用法。',
          source: 'basic'
        },
        {
          title: '高级用法',
          desc: '更复杂的{按钮}用法示例。',
          source: 'advanced'
        }
      ],
      apis: [
        {
          title: 'Button',
          apiKey: 'Button'
        }
      ]
    },
    'input': {
      label: 'Input 输入框',
      description: '通过鼠标或键盘输入内容，是最基础的表单域的包装。',
      demos: [
        {
          title: '基础用法',
          desc: '基础的输入框用法。',
          source: 'basic'
        }
      ],
      apis: [
        {
          title: 'Input',
          apiKey: 'Input'
        }
      ]
    }
  },
  
  // 首页配置
  installation: 'npm install @your-org/component-library',
  usage: `import { Button, Input } from '@your-org/component-library';

function App() {
  return (
    <div>
      <Button type="primary">Hello World</Button>
      <Input placeholder="Enter text" />
    </div>
  );
}`,
  
  features: [
    {
      icon: '🎨',
      title: '主题定制',
      description: '支持深度的主题定制，满足企业级产品的个性化UI需求。'
    },
    {
      icon: '📱',
      title: '移动设备友好',
      description: '组件经过移动设备优化，保证良好的手机和平板体验。'
    },
    {
      icon: '🌍',
      title: '国际化',
      description: '提供完备的国际化语言支持，轻松实现多语言切换。'
    },
    {
      icon: '⚡',
      title: '开箱即用',
      description: '高质量的React组件，开箱即用，提升开发效率。'
    }
  ],
  
  // 搜索配置
  search: {
    enabled: true,
    placeholder: '搜索组件...'
  }
});

// 渲染文档
docSdk.render('#app');
