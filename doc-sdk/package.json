{"name": "@baidu/doc-sdk", "version": "1.0.0", "description": "A powerful documentation rendering SDK for component libraries", "main": "lib/index.js", "module": "es/index.js", "types": "lib/index.d.ts", "sideEffects": ["*.css", "*.less"], "files": ["lib", "es", "src", "bin", "tools", "README.md"], "bin": {"doc-sdk": "./bin/doc-sdk.js"}, "scripts": {"dev": "webpack serve --config webpack.dev.js", "build": "npm run clean && npm run build:cjs && npm run build:es && npm run build:types", "build:cjs": "cross-env BABEL_ENV=production NODE_ENV=production BUILD_TARGET=cjs babel src --out-dir lib --extensions .js,.jsx,.ts,.tsx", "build:es": "cross-env BABEL_ENV=production NODE_ENV=production BUILD_TARGET=es babel src --out-dir es --extensions .js,.jsx,.ts,.tsx", "build:types": "tsc --emitDeclarationOnly --outDir lib", "clean": "rimraf lib es dist", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepublishOnly": "npm run build"}, "keywords": ["documentation", "component", "react", "sdk", "docs", "demo", "api"], "author": "Doc SDK Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/doc-sdk.git"}, "bugs": {"url": "https://github.com/your-org/doc-sdk/issues"}, "homepage": "https://github.com/your-org/doc-sdk#readme", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"lodash": "^4.17.21", "mini-event": "^2.3.0", "commander": "^11.0.0", "express": "^4.18.0"}, "devDependencies": {"@babel/cli": "^7.22.0", "@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0", "@babel/preset-typescript": "^7.22.0", "@types/lodash": "^4.14.195", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "babel-loader": "^9.1.0", "cross-env": "^7.0.3", "css-loader": "^6.8.0", "eslint": "^8.42.0", "eslint-plugin-react": "^7.32.0", "eslint-plugin-react-hooks": "^4.6.0", "html-webpack-plugin": "^5.5.0", "jest": "^29.5.0", "less": "^4.1.3", "less-loader": "^11.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^5.0.0", "style-loader": "^3.3.0", "typescript": "^5.1.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}, "babel": {"presets": [["@babel/preset-env", {"modules": false, "targets": {"browsers": ["> 1%", "last 2 versions", "not ie <= 8"]}}], "@babel/preset-react", "@babel/preset-typescript"], "env": {"cjs": {"presets": [["@babel/preset-env", {"modules": "cjs"}]]}}}, "eslintConfig": {"extends": ["eslint:recommended", "@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "react", "react-hooks"], "env": {"browser": true, "es6": true, "node": true}, "settings": {"react": {"version": "detect"}}, "rules": {"react/prop-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "warn"}}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}, "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts", "!src/index.js"]}}