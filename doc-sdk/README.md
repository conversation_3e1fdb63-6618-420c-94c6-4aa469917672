# 文档渲染SDK

一个专门用于渲染组件文档站点的SDK，从原有的doc目录抽象而来。

## 特性

- 🎨 **主题化设计** - 支持自定义主题和样式
- 📱 **响应式布局** - 适配各种屏幕尺寸
- 🔧 **组件化架构** - 高度可复用的文档组件
- 📖 **Demo展示** - 支持代码预览、实时运行、代码折叠
- 📋 **API文档** - 结构化的API参数文档
- 🚀 **热更新** - 开发时支持热更新
- 🎯 **插件系统** - 支持自定义插件扩展功能
- 📦 **零配置** - 开箱即用，同时支持深度定制

## 快速开始

### 安装

```bash
npm install @baidu/doc-sdk
```

### 基本使用

```javascript
import DocSDK from '@baidu/doc-sdk';

const docSdk = new DocSDK({
  title: '我的组件库文档',
  theme: 'default',
  components: {
    'my-component': {
      label: '我的组件',
      demos: [
        {
          title: '基础用法',
          desc: '组件的基础使用方式',
          source: 'basic'
        }
      ],
      apis: [
        {
          title: 'MyComponent',
          apiKey: 'my-component'
        }
      ]
    }
  }
});

docSdk.render('#app');
```

## 架构设计

### 核心模块

- **DocRenderer** - 文档渲染器核心
- **ConfigManager** - 配置管理器
- **ComponentRegistry** - 组件注册表
- **ThemeManager** - 主题管理器
- **PluginManager** - 插件管理器
- **RouterManager** - 路由管理器

### 组件系统

- **Layout** - 布局组件
- **Navigation** - 导航组件
- **Demo** - 演示组件
- **ApiDoc** - API文档组件
- **CodeBlock** - 代码块组件
- **Search** - 搜索组件

## 目录结构

```
doc-sdk/
├── src/
│   ├── core/           # 核心模块
│   │   ├── DocRenderer.js      # 文档渲染器
│   │   ├── ConfigManager.js    # 配置管理器
│   │   ├── ComponentRegistry.js # 组件注册表
│   │   ├── ThemeManager.js     # 主题管理器
│   │   ├── PluginManager.js    # 插件管理器
│   │   └── RouterManager.js    # 路由管理器
│   ├── components/     # 文档组件
│   │   ├── Layout.jsx          # 主布局组件
│   │   ├── Navigation.jsx      # 导航组件
│   │   ├── Header.jsx          # 头部组件
│   │   ├── Content.jsx         # 内容组件
│   │   ├── Demo.jsx            # Demo展示组件
│   │   ├── ApiDoc.jsx          # API文档组件
│   │   ├── CodeBlock.jsx       # 代码块组件
│   │   ├── Search.jsx          # 搜索组件
│   │   ├── Home.jsx            # 首页组件
│   │   └── Footer.jsx          # 页脚组件
│   ├── themes/         # 主题文件
│   │   └── default.js          # 默认主题
│   ├── config/         # 配置文件
│   │   └── default.js          # 默认配置
│   ├── utils/          # 工具函数
│   │   └── index.js            # 工具函数集合
│   └── index.js        # 入口文件
├── bin/                # CLI工具
│   └── doc-sdk.js      # 命令行工具
├── tools/              # 构建工具
│   └── migrate.js      # 迁移工具
├── examples/           # 示例项目
│   ├── index.html      # 示例HTML
│   └── index.js        # 示例配置
├── lib/                # CommonJS构建输出
├── es/                 # ES模块构建输出
├── docs-new/           # 迁移示例
└── README.md           # 说明文档
```

## 🎯 设计理念

### 核心原则

1. **组件化架构** - 高度模块化，每个功能都是独立的组件
2. **配置驱动** - 通过配置文件控制所有行为，减少硬编码
3. **主题化设计** - 支持深度定制，满足不同项目需求
4. **插件扩展** - 提供插件系统，支持功能扩展
5. **开发友好** - 提供完整的开发工具链和迁移工具

### 技术栈

- **React** - 组件渲染
- **Webpack** - 模块打包
- **Babel** - 代码转换
- **Less/CSS** - 样式处理
- **Highlight.js** - 代码高亮
- **Mini-event** - 事件系统

## 🚀 快速开始

### 安装

```bash
npm install @baidu/doc-sdk
```

### 基本使用

```javascript
import DocSDK from '@baidu/doc-sdk';

const docSdk = new DocSDK({
  title: '我的组件库',
  components: {
    'button': {
      label: 'Button 按钮',
      demos: [
        {
          title: '基础用法',
          desc: '最简单的用法',
          source: 'basic'
        }
      ],
      apis: [
        {
          title: 'Button',
          apiKey: 'Button'
        }
      ]
    }
  }
});

docSdk.render('#app');
```

### CLI 工具

```bash
# 创建新项目
npx @baidu/doc-sdk create my-docs

# 启动开发服务器
npx @baidu/doc-sdk dev

# 构建项目
npx @baidu/doc-sdk build

# 迁移旧项目
npx @baidu/doc-sdk migrate --source ./doc --target ./docs-new
```

## 📚 详细文档

### 配置选项

```javascript
const config = {
  // 基本信息
  title: 'Documentation',
  description: 'Component Documentation Site',
  version: '1.0.0',

  // 主题配置
  theme: {
    name: 'default',
    colors: {
      primary: '#1890ff'
    }
  },

  // 布局配置
  layout: {
    type: 'sidebar', // sidebar, top, mixed
    sidebar: {
      width: 280,
      collapsible: true
    }
  },

  // 组件配置
  components: {
    'component-name': {
      label: '组件名称',
      description: '组件描述',
      demos: [...],
      apis: [...]
    }
  },

  // 搜索配置
  search: {
    enabled: true,
    placeholder: '搜索...'
  }
};
```

### 组件注册

```javascript
// 注册Demo组件
window.__DOC_SDK_DEMOS__ = {
  'component-name': {
    'demo-name': DemoComponent
  }
};

// 注册Demo代码
window.__DOC_SDK_DEMO_CODES__ = {
  'component-name': {
    'demo-name': 'const Demo = () => <div>Hello</div>;'
  }
};

// 注册API文档
window.__DOC_SDK_APIS__ = {
  'component-name': {
    'api-name': [
      {
        param: 'prop',
        type: 'string',
        desc: '属性描述',
        default: '',
        required: false
      }
    ]
  }
};
```

## 🔧 高级功能

### 自定义主题

```javascript
const customTheme = {
  name: 'custom',
  colors: {
    primary: '#ff6b6b',
    success: '#51cf66',
    warning: '#ffd43b',
    error: '#ff6b6b'
  },
  typography: {
    fontFamily: 'Inter, sans-serif'
  },
  components: {
    demo: `
      .doc-demo {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      }
    `
  }
};

const docSdk = new DocSDK({
  theme: customTheme
});
```

### 插件开发

```javascript
const myPlugin = {
  name: 'my-plugin',
  version: '1.0.0',
  install(context) {
    // 添加钩子
    context.hooks.add('beforeRender', () => {
      console.log('Before render');
    });

    // 监听事件
    context.events.on('routeChange', (route) => {
      console.log('Route changed:', route);
    });
  }
};

docSdk.use(myPlugin);
```

## 🛠️ 开发指南

### 本地开发

```bash
# 克隆项目
git clone https://github.com/your-org/doc-sdk.git
cd doc-sdk

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建项目
npm run build
```

### 项目结构说明

- `src/core/` - 核心功能模块
- `src/components/` - React组件
- `src/themes/` - 主题文件
- `src/utils/` - 工具函数
- `bin/` - CLI工具
- `tools/` - 构建和迁移工具
- `examples/` - 使用示例

## 🔄 迁移指南

### 从旧版doc目录迁移

1. **使用迁移工具**
```bash
npx @baidu/doc-sdk migrate --source ./doc --target ./docs-new
```

2. **手动调整**
- 检查组件导入路径
- 更新API文档格式
- 调整样式配置

3. **测试验证**
- 确保所有Demo正常显示
- 检查API文档完整性
- 验证搜索功能

### 主要变化

| 功能 | 旧版本 | 新版本 |
|------|--------|--------|
| 配置 | 分散在多个文件 | 统一配置对象 |
| 组件加载 | require.context | 全局变量注册 |
| 路由 | 自定义hash路由 | 标准化路由管理 |
| 主题 | CSS文件 | JavaScript主题对象 |
| 构建 | Webpack配置 | CLI工具 |

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

### 开发流程

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交 Pull Request

### 代码规范

- 使用 ESLint 进行代码检查
- 遵循 React Hooks 最佳实践
- 添加适当的注释和文档

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件
