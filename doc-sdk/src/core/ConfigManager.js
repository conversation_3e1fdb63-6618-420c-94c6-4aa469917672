/**
 * 配置管理器
 * 负责管理文档站点的所有配置
 */

import { merge, get, set, cloneDeep } from 'lodash';
import defaultConfig from '../config/default';

export default class ConfigManager {
  constructor(userConfig = {}) {
    this.config = this.mergeConfig(defaultConfig, userConfig);
    this.listeners = new Set();
  }

  /**
   * 合并配置
   * @param {Object} defaultConfig - 默认配置
   * @param {Object} userConfig - 用户配置
   * @returns {Object} 合并后的配置
   */
  mergeConfig(defaultConfig, userConfig) {
    return merge(cloneDeep(defaultConfig), userConfig);
  }

  /**
   * 获取完整配置
   * @returns {Object} 配置对象
   */
  getConfig() {
    return cloneDeep(this.config);
  }

  /**
   * 获取配置项
   * @param {string} path - 配置路径，支持点分隔符
   * @param {*} defaultValue - 默认值
   * @returns {*} 配置值
   */
  get(path, defaultValue) {
    return get(this.config, path, defaultValue);
  }

  /**
   * 设置配置项
   * @param {string} path - 配置路径
   * @param {*} value - 配置值
   */
  set(path, value) {
    const oldValue = this.get(path);
    set(this.config, path, value);
    
    this.notifyChange(path, value, oldValue);
  }

  /**
   * 更新配置
   * @param {Object} newConfig - 新配置
   */
  update(newConfig) {
    const oldConfig = cloneDeep(this.config);
    this.config = this.mergeConfig(this.config, newConfig);
    
    this.notifyChange('*', this.config, oldConfig);
  }

  /**
   * 重置配置
   * @param {Object} newConfig - 新配置
   */
  reset(newConfig = {}) {
    const oldConfig = cloneDeep(this.config);
    this.config = this.mergeConfig(defaultConfig, newConfig);
    
    this.notifyChange('*', this.config, oldConfig);
  }

  /**
   * 监听配置变化
   * @param {Function} listener - 监听函数
   */
  onChange(listener) {
    this.listeners.add(listener);
    
    // 返回取消监听的函数
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * 通知配置变化
   * @param {string} path - 变化的配置路径
   * @param {*} newValue - 新值
   * @param {*} oldValue - 旧值
   */
  notifyChange(path, newValue, oldValue) {
    this.listeners.forEach(listener => {
      try {
        listener({ path, newValue, oldValue, config: this.config });
      } catch (error) {
        console.error('Config change listener error:', error);
      }
    });
  }

  /**
   * 验证配置
   * @param {Object} config - 要验证的配置
   * @returns {Object} 验证结果
   */
  validate(config = this.config) {
    const errors = [];
    const warnings = [];

    // 验证必需字段
    if (!config.title) {
      warnings.push('Missing title in config');
    }

    if (!config.components || Object.keys(config.components).length === 0) {
      warnings.push('No components defined in config');
    }

    // 验证组件配置
    if (config.components) {
      Object.entries(config.components).forEach(([id, component]) => {
        if (!component.label) {
          warnings.push(`Component ${id} missing label`);
        }
        
        if (!component.demos || !Array.isArray(component.demos)) {
          warnings.push(`Component ${id} missing demos array`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 获取组件配置
   * @param {string} componentId - 组件ID
   * @returns {Object|null} 组件配置
   */
  getComponent(componentId) {
    return this.get(`components.${componentId}`, null);
  }

  /**
   * 获取所有组件配置
   * @returns {Object} 组件配置对象
   */
  getComponents() {
    return this.get('components', {});
  }

  /**
   * 获取主题配置
   * @returns {Object} 主题配置
   */
  getTheme() {
    return this.get('theme', {});
  }

  /**
   * 获取布局配置
   * @returns {Object} 布局配置
   */
  getLayout() {
    return this.get('layout', {});
  }

  /**
   * 获取插件配置
   * @returns {Array} 插件配置数组
   */
  getPlugins() {
    return this.get('plugins', []);
  }
}
