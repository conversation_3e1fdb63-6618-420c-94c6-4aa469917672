/**
 * 文档渲染SDK入口文件
 * <AUTHOR> SDK Team
 */

import DocRenderer from './core/DocRenderer';
import ConfigManager from './core/ConfigManager';
import ThemeManager from './core/ThemeManager';
import PluginManager from './core/PluginManager';

// 导出核心类
export { DocRenderer, ConfigManager, ThemeManager, PluginManager };

// 导出组件
export { default as Layout } from './components/Layout';
export { default as Navigation } from './components/Navigation';
export { default as Demo } from './components/Demo';
export { default as ApiDoc } from './components/ApiDoc';
export { default as CodeBlock } from './components/CodeBlock';

// 导出工具函数
export * from './utils';

// 默认导出主类
export default class DocSDK {
  constructor(config = {}) {
    this.configManager = new ConfigManager(config);
    this.themeManager = new ThemeManager(this.configManager);
    this.pluginManager = new PluginManager(this.configManager);
    this.renderer = new DocRenderer({
      configManager: this.configManager,
      themeManager: this.themeManager,
      pluginManager: this.pluginManager
    });
  }

  /**
   * 渲染文档到指定容器
   * @param {string|HTMLElement} target - 目标容器
   */
  render(target) {
    return this.renderer.render(target);
  }

  /**
   * 销毁文档实例
   */
  destroy() {
    return this.renderer.destroy();
  }

  /**
   * 更新配置
   * @param {Object} config - 新配置
   */
  updateConfig(config) {
    this.configManager.update(config);
    return this.renderer.update();
  }

  /**
   * 注册插件
   * @param {Object} plugin - 插件对象
   */
  use(plugin) {
    this.pluginManager.register(plugin);
    return this;
  }

  /**
   * 设置主题
   * @param {string|Object} theme - 主题名称或主题对象
   */
  setTheme(theme) {
    this.themeManager.setTheme(theme);
    return this.renderer.update();
  }

  /**
   * 获取当前配置
   */
  getConfig() {
    return this.configManager.getConfig();
  }

  /**
   * 监听事件
   * @param {string} event - 事件名称
   * @param {Function} handler - 事件处理函数
   */
  on(event, handler) {
    return this.renderer.on(event, handler);
  }

  /**
   * 移除事件监听
   * @param {string} event - 事件名称
   * @param {Function} handler - 事件处理函数
   */
  off(event, handler) {
    return this.renderer.off(event, handler);
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    return this.renderer.emit(event, data);
  }
}
