/**
 * 搜索组件
 */

import React, { useState, useEffect, useRef } from 'react';

const Search = ({ 
  config, 
  componentRegistry, 
  onClose, 
  onNavigate 
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef(null);
  const overlayRef = useRef(null);

  useEffect(() => {
    // 自动聚焦输入框
    if (inputRef.current) {
      inputRef.current.focus();
    }

    // 监听键盘事件
    const handleKeyDown = (e) => {
      if (e.key === 'Escape') {
        onClose();
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, 0));
      } else if (e.key === 'Enter') {
        e.preventDefault();
        if (results[selectedIndex]) {
          handleResultClick(results[selectedIndex]);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [results, selectedIndex, onClose]);

  useEffect(() => {
    // 搜索逻辑
    if (!query.trim()) {
      setResults([]);
      setSelectedIndex(0);
      return;
    }

    const searchResults = performSearch(query);
    setResults(searchResults);
    setSelectedIndex(0);
  }, [query, componentRegistry]);

  const performSearch = (searchQuery) => {
    const results = [];
    const lowerQuery = searchQuery.toLowerCase();
    
    // 搜索组件
    const componentIds = componentRegistry.getComponentIds();
    
    componentIds.forEach(componentId => {
      const componentConfig = componentRegistry.getComponent(componentId);
      const label = componentConfig?.label || componentId;
      const description = componentConfig?.description || '';
      
      // 匹配组件名称和描述
      if (
        label.toLowerCase().includes(lowerQuery) ||
        description.toLowerCase().includes(lowerQuery) ||
        componentId.toLowerCase().includes(lowerQuery)
      ) {
        results.push({
          type: 'component',
          id: componentId,
          title: label,
          description: description,
          path: `/${componentId}`,
          score: calculateScore(lowerQuery, label, description)
        });
      }

      // 搜索demos
      const demos = componentRegistry.getDemos(componentId);
      demos.forEach((demo, demoSource) => {
        const demoTitle = demo.title || demoSource;
        const demoDesc = demo.desc || '';
        
        if (
          demoTitle.toLowerCase().includes(lowerQuery) ||
          demoDesc.toLowerCase().includes(lowerQuery)
        ) {
          results.push({
            type: 'demo',
            id: `${componentId}-${demoSource}`,
            title: `${label} - ${demoTitle}`,
            description: demoDesc,
            path: `/${componentId}`,
            anchor: `demo-${demoSource}`,
            score: calculateScore(lowerQuery, demoTitle, demoDesc)
          });
        }
      });

      // 搜索APIs
      const apis = componentRegistry.getApis(componentId);
      apis.forEach((api, apiKey) => {
        const apiTitle = api.title || apiKey;
        
        if (apiTitle.toLowerCase().includes(lowerQuery)) {
          results.push({
            type: 'api',
            id: `${componentId}-${apiKey}`,
            title: `${label} - ${apiTitle}`,
            description: 'API文档',
            path: `/${componentId}`,
            anchor: `api-${apiKey}`,
            score: calculateScore(lowerQuery, apiTitle, '')
          });
        }
      });
    });

    // 按相关性排序
    return results.sort((a, b) => b.score - a.score).slice(0, 10);
  };

  const calculateScore = (query, title, description) => {
    let score = 0;
    const lowerTitle = title.toLowerCase();
    const lowerDesc = description.toLowerCase();
    
    // 完全匹配得分最高
    if (lowerTitle === query) score += 100;
    else if (lowerTitle.startsWith(query)) score += 50;
    else if (lowerTitle.includes(query)) score += 25;
    
    if (lowerDesc.includes(query)) score += 10;
    
    return score;
  };

  const handleResultClick = (result) => {
    if (onNavigate) {
      onNavigate(result.path);
      
      // 如果有锚点，滚动到对应位置
      if (result.anchor) {
        setTimeout(() => {
          const element = document.getElementById(result.anchor);
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
          }
        }, 100);
      }
    }
    
    onClose();
  };

  const handleOverlayClick = (e) => {
    if (e.target === overlayRef.current) {
      onClose();
    }
  };

  const getResultIcon = (type) => {
    switch (type) {
      case 'component':
        return '📦';
      case 'demo':
        return '🎯';
      case 'api':
        return '📋';
      default:
        return '📄';
    }
  };

  const highlightText = (text, query) => {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="doc-search-highlight">{part}</mark>
      ) : part
    );
  };

  return (
    <div 
      className="doc-search-overlay" 
      ref={overlayRef}
      onClick={handleOverlayClick}
    >
      <div className="doc-search-modal">
        <div className="doc-search-input-container">
          <input
            ref={inputRef}
            type="text"
            className="doc-search-input"
            placeholder={config.placeholder || '搜索文档...'}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
          
          <button 
            className="doc-search-close"
            onClick={onClose}
            title="关闭搜索"
          >
            ✕
          </button>
        </div>

        {results.length > 0 && (
          <div className="doc-search-results">
            {results.map((result, index) => (
              <div
                key={result.id}
                className={`doc-search-result ${
                  index === selectedIndex ? 'selected' : ''
                }`}
                onClick={() => handleResultClick(result)}
              >
                <div className="doc-search-result-icon">
                  {getResultIcon(result.type)}
                </div>
                
                <div className="doc-search-result-content">
                  <div className="doc-search-result-title">
                    {highlightText(result.title, query)}
                  </div>
                  
                  {result.description && (
                    <div className="doc-search-result-description">
                      {highlightText(result.description, query)}
                    </div>
                  )}
                </div>

                <div className="doc-search-result-type">
                  {result.type}
                </div>
              </div>
            ))}
          </div>
        )}

        {query && results.length === 0 && (
          <div className="doc-search-no-results">
            <p>未找到相关结果</p>
          </div>
        )}

        <div className="doc-search-footer">
          <div className="doc-search-shortcuts">
            <span><kbd>↑</kbd><kbd>↓</kbd> 导航</span>
            <span><kbd>Enter</kbd> 选择</span>
            <span><kbd>Esc</kbd> 关闭</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Search;
