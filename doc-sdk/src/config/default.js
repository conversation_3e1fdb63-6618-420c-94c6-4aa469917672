/**
 * 默认配置
 */

export default {
  // 站点基本信息
  title: 'Documentation',
  description: 'Component Documentation Site',
  version: '1.0.0',
  
  // 主题配置
  theme: {
    name: 'default',
    colors: {},
    typography: {},
    spacing: {},
    layout: {}
  },
  
  // 布局配置
  layout: {
    type: 'sidebar', // sidebar, top, mixed
    sidebar: {
      width: 280,
      collapsible: true,
      defaultCollapsed: false
    },
    header: {
      height: 64,
      showLogo: true,
      showTitle: true,
      showSearch: true
    },
    content: {
      maxWidth: 1200,
      padding: 24
    },
    footer: {
      show: true,
      height: 60
    }
  },
  
  // 组件配置
  components: {},
  
  // 导航配置
  navigation: {
    mode: 'auto', // auto, manual
    groups: [], // 手动配置的分组
    showHome: true,
    homeTitle: '首页'
  },
  
  // 搜索配置
  search: {
    enabled: true,
    placeholder: '搜索文档...',
    hotkey: 'ctrl+k'
  },
  
  // 代码高亮配置
  highlight: {
    theme: 'github',
    languages: ['javascript', 'jsx', 'typescript', 'tsx', 'css', 'less', 'scss', 'html', 'json']
  },
  
  // Demo配置
  demo: {
    showCode: true,
    codeCollapsed: true,
    showCopyButton: true,
    showExpandButton: true
  },
  
  // API文档配置
  api: {
    showRequired: true,
    showType: true,
    showDefault: true,
    showDescription: true
  },
  
  // 插件配置
  plugins: [],
  
  // 开发配置
  dev: {
    hot: true,
    port: 8080,
    host: 'localhost',
    open: true
  },
  
  // 构建配置
  build: {
    outDir: 'dist',
    publicPath: '/',
    sourcemap: false,
    minify: true
  },
  
  // 路由配置
  router: {
    mode: 'hash', // hash, history
    base: '/'
  },
  
  // 国际化配置
  i18n: {
    enabled: false,
    defaultLocale: 'zh-CN',
    locales: ['zh-CN', 'en-US']
  },
  
  // SEO配置
  seo: {
    keywords: [],
    author: '',
    favicon: ''
  },
  
  // 分析配置
  analytics: {
    enabled: false,
    providers: []
  }
};
