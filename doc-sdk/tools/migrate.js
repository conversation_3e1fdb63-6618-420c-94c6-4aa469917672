#!/usr/bin/env node

/**
 * 迁移工具 - 从原有doc目录迁移到新的Doc SDK
 */

const fs = require('fs');
const path = require('path');

class DocMigrator {
  constructor(options = {}) {
    this.sourceDir = options.sourceDir || './doc';
    this.targetDir = options.targetDir || './docs-new';
    this.config = {
      title: 'Documentation',
      description: 'Component Documentation',
      components: {}
    };
  }

  /**
   * 开始迁移
   */
  async migrate() {
    console.log('🚀 开始迁移文档...');
    
    try {
      // 检查源目录
      if (!fs.existsSync(this.sourceDir)) {
        throw new Error(`源目录不存在: ${this.sourceDir}`);
      }

      // 创建目标目录
      this.ensureDir(this.targetDir);

      // 分析原有配置
      await this.analyzeConfig();

      // 迁移组件
      await this.migrateComponents();

      // 生成新的配置文件
      await this.generateConfig();

      // 生成示例文件
      await this.generateExample();

      console.log('✅ 迁移完成!');
      console.log(`📁 新文档目录: ${this.targetDir}`);
      console.log('📖 请查看生成的 README.md 了解如何使用新的Doc SDK');

    } catch (error) {
      console.error('❌ 迁移失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 分析原有配置
   */
  async analyzeConfig() {
    console.log('📋 分析原有配置...');

    const configPath = path.join(this.sourceDir, 'src/config.js');
    if (fs.existsSync(configPath)) {
      try {
        // 读取配置文件内容
        const configContent = fs.readFileSync(configPath, 'utf8');
        
        // 简单解析配置（这里可以根据实际情况优化）
        const titleMatch = configContent.match(/title['":\s]*['"]([^'"]+)['"]/);
        if (titleMatch) {
          this.config.title = titleMatch[1];
        }

        console.log(`📝 发现配置: ${this.config.title}`);
      } catch (error) {
        console.warn('⚠️  配置文件解析失败，使用默认配置');
      }
    }
  }

  /**
   * 迁移组件
   */
  async migrateComponents() {
    console.log('📦 迁移组件...');

    const componentsDir = path.join(this.sourceDir, 'src/components');
    if (!fs.existsSync(componentsDir)) {
      console.warn('⚠️  未找到组件目录');
      return;
    }

    const componentDirs = fs.readdirSync(componentsDir)
      .filter(name => {
        const fullPath = path.join(componentsDir, name);
        return fs.statSync(fullPath).isDirectory() && name !== 'index';
      });

    for (const componentName of componentDirs) {
      await this.migrateComponent(componentName);
    }
  }

  /**
   * 迁移单个组件
   */
  async migrateComponent(componentName) {
    console.log(`  📄 迁移组件: ${componentName}`);

    const componentDir = path.join(this.sourceDir, 'src/components', componentName);
    const targetComponentDir = path.join(this.targetDir, 'components', componentName);

    this.ensureDir(targetComponentDir);

    // 分析组件配置
    const componentConfig = await this.analyzeComponentConfig(componentDir);
    this.config.components[componentName] = componentConfig;

    // 迁移demos
    await this.migrateDemos(componentDir, targetComponentDir, componentName);

    // 迁移API文档
    await this.migrateApis(componentDir, targetComponentDir, componentName);
  }

  /**
   * 分析组件配置
   */
  async analyzeComponentConfig(componentDir) {
    const indexPath = path.join(componentDir, 'index.js');
    const config = {
      label: '',
      description: '',
      demos: [],
      apis: []
    };

    if (fs.existsSync(indexPath)) {
      try {
        const content = fs.readFileSync(indexPath, 'utf8');
        
        // 解析label
        const labelMatch = content.match(/label['":\s]*['"]([^'"]+)['"]/);
        if (labelMatch) {
          config.label = labelMatch[1];
        }

        // 解析demos
        const demosMatch = content.match(/demos['":\s]*\[([\s\S]*?)\]/);
        if (demosMatch) {
          const demosContent = demosMatch[1];
          const demoMatches = demosContent.match(/\{[\s\S]*?\}/g) || [];
          
          demoMatches.forEach(demoStr => {
            const titleMatch = demoStr.match(/title['":\s]*['"]([^'"]+)['"]/);
            const descMatch = demoStr.match(/desc['":\s]*['"]([^'"]+)['"]/);
            const sourceMatch = demoStr.match(/source['":\s]*['"]([^'"]+)['"]/);
            
            if (titleMatch && sourceMatch) {
              config.demos.push({
                title: titleMatch[1],
                desc: descMatch ? descMatch[1] : '',
                source: sourceMatch[1]
              });
            }
          });
        }

        // 解析APIs
        const apisMatch = content.match(/apis['":\s]*\[([\s\S]*?)\]/);
        if (apisMatch) {
          const apisContent = apisMatch[1];
          const apiMatches = apisContent.match(/\{[\s\S]*?\}/g) || [];
          
          apiMatches.forEach(apiStr => {
            const titleMatch = apiStr.match(/title['":\s]*['"]([^'"]+)['"]/);
            const apiKeyMatch = apiStr.match(/apiKey['":\s]*['"]([^'"]+)['"]/);
            
            if (titleMatch && apiKeyMatch) {
              config.apis.push({
                title: titleMatch[1],
                apiKey: apiKeyMatch[1]
              });
            }
          });
        }

      } catch (error) {
        console.warn(`⚠️  组件配置解析失败: ${componentDir}`);
      }
    }

    return config;
  }

  /**
   * 迁移demos
   */
  async migrateDemos(componentDir, targetComponentDir, componentName) {
    const demoDir = path.join(componentDir, 'demo');
    if (!fs.existsSync(demoDir)) {
      return;
    }

    const targetDemoDir = path.join(targetComponentDir, 'demo');
    this.ensureDir(targetDemoDir);

    const demoFiles = fs.readdirSync(demoDir)
      .filter(file => file.endsWith('.js') || file.endsWith('.jsx'));

    for (const demoFile of demoFiles) {
      const sourcePath = path.join(demoDir, demoFile);
      const targetPath = path.join(targetDemoDir, demoFile);
      
      // 复制demo文件
      fs.copyFileSync(sourcePath, targetPath);
      
      console.log(`    📋 迁移demo: ${demoFile}`);
    }
  }

  /**
   * 迁移API文档
   */
  async migrateApis(componentDir, targetComponentDir, componentName) {
    const apiPath = path.join(componentDir, 'api.js');
    if (!fs.existsSync(apiPath)) {
      return;
    }

    const targetApiPath = path.join(targetComponentDir, 'api.js');
    fs.copyFileSync(apiPath, targetApiPath);
    
    console.log(`    📚 迁移API: api.js`);
  }

  /**
   * 生成新的配置文件
   */
  async generateConfig() {
    console.log('⚙️  生成配置文件...');

    const configContent = `/**
 * Doc SDK 配置文件
 * 从原有doc目录迁移生成
 */

export default ${JSON.stringify(this.config, null, 2)};
`;

    fs.writeFileSync(
      path.join(this.targetDir, 'config.js'),
      configContent
    );
  }

  /**
   * 生成示例文件
   */
  async generateExample() {
    console.log('📝 生成示例文件...');

    // 生成index.html
    const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${this.config.title}</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
</head>
<body>
  <div id="app"></div>
  <script src="./index.js"></script>
</body>
</html>`;

    fs.writeFileSync(path.join(this.targetDir, 'index.html'), htmlContent);

    // 生成index.js
    const jsContent = `/**
 * ${this.config.title} 文档站点
 * 使用 Doc SDK 构建
 */

import DocSDK from '@baidu/doc-sdk';
import config from './config.js';

// 注册组件demos和APIs
// TODO: 根据实际情况注册组件

const docSdk = new DocSDK(config);
docSdk.render('#app');
`;

    fs.writeFileSync(path.join(this.targetDir, 'index.js'), jsContent);

    // 生成README.md
    const readmeContent = `# ${this.config.title}

> 使用 Doc SDK 构建的文档站点

## 安装依赖

\`\`\`bash
npm install @baidu/doc-sdk
\`\`\`

## 开发

\`\`\`bash
npm run dev
\`\`\`

## 构建

\`\`\`bash
npm run build
\`\`\`

## 迁移说明

此文档站点从原有的doc目录迁移而来，主要变化：

1. 使用新的 Doc SDK 架构
2. 配置文件格式更新
3. 组件注册方式改变

## 下一步

1. 检查并完善 \`config.js\` 中的配置
2. 注册组件demos和APIs到全局变量
3. 根据需要自定义主题和样式
4. 测试所有功能是否正常

## 文档

- [Doc SDK 文档](https://github.com/your-org/doc-sdk)
- [迁移指南](https://github.com/your-org/doc-sdk/blob/main/MIGRATION.md)
`;

    fs.writeFileSync(path.join(this.targetDir, 'README.md'), readmeContent);

    // 生成package.json
    const packageContent = {
      name: `${this.config.title.toLowerCase().replace(/\s+/g, '-')}-docs`,
      version: '1.0.0',
      description: this.config.description,
      scripts: {
        dev: 'webpack serve --config webpack.dev.js',
        build: 'webpack --config webpack.prod.js'
      },
      dependencies: {
        '@baidu/doc-sdk': '^1.0.0'
      },
      devDependencies: {
        webpack: '^5.88.0',
        'webpack-cli': '^5.1.0',
        'webpack-dev-server': '^4.15.0'
      }
    };

    fs.writeFileSync(
      path.join(this.targetDir, 'package.json'),
      JSON.stringify(packageContent, null, 2)
    );
  }

  /**
   * 确保目录存在
   */
  ensureDir(dir) {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  }
}

// 命令行接口
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};

  // 解析命令行参数
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace(/^--/, '');
    const value = args[i + 1];
    options[key] = value;
  }

  const migrator = new DocMigrator(options);
  migrator.migrate();
}

module.exports = DocMigrator;
